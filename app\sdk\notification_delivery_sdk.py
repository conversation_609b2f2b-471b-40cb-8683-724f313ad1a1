"""
Notification Delivery SDK for <PERSON><PERSON>.

This module provides a unified interface for delivering notifications via Firebase Cloud Messaging.
Follows Darvis SDK adapter pattern for easy provider swapping.
"""

import os
import logging
from typing import Dict, Any, Optional
import firebase_admin
from firebase_admin import messaging

# Configure logging
logger = logging.getLogger(__name__)

class NotificationDeliveryError(Exception):
    """Custom exception for notification delivery errors."""
    pass

class NotificationDeliverySDK:
    """
    SDK for delivering notifications via Firebase Cloud Messaging.
    
    Follows Darvis SDK adapter pattern for easy provider swapping.
    Provides push notification capabilities with proper error handling,
    rate limiting, and response formatting.
    """
    
    def __init__(self):
        """Initialize the notification delivery SDK."""
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Firebase Admin SDK client."""
        try:
            # Use existing Firebase Admin SDK initialization
            if firebase_admin._apps:
                self.client = messaging
                logger.info("Notification delivery SDK initialized successfully")
            else:
                logger.warning("Firebase Admin SDK not initialized. Using mock notifications.")
                self.client = None
                
        except ImportError:
            logger.warning("firebase-admin not installed. Using mock notifications.")
            self.client = None
        except Exception as e:
            logger.error(f"Failed to initialize notification client: {e}")
            self.client = None
    
    def send_push_notification(
        self, 
        fcm_token: str, 
        title: str, 
        body: str, 
        data: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Send push notification via Firebase Cloud Messaging.
        
        Args:
            fcm_token: FCM registration token for the target device
            title: Notification title
            body: Notification body text
            data: Optional data payload for the notification
            
        Returns:
            Dictionary containing:
            - success: Boolean indicating if notification was sent successfully
            - message_id: FCM message ID (if successful)
            - error: Error message (if failed)
            - mock: Boolean indicating if this was a mock response
            
        Raises:
            NotificationDeliveryError: If the notification delivery fails
        """
        if not fcm_token:
            logger.warning("No FCM token provided for notification")
            return {"success": False, "error": "No FCM token provided"}
        
        if not self.client:
            logger.info(f"Mock notification: {title} - {body}")
            return {
                "success": True, 
                "mock": True,
                "message_id": "mock_message_id",
                "title": title,
                "body": body
            }
        
        try:
            # Create FCM message
            message = messaging.Message(
                notification=messaging.Notification(
                    title=title,
                    body=body
                ),
                data=data or {},
                token=fcm_token
            )
            
            # Send the message
            response = messaging.send(message)
            logger.info(f"Notification sent successfully: {response}")
            
            return {
                "success": True,
                "message_id": response,
                "title": title,
                "body": body
            }
            
        except messaging.UnregisteredError:
            logger.warning(f"FCM token is invalid or unregistered: {fcm_token}")
            return {
                "success": False,
                "error": "Invalid or unregistered FCM token"
            }
        except messaging.SenderIdMismatchError:
            logger.error(f"FCM sender ID mismatch for token: {fcm_token}")
            return {
                "success": False,
                "error": "FCM sender ID mismatch"
            }
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def send_batch_notifications(
        self, 
        notifications: list[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Send multiple notifications in batch for efficiency.
        
        Args:
            notifications: List of notification dictionaries, each containing:
                - fcm_token: FCM registration token
                - title: Notification title
                - body: Notification body
                - data: Optional data payload
                
        Returns:
            Dictionary containing:
            - success_count: Number of successfully sent notifications
            - failure_count: Number of failed notifications
            - responses: List of individual response dictionaries
        """
        if not notifications:
            return {"success_count": 0, "failure_count": 0, "responses": []}
        
        responses = []
        success_count = 0
        failure_count = 0
        
        for notification in notifications:
            response = self.send_push_notification(
                fcm_token=notification.get("fcm_token"),
                title=notification.get("title"),
                body=notification.get("body"),
                data=notification.get("data")
            )
            
            responses.append(response)
            
            if response.get("success"):
                success_count += 1
            else:
                failure_count += 1
        
        logger.info(f"Batch notification complete: {success_count} success, {failure_count} failures")
        
        return {
            "success_count": success_count,
            "failure_count": failure_count,
            "responses": responses
        }
    
    def validate_fcm_token(self, fcm_token: str) -> bool:
        """
        Validate if an FCM token is properly formatted.
        
        Args:
            fcm_token: FCM registration token to validate
            
        Returns:
            Boolean indicating if the token appears valid
        """
        if not fcm_token or not isinstance(fcm_token, str):
            return False
        
        # Basic validation - FCM tokens are typically 152+ characters
        if len(fcm_token) < 100:
            return False
        
        # FCM tokens should not contain spaces or special characters
        if " " in fcm_token or "\n" in fcm_token:
            return False
        
        return True

# Create a global instance for easy importing
notification_delivery_sdk = NotificationDeliverySDK()

# Convenience function for direct usage
def send_notification(fcm_token: str, title: str, body: str, data: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """
    Convenience function to send a single notification.
    
    Args:
        fcm_token: FCM registration token
        title: Notification title
        body: Notification body
        data: Optional data payload
        
    Returns:
        Response dictionary from send_push_notification
    """
    return notification_delivery_sdk.send_push_notification(fcm_token, title, body, data)
