import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add the darvis-ai-core directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "darvis-ai-core"))

from app.db.models import Conversation, Message, ActionLog
from app.db import crud
from app.sdk.embedding_sdk import EmbeddingSDK

class TestMemoryModels:
    """Test cases for the new memory models."""
    
    def test_conversation_model_creation(self):
        """Test conversation model structure."""
        # This would require a database session in a real test
        # For now, just test the model structure
        assert hasattr(Conversation, 'id')
        assert hasattr(Conversation, 'summary')
        assert hasattr(Conversation, 'owner_id')
        assert hasattr(Conversation, 'messages')
        assert hasattr(Conversation, 'action_logs')
    
    def test_message_model_creation(self):
        """Test message model structure."""
        assert hasattr(Message, 'id')
        assert hasattr(Message, 'role')
        assert hasattr(Message, 'content')
        assert hasattr(Message, 'embedding')
        assert hasattr(Message, 'conversation_id')
    
    def test_action_log_model_creation(self):
        """Test action log model structure."""
        assert hasattr(ActionLog, 'id')
        assert hasattr(ActionLog, 'tool_name')
        assert hasattr(ActionLog, 'input_params')
        assert hasattr(ActionLog, 'output_summary')
        assert hasattr(ActionLog, 'conversation_id')

class TestEmbeddingSDK:
    """Test cases for the embedding service."""
    
    def test_embedding_sdk_initialization(self):
        """Test that embedding SDK initializes correctly."""
        embedding_sdk = EmbeddingSDK()
        assert embedding_sdk.model_name == "all-MiniLM-L6-v2"
    
    def test_mock_embedding_generation(self):
        """Test mock embedding generation."""
        embedding_sdk = EmbeddingSDK()
        # Force mock mode
        embedding_sdk.model = None
        
        embedding = embedding_sdk.generate_embedding("Hello, world!")
        
        assert isinstance(embedding, list)
        assert len(embedding) == 384
        assert all(isinstance(x, float) for x in embedding)
    
    def test_zero_embedding_fallback(self):
        """Test zero embedding fallback."""
        embedding_sdk = EmbeddingSDK()
        
        zero_embedding = embedding_sdk._get_zero_embedding()
        
        assert len(zero_embedding) == 384
        assert all(x == 0.0 for x in zero_embedding)
    
    def test_similarity_calculation(self):
        """Test cosine similarity calculation."""
        embedding_sdk = EmbeddingSDK()
        
        # Test identical vectors
        vec1 = [1.0, 0.0, 0.0]
        vec2 = [1.0, 0.0, 0.0]
        similarity = embedding_sdk.calculate_similarity(vec1, vec2)
        assert abs(similarity - 1.0) < 0.001
        
        # Test orthogonal vectors
        vec3 = [1.0, 0.0, 0.0]
        vec4 = [0.0, 1.0, 0.0]
        similarity = embedding_sdk.calculate_similarity(vec3, vec4)
        assert abs(similarity - 0.0) < 0.001

class TestMemoryIntegration:
    """Test cases for memory integration."""
    
    @patch('darvis-ai-core.main_orchestrator.crud')
    @patch('darvis-ai-core.main_orchestrator.embedding_service')
    @patch('darvis-ai-core.main_orchestrator.get_db')
    def test_memory_retrieval_node(self, mock_get_db, mock_embedding_service, mock_crud):
        """Test memory retrieval node functionality."""
        from main_orchestrator import DarvisOrchestrator
        from langchain_core.messages import HumanMessage
        
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock embedding service
        mock_embedding_service.generate_embedding.return_value = [0.1] * 384
        
        # Mock similar messages
        mock_message = MagicMock()
        mock_message.content = "Previous conversation about weather"
        mock_message.role = "human"
        mock_message.conversation_id = "prev_conv_123"
        mock_message.created_at.isoformat.return_value = "2025-01-23T10:00:00"
        
        mock_crud.find_similar_messages.return_value = [mock_message]
        
        orchestrator = DarvisOrchestrator()
        
        state = {
            "user_id": "test_user",
            "conversation_id": "current_conv",
            "messages": [HumanMessage(content="What's the weather like?")]
        }
        
        result = orchestrator._memory_retrieval_node(state)
        
        assert "relevant_memories" in result
        assert len(result["relevant_memories"]) == 1
        assert result["relevant_memories"][0]["content"] == "Previous conversation about weather"
    
    def test_conversation_state_structure(self):
        """Test conversation state type definition."""
        from main_orchestrator import ConversationState
        
        # Test that ConversationState has all required fields
        required_fields = ["messages", "user_id", "conversation_id", "conversation_context", "relevant_memories"]
        
        # This is a TypedDict, so we can't instantiate it directly in tests
        # But we can verify the structure exists
        assert hasattr(ConversationState, '__annotations__')

class TestChatAPIMemory:
    """Test cases for memory-enhanced chat API."""
    
    def test_chat_message_with_conversation_id(self):
        """Test chat message model with conversation ID."""
        from app.api.chat import ChatMessage
        
        message = ChatMessage(
            message="Hello, remember our previous conversation?",
            conversation_id="conv_123",
            context={"topic": "weather"}
        )
        
        assert message.message == "Hello, remember our previous conversation?"
        assert message.conversation_id == "conv_123"
        assert message.context["topic"] == "weather"
    
    def test_chat_response_with_memory_metadata(self):
        """Test chat response model with memory metadata."""
        from app.api.chat import ChatResponse
        
        response = ChatResponse(
            response="Based on our previous discussion about weather...",
            conversation_id="conv_123",
            user_id="user_456",
            relevant_memories_count=3,
            success=True
        )
        
        assert response.conversation_id == "conv_123"
        assert response.relevant_memories_count == 3
        assert response.success is True

if __name__ == "__main__":
    pytest.main([__file__])
