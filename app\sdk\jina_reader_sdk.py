"""
Jina AI Reader SDK for content extraction from URLs.

This module provides a simple interface to the Jina AI Reader API
for extracting clean, LLM-friendly content from web pages.
"""

import httpx
import logging
from typing import Dict, Any, Optional
import os

# Configure logging
logger = logging.getLogger(__name__)

class JinaReaderError(Exception):
    """Custom exception for Jina Reader API errors."""
    pass

class JinaReaderSDK:
    """
    SDK for interacting with Jina AI Reader API.
    
    The Jina AI Reader API converts any URL to LLM-friendly content
    by prefixing URLs with https://r.jina.ai/
    """
    
    def __init__(self):
        """Initialize the Jina Reader SDK."""
        self.base_url = "https://r.jina.ai"
        self.timeout = 30  # Default timeout in seconds
        
    def get_content_from_url(self, url: str, timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Extract clean content from a URL using Jina AI Reader.
        
        Args:
            url: The URL to extract content from
            timeout: Optional timeout in seconds (default: 30)
            
        Returns:
            Dictionary containing:
            - success: Boolean indicating if extraction was successful
            - content: Clean text content (if successful)
            - error: Error message (if failed)
            - url: Original URL
            
        Raises:
            JinaReaderError: If the API request fails
        """
        if not url:
            return {
                "success": False,
                "content": None,
                "error": "URL cannot be empty",
                "url": url
            }
            
        # Clean and validate URL
        url = url.strip()
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            
        try:
            # Use the Jina Reader API format: https://r.jina.ai/{url}
            jina_url = f"{self.base_url}/{url}"
            
            # Set timeout
            request_timeout = timeout or self.timeout
            
            logger.info(f"Extracting content from URL: {url}")
            
            with httpx.Client(timeout=request_timeout) as client:
                response = client.get(jina_url)
                response.raise_for_status()
                
                # Get the clean content
                clean_content = response.text
                
                if not clean_content or len(clean_content.strip()) < 10:
                    return {
                        "success": False,
                        "content": None,
                        "error": "No meaningful content extracted from URL",
                        "url": url
                    }
                
                logger.info(f"Successfully extracted {len(clean_content)} characters from {url}")
                
                return {
                    "success": True,
                    "content": clean_content.strip(),
                    "error": None,
                    "url": url
                }
                
        except httpx.TimeoutException:
            error_msg = f"Timeout while extracting content from {url}"
            logger.error(error_msg)
            return {
                "success": False,
                "content": None,
                "error": error_msg,
                "url": url
            }
            
        except httpx.HTTPStatusError as e:
            error_msg = f"HTTP error {e.response.status_code} while extracting content from {url}"
            logger.error(error_msg)
            return {
                "success": False,
                "content": None,
                "error": error_msg,
                "url": url
            }
            
        except Exception as e:
            error_msg = f"Unexpected error while extracting content from {url}: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "content": None,
                "error": error_msg,
                "url": url
            }

    def get_content_with_options(self, url: str, wait_for_selector: Optional[str] = None, 
                                timeout_seconds: Optional[int] = None) -> Dict[str, Any]:
        """
        Extract content with advanced options for SPAs and dynamic content.
        
        Args:
            url: The URL to extract content from
            wait_for_selector: CSS selector to wait for before extracting content
            timeout_seconds: How long to wait for dynamic content to load
            
        Returns:
            Dictionary with extraction results
        """
        if not url:
            return {
                "success": False,
                "content": None,
                "error": "URL cannot be empty",
                "url": url
            }
            
        # Clean and validate URL
        url = url.strip()
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            
        try:
            headers = {}
            
            # Add special headers for dynamic content
            if wait_for_selector:
                headers['x-wait-for-selector'] = wait_for_selector
                
            if timeout_seconds:
                headers['x-timeout'] = str(timeout_seconds)
            
            logger.info(f"Extracting content from URL with options: {url}")
            
            with httpx.Client(timeout=self.timeout) as client:
                response = client.get(f"{self.base_url}/{url}", headers=headers)
                response.raise_for_status()
                
                clean_content = response.text
                
                if not clean_content or len(clean_content.strip()) < 10:
                    return {
                        "success": False,
                        "content": None,
                        "error": "No meaningful content extracted from URL",
                        "url": url
                    }
                
                logger.info(f"Successfully extracted {len(clean_content)} characters from {url}")
                
                return {
                    "success": True,
                    "content": clean_content.strip(),
                    "error": None,
                    "url": url
                }
                
        except Exception as e:
            error_msg = f"Error extracting content with options from {url}: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "content": None,
                "error": error_msg,
                "url": url
            }

# Create a global instance for easy importing
jina_reader = JinaReaderSDK()

# Convenience function for direct usage
def get_content_from_url(url: str, timeout: Optional[int] = None) -> Dict[str, Any]:
    """
    Convenience function to extract content from a URL.
    
    Args:
        url: The URL to extract content from
        timeout: Optional timeout in seconds
        
    Returns:
        Dictionary with extraction results
    """
    return jina_reader.get_content_from_url(url, timeout)
