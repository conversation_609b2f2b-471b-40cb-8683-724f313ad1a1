import os
from typing import List, Optional
import logging
import numpy as np

# Configure logging
logger = logging.getLogger(__name__)

class EmbeddingSDK:
    """
    SDK for generating text embeddings using Voyage AI.

    This service generates 1024-dimensional embeddings using voyage-3-large
    for state-of-the-art semantic search and similarity matching.
    """

    def __init__(self, model_name: str = "voyage-3-large"):
        """
        Initialize the embedding service.

        Args:
            model_name: Name of the Voyage AI model to use (default: voyage-3-large)
        """
        self.model_name = model_name
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize the Voyage AI client."""
        try:
            import voyageai

            api_key = os.getenv("VOYAGE_API_KEY")
            if not api_key:
                logger.warning("VOYAGE_API_KEY not found. Using mock embeddings.")
                self.client = None
                return

            self.client = voyageai.Client(api_key=api_key)
            logger.info(f"Voyage AI client initialized successfully with model {self.model_name}")

        except ImportError:
            logger.warning("voyageai not installed. Using mock embeddings.")
            self.client = None
        except Exception as e:
            logger.error(f"Failed to initialize Voyage AI client: {e}")
            self.client = None
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a single text using Voyage AI.

        Args:
            text: Input text to embed

        Returns:
            List of 1024 float values representing the embedding
        """
        if not text or not text.strip():
            logger.warning("Empty text provided for embedding")
            return self._get_zero_embedding()

        if not self.client:
            # Return mock embedding for development
            return self._get_mock_embedding(text)

        try:
            # Generate embedding using Voyage AI
            result = self.client.embed(
                texts=[text.strip()],
                model=self.model_name,
                input_type="document"  # Use document type for general text
            )

            # Extract the embedding from the result
            embedding_list = result.embeddings[0]

            # Voyage AI voyage-3-large returns 1024-dimensional embeddings by default
            if len(embedding_list) != 1024:
                logger.warning(f"Unexpected embedding dimension: {len(embedding_list)}")
                # Pad or truncate to 1024 dimensions
                if len(embedding_list) < 1024:
                    embedding_list.extend([0.0] * (1024 - len(embedding_list)))
                else:
                    embedding_list = embedding_list[:1024]

            logger.debug(f"Generated Voyage AI embedding for text: {text[:50]}...")
            return embedding_list

        except Exception as e:
            logger.error(f"Error generating Voyage AI embedding: {e}")
            return self._get_zero_embedding()
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts using Voyage AI.

        Args:
            texts: List of input texts to embed

        Returns:
            List of embeddings, each with 1024 dimensions
        """
        if not texts:
            return []

        if not self.client:
            # Return mock embeddings for development
            return [self._get_mock_embedding(text) for text in texts]

        try:
            # Generate embeddings using Voyage AI
            result = self.client.embed(
                texts=texts,
                model=self.model_name,
                input_type="document"  # Use document type for general text
            )

            # Extract embeddings from the result
            embeddings_list = []
            for embedding in result.embeddings:
                # Ensure 1024 dimensions
                if len(embedding) != 1024:
                    if len(embedding) < 1024:
                        embedding.extend([0.0] * (1024 - len(embedding)))
                    else:
                        embedding = embedding[:1024]

                embeddings_list.append(embedding)

            logger.debug(f"Generated {len(embeddings_list)} Voyage AI embeddings")
            return embeddings_list

        except Exception as e:
            logger.error(f"Error generating Voyage AI embeddings: {e}")
            return [self._get_zero_embedding() for _ in texts]
    
    def _get_mock_embedding(self, text: str) -> List[float]:
        """
        Generate a mock embedding based on text hash for development.

        Args:
            text: Input text

        Returns:
            Mock 1024-dimensional embedding
        """
        # Use text hash to generate deterministic mock embedding
        text_hash = hash(text.strip().lower())
        np.random.seed(abs(text_hash) % (2**32))

        # Generate random embedding with some structure
        embedding = np.random.normal(0, 0.1, 1024).tolist()

        logger.debug(f"Generated mock embedding for: {text[:50]}...")
        return embedding

    def _get_zero_embedding(self) -> List[float]:
        """
        Get a zero embedding as fallback.

        Returns:
            1024-dimensional zero vector
        """
        return [0.0] * 1024
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Cosine similarity score between -1 and 1
        """
        try:
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0

# Global embedding service instance
embedding_service = EmbeddingSDK()
