from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import uuid
import os
from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import AttachmentResponse, AttachmentListResponse
from app.core.config import settings
import cloudinary
import cloudinary.uploader

router = APIRouter(prefix="/api/v1/attachments", tags=["Attachments"])

# Configure Cloudinary
cloudinary.config(
    cloud_name=settings.cloudinary_cloud_name,
    api_key=settings.cloudinary_api_key,
    api_secret=settings.cloudinary_api_secret
)

@router.post("/upload", response_model=AttachmentResponse, status_code=status.HTTP_201_CREATED)
async def upload_attachment(
    file: UploadFile = File(...),
    entity_type: str = Form(..., description="Type of entity: note, task, or calendar_event"),
    entity_id: str = Form(..., description="ID of the entity to attach file to"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Upload a file attachment for a note, task, or calendar event.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Form Data**:
    - **file**: File to upload (multipart/form-data) - **Required**
    - **entity_type**: Type of entity (note, task, calendar_event) - **Required**
    - **entity_id**: ID of the entity to attach file to - **Required**
    
    **Returns**:
    - **201 Created**: Attachment successfully uploaded
    - **400 Bad Request**: Invalid file or entity data
    - **404 Not Found**: Entity not found
    - **401 Unauthorized**: Invalid or expired authentication token
    
    **Supported File Types**: Images, documents, audio, video (max 10MB)
    """
    user_id = current_user["uid"]
    
    # Validate entity type
    if entity_type not in ["note", "task", "calendar_event"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid entity type. Must be 'note', 'task', or 'calendar_event'"
        )
    
    # Verify entity exists and belongs to user
    if entity_type == "note":
        entity = crud.get_note_by_id(db=db, note_id=entity_id, user_id=user_id)
    elif entity_type == "task":
        entity = crud.get_task_by_id(db=db, task_id=entity_id, user_id=user_id)
    elif entity_type == "calendar_event":
        entity = crud.get_calendar_event_by_id(db=db, event_id=entity_id, user_id=user_id)
    
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{entity_type.replace('_', ' ').title()} not found"
        )
    
    # Validate file size (10MB limit)
    if file.size and file.size > 10 * 1024 * 1024:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size exceeds 10MB limit"
        )
    
    # Validate file type
    allowed_types = [
        "image/jpeg", "image/png", "image/gif", "image/webp",
        "application/pdf", "text/plain", "text/csv",
        "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "audio/mpeg", "audio/wav", "audio/ogg",
        "video/mp4", "video/avi", "video/mov"
    ]
    
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type {file.content_type} not supported"
        )
    
    try:
        # Generate unique filename
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        
        # Upload to Cloudinary
        upload_result = cloudinary.uploader.upload(
            file.file,
            public_id=f"attachments/{user_id}/{unique_filename}",
            resource_type="auto",  # Auto-detect resource type
            folder=f"darvis/attachments/{entity_type}"
        )
        
        # Create attachment record
        attachment = crud.create_attachment(
            db=db,
            user_id=user_id,
            filename=unique_filename,
            original_filename=file.filename,
            file_size=file.size or 0,
            mime_type=file.content_type,
            storage_url=upload_result["secure_url"],
            storage_public_id=upload_result["public_id"],
            storage_provider="cloudinary",
            note_id=entity_id if entity_type == "note" else None,
            task_id=entity_id if entity_type == "task" else None,
            calendar_event_id=entity_id if entity_type == "calendar_event" else None
        )
        
        return attachment
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload attachment: {str(e)}"
        )

@router.get("/{entity_type}/{entity_id}", response_model=AttachmentListResponse)
def get_attachments_for_entity(
    entity_type: str,
    entity_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all attachments for a specific entity (note, task, or calendar event).
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Path Parameters**:
    - **entity_type**: Type of entity (note, task, calendar_event)
    - **entity_id**: ID of the entity
    
    **Returns**:
    - **200 OK**: List of attachments for the entity
    - **400 Bad Request**: Invalid entity type
    - **404 Not Found**: Entity not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    # Validate entity type
    if entity_type not in ["note", "task", "calendar_event"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid entity type. Must be 'note', 'task', or 'calendar_event'"
        )
    
    # Verify entity exists and belongs to user
    if entity_type == "note":
        entity = crud.get_note_by_id(db=db, note_id=entity_id, user_id=user_id)
    elif entity_type == "task":
        entity = crud.get_task_by_id(db=db, task_id=entity_id, user_id=user_id)
    elif entity_type == "calendar_event":
        entity = crud.get_calendar_event_by_id(db=db, event_id=entity_id, user_id=user_id)
    
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{entity_type.replace('_', ' ').title()} not found"
        )
    
    try:
        attachments = crud.get_attachments_for_entity(
            db=db,
            user_id=user_id,
            entity_type=entity_type,
            entity_id=entity_id
        )
        
        return AttachmentListResponse(attachments=attachments, total=len(attachments))
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve attachments: {str(e)}"
        )

@router.delete("/{attachment_id}")
def delete_attachment(
    attachment_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete an attachment.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Path Parameters**:
    - **attachment_id**: ID of the attachment to delete
    
    **Returns**:
    - **204 No Content**: Attachment successfully deleted
    - **404 Not Found**: Attachment not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    # Get attachment to get storage info before deletion
    attachment = db.query(crud.Attachment).filter(
        crud.and_(crud.Attachment.id == attachment_id, crud.Attachment.owner_id == user_id)
    ).first()
    
    if not attachment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Attachment not found"
        )
    
    try:
        # Delete from Cloudinary if it exists there
        if attachment.storage_provider == "cloudinary" and attachment.storage_public_id:
            cloudinary.uploader.destroy(attachment.storage_public_id)
        
        # Delete from database
        success = crud.delete_attachment(db=db, attachment_id=attachment_id, user_id=user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Attachment not found"
            )
        
        return {"message": "Attachment deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete attachment: {str(e)}"
        )

@router.get("/user/all", response_model=AttachmentListResponse)
def get_user_attachments(
    skip: int = 0,
    limit: int = 100,
    entity_type: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all attachments for the authenticated user.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Query Parameters**:
    - **skip**: Number of attachments to skip (default: 0)
    - **limit**: Maximum number of attachments to return (default: 100)
    - **entity_type**: Filter by entity type (note, task, calendar_event)
    
    **Returns**:
    - **200 OK**: List of user's attachments
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    try:
        query = db.query(crud.Attachment).filter(crud.Attachment.owner_id == user_id)
        
        if entity_type:
            if entity_type == "note":
                query = query.filter(crud.Attachment.note_id.isnot(None))
            elif entity_type == "task":
                query = query.filter(crud.Attachment.task_id.isnot(None))
            elif entity_type == "calendar_event":
                query = query.filter(crud.Attachment.calendar_event_id.isnot(None))
        
        total = query.count()
        attachments = query.order_by(crud.desc(crud.Attachment.created_at)).offset(skip).limit(limit).all()
        
        return AttachmentListResponse(attachments=attachments, total=total)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve user attachments: {str(e)}"
        )
