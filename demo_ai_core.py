#!/usr/bin/env python3
"""
Demonstration script for the Darvis AI Core functionality.

This script shows how the PRP system, LangGraph orchestrator, and LLM provider
work together to create an intelligent conversational agent.
"""

import sys
import os

# Add the darvis-ai-core directory to the path
sys.path.append("darvis-ai-core")
sys.path.append("app")

def demo_prp_manager():
    """Demonstrate the PRP Manager functionality."""
    print("🔧 PRP Manager Demo")
    print("=" * 50)
    
    try:
        from prp_manager import prp_manager
        
        # Load the initial persona PRP
        persona = prp_manager.get_system_prp("_initial_persona.md")
        
        if persona:
            print(f"✅ Loaded PRP: {persona['title']}")
            print(f"📋 Rules ({len(persona['rules'])} total):")
            for i, rule in enumerate(persona['rules'], 1):
                print(f"   {i}. {rule}")
            print(f"🎯 Trigger: {persona['trigger']}")
            print(f"📊 Evaluation: {persona['evaluation']}")
        else:
            print("❌ Failed to load PRP")
            
    except Exception as e:
        print(f"❌ Error in PRP Manager demo: {e}")
    
    print()

def demo_llm_provider():
    """Demonstrate the LLM Provider SDK."""
    print("🤖 LLM Provider SDK Demo")
    print("=" * 50)
    
    try:
        from sdk.llm_provider_sdk import LLMProviderSDK
        from langchain_core.messages import HumanMessage, SystemMessage
        
        # Initialize LLM provider (will use mock if no API key)
        llm = LLMProviderSDK(provider="groq", model="llama3-8b-8192")
        
        print(f"✅ Initialized LLM Provider: {llm.provider}")
        print(f"📱 Model: {llm.model}")
        print(f"🔗 Client Available: {llm.client is not None}")
        
        # Test message creation
        messages = llm.create_messages(
            system_prompt="You are a helpful assistant.",
            user_message="Hello, how are you?",
            conversation_history=[
                {"role": "user", "content": "Hi there"},
                {"role": "assistant", "content": "Hello! Nice to meet you."}
            ]
        )
        
        print(f"📝 Created {len(messages)} messages for LLM")
        
        # Test invocation (will return mock response if no API key)
        response = llm.invoke(messages)
        print(f"💬 LLM Response: {response.content[:100]}...")
        
    except Exception as e:
        print(f"❌ Error in LLM Provider demo: {e}")
    
    print()

def demo_tool_integration():
    """Demonstrate tool integration capabilities."""
    print("🔧 Tool Integration Demo")
    print("=" * 50)

    try:
        from sdk.composio_tools_sdk import ComposioToolsSDK

        tools_sdk = ComposioToolsSDK()

        print(f"🔗 Composio client available: {tools_sdk.client is not None}")
        print(f"🔑 API Key configured: {'Yes' if tools_sdk.client else 'No (using mock tools)'}")

        # Test calendar event creation
        print("\n📅 Testing calendar event creation...")

        result = tools_sdk.create_calendar_event(
            user_id="demo_user",
            summary="Demo Team Meeting",
            start_time="2025-01-24T15:00:00Z",
            end_time="2025-01-24T16:00:00Z",
            description="Weekly team sync meeting for demo purposes",
            location="Conference Room A"
        )

        if result["success"]:
            print(f"✅ Event created successfully!")
            print(f"   📝 Title: {result['summary']}")
            print(f"   🕐 Time: {result['start_time']} - {result['end_time']}")
            if result.get("event_link"):
                print(f"   🔗 Link: {result['event_link']}")
            if result.get("mock"):
                print(f"   🎭 Mode: Mock (no real calendar integration)")
        else:
            print(f"❌ Event creation failed: {result.get('error', 'Unknown error')}")

        # Test tool detection
        print("\n🔍 Testing tool intent detection...")

        from main_orchestrator import DarvisOrchestrator
        orchestrator = DarvisOrchestrator()

        test_messages = [
            "Schedule a meeting tomorrow at 3pm",
            "Hello, how are you?",
            "Book an appointment for next week",
            "What's the weather like?"
        ]

        for message in test_messages:
            intent = orchestrator._detect_tool_intent(message)
            has_intent = intent is not None
            print(f"   📝 '{message[:30]}...' → {'🔧 Tool needed' if has_intent else '💬 Chat only'}")

        # Test tool call extraction
        print("\n🎯 Testing tool call extraction...")

        sample_ai_response = """I'll schedule that meeting for you.

TOOL_CALL: create_calendar_event
PARAMETERS: {"summary": "Team Meeting", "start_time": "2025-01-24T15:00:00Z", "end_time": "2025-01-24T16:00:00Z"}
REASONING: User requested to schedule a team meeting

The meeting has been scheduled successfully."""

        tool_calls = orchestrator._extract_tool_calls(sample_ai_response)

        print(f"   📊 Extracted {len(tool_calls)} tool calls")
        for i, call in enumerate(tool_calls, 1):
            print(f"   {i}. Tool: {call['tool_name']}")
            print(f"      Parameters: {call['parameters']}")
            print(f"      Reasoning: {call['reasoning']}")

    except Exception as e:
        print(f"❌ Error in tool integration demo: {e}")

    print()

def demo_orchestrator():
    """Demonstrate the LangGraph Orchestrator."""
    print("🎭 LangGraph Orchestrator Demo")
    print("=" * 50)
    
    try:
        from main_orchestrator import DarvisOrchestrator
        
        # Initialize orchestrator
        orchestrator = DarvisOrchestrator()
        
        print(f"✅ Orchestrator initialized")
        print(f"📊 Graph available: {orchestrator.graph is not None}")
        
        # Test message processing
        test_messages = [
            "Hello, who are you?",
            "What can you help me with?",
            "Tell me about your capabilities."
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n🗣️  User Message {i}: {message}")
            
            response = orchestrator.process_message(
                user_message=message,
                user_id=f"demo_user_{i}",
                conversation_context={"demo": True}
            )
            
            print(f"🤖 Darvis Response: {response[:150]}...")
        
    except Exception as e:
        print(f"❌ Error in Orchestrator demo: {e}")
    
    print()

def demo_memory_capabilities():
    """Demonstrate memory and embedding capabilities with Voyage AI."""
    print("🧠 Memory & Voyage AI Embedding Demo")
    print("=" * 50)

    try:
        from sdk.embedding_sdk import EmbeddingSDK

        embedding_sdk = EmbeddingSDK()

        print(f"🚀 Using Voyage AI model: {embedding_sdk.model_name}")
        print(f"🔑 API Key configured: {'Yes' if embedding_sdk.client else 'No (using mock embeddings)'}")

        # Test embedding generation
        test_texts = [
            "I love sunny weather and outdoor activities",
            "My favorite hobby is reading science fiction books",
            "I work as a software engineer in San Francisco"
        ]

        print("\n📝 Generating embeddings for sample texts...")
        embeddings = embedding_sdk.generate_embeddings(test_texts)

        print(f"✅ Generated {len(embeddings)} embeddings")
        print(f"📊 Embedding dimensions: {len(embeddings[0])} (Voyage AI voyage-3-large)")

        # Test similarity
        query = "I enjoy outdoor sports and sunshine"
        query_embedding = embedding_sdk.generate_embedding(query)

        print(f"\n🔍 Query: {query}")
        print("📈 Similarity scores:")

        for i, text in enumerate(test_texts):
            similarity = embedding_sdk.calculate_similarity(query_embedding, embeddings[i])
            print(f"   {i+1}. {text[:40]}... → {similarity:.3f}")

    except Exception as e:
        print(f"❌ Error in memory demo: {e}")

    print()

def demo_model_swapping():
    """Demonstrate model swapping capabilities."""
    print("🔄 Model Swapping Demo")
    print("=" * 50)

    try:
        from sdk.llm_provider_sdk import LLMProviderSDK
        from langchain_core.messages import HumanMessage, SystemMessage

        llm = LLMProviderSDK(provider="groq", model="llama3-8b-8192")

        print(f"🤖 Default model: {llm.model}")
        print(f"🔗 Client available: {llm.client is not None}")

        # Test different models
        models_to_test = [
            "llama3-8b-8192",  # Fast model
            "llama3-70b-8192", # Powerful model
            "mixtral-8x7b-32768" # Alternative model
        ]

        messages = [
            SystemMessage(content="You are a helpful assistant."),
            HumanMessage(content="Explain quantum computing in one sentence.")
        ]

        print("\n🧪 Testing different models:")

        for model in models_to_test:
            print(f"\n📱 Testing model: {model}")
            try:
                response = llm.invoke(messages, model_name=model)
                print(f"✅ Response: {response.content[:100]}...")
            except Exception as e:
                print(f"❌ Error with {model}: {e}")

    except Exception as e:
        print(f"❌ Error in model swapping demo: {e}")

    print()

def demo_streaming():
    """Demonstrate streaming capabilities."""
    print("📡 Streaming Demo")
    print("=" * 50)

    try:
        from sdk.llm_provider_sdk import LLMProviderSDK
        from langchain_core.messages import HumanMessage, SystemMessage

        llm = LLMProviderSDK(provider="groq", model="llama3-8b-8192")

        messages = [
            SystemMessage(content="You are a helpful assistant."),
            HumanMessage(content="Write a short poem about artificial intelligence.")
        ]

        print("🎬 Starting streaming response...")
        print("📝 Response: ", end="", flush=True)

        full_response = ""
        chunk_count = 0

        for chunk in llm.stream(messages, model_name="llama3-8b-8192"):
            if chunk.get("choices") and chunk["choices"][0].get("delta", {}).get("content"):
                content = chunk["choices"][0]["delta"]["content"]
                print(content, end="", flush=True)
                full_response += content
                chunk_count += 1

        print(f"\n\n✅ Streaming completed!")
        print(f"📊 Total chunks: {chunk_count}")
        print(f"📏 Response length: {len(full_response)} characters")

    except Exception as e:
        print(f"❌ Error in streaming demo: {e}")

    print()

def demo_full_conversation():
    """Demonstrate a full conversation flow with memory."""
    print("💬 Full Conversation Demo with Memory")
    print("=" * 50)

    try:
        from main_orchestrator import darvis_orchestrator

        conversation = [
            "Hi Darvis, I'm new here. Can you introduce yourself?",
            "What kind of tasks can you help me with?",
            "I love hiking and outdoor activities. Can you remember that?",
            "What did I just tell you about my interests?"
        ]

        print("🎬 Starting conversation simulation with memory...")
        conversation_id = None

        for i, user_input in enumerate(conversation, 1):
            print(f"\n👤 User: {user_input}")

            result = darvis_orchestrator.process_message(
                user_message=user_input,
                user_id="demo_conversation_user",
                conversation_id=conversation_id,
                conversation_context={"session": "demo", "turn": i}
            )

            # Extract response and metadata
            if isinstance(result, dict):
                response = result.get("response", "No response")
                conversation_id = result.get("conversation_id", conversation_id)
                memories_count = result.get("relevant_memories_count", 0)

                print(f"🤖 Darvis: {response}")
                print(f"📊 Conversation ID: {conversation_id}")
                print(f"🧠 Relevant memories: {memories_count}")
            else:
                print(f"🤖 Darvis: {result}")

            # Add a small delay for readability
            import time
            time.sleep(0.5)

        print("\n✅ Memory-enhanced conversation demo completed!")

    except Exception as e:
        print(f"❌ Error in conversation demo: {e}")

def main():
    """Run all demonstrations."""
    print("🚀 Darvis AI Core Demonstration")
    print("=" * 60)
    print("This demo shows the foundational AI capabilities of Darvis.")
    print("Note: Without API keys, mock responses will be used.\n")
    
    # Run individual component demos
    demo_prp_manager()
    demo_llm_provider()
    demo_memory_capabilities()
    demo_model_swapping()
    demo_streaming()
    demo_tool_integration()
    demo_orchestrator()
    demo_full_conversation()
    
    print("\n🎉 Demo completed! The Enhanced Darvis AI Core with Tool Integration is ready!")
    print("\n📚 Next steps:")
    print("   1. Set up API keys:")
    print("      - GROQ_API_KEY for LLM provider")
    print("      - VOYAGE_API_KEY for state-of-the-art embeddings")
    print("      - COMPOSIO_API_KEY for tool integrations (Google Calendar, etc.)")
    print("   2. Install AI dependencies: pip install -r requirements-ai.txt")
    print("   3. Ensure PostgreSQL has pgvector extension enabled")
    print("   4. Test enhanced features:")
    print("      - Model swapping: POST /api/v1/chat/ with model_name parameter")
    print("      - Streaming: POST /api/v1/chat/ with stream=true")
    print("      - Memory: Use conversation_id for continuity")
    print("      - Tools: Try 'Schedule a meeting tomorrow at 3pm'")
    print("   5. Explore creating custom PRPs for specialized behaviors")
    print("   6. Monitor conversation history, tool usage, and semantic search capabilities")
    print("   7. Connect Google Calendar via Composio for real calendar integration")

if __name__ == "__main__":
    main()
