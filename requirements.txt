# Core FastAPI and ASGI server
fastapi==0.116.1
uvicorn[standard]==0.35.0

# Database dependencies
sqlalchemy==2.0.40
psycopg2-binary==2.9.10
alembic==1.16.4

# Redis for caching and session management
redis==6.2.0

# Background task processing
celery==5.4.0

# AI and LLM orchestration
langchain-core==0.3.70
langchain==0.3.26
langgraph==0.5.4
langchain-community==0.3.26
langchain-groq==0.3.6  # Add this line

# LiveKit Voice AI Agent Framework - Core framework with Turn Detector
livekit-agents[turn-detector]~=1.0
livekit-api>=1.0.5

# Custom STT/TTS integrations as requested
groq  # For Groq STT (Whisper models)
# DeepInfra API access via httpx (already included below)


# Authentication
firebase-admin==7.0.0

# Agent tooling
composio-core==0.7.20

# HTTP client for external APIs
httpx==0.28.1
requests==2.32.4

# Environment and configuration
python-dotenv==1.1.1
pydantic[email]==2.11.7
pydantic-settings==2.10.0

# Logging and monitoring
structlog==25.4.0

# Vector database support
pgvector==0.4.0

# Additional utilities
python-multipart==0.0.20
python-jose[cryptography]==3.5.0
passlib[bcrypt]==1.7.4

# Testing
pytest==8.4.1

#cloudinary
cloudinary==1.44.1
