#!/usr/bin/env python3
"""
Test script for Darvis Notification System
Tests the notification system components without requiring a running server.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all notification-related modules can be imported successfully."""
    print("🧪 Testing imports...")
    
    try:
        # Test notification delivery SDK
        from app.sdk.notification_delivery_sdk import NotificationDeliverySDK, send_notification
        print("✅ Notification delivery SDK imported successfully")
        
        # Test database models
        from app.db.models import NotificationPreference, NotificationHistory
        print("✅ Notification database models imported successfully")
        
        # Test API module
        from app.api.notifications import router
        print("✅ Notification API module imported successfully")
        
        # Test CRUD operations
        from app.db.crud import (
            create_notification_preferences, 
            get_notification_preferences,
            create_notification_history,
            get_notification_analytics
        )
        print("✅ Notification CRUD operations imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_notification_sdk():
    """Test the notification delivery SDK functionality."""
    print("\n🧪 Testing Notification Delivery SDK...")
    
    try:
        from app.sdk.notification_delivery_sdk import NotificationDeliverySDK
        
        # Initialize SDK
        sdk = NotificationDeliverySDK()
        print("✅ SDK initialized successfully")
        
        # Test mock notification (should work without FCM setup)
        result = sdk.send_push_notification(
            fcm_token="test_token_123",
            title="Test Notification",
            body="This is a test notification from Darvis",
            data={"type": "test", "source": "notification_test"}
        )
        
        print(f"✅ Mock notification sent: {result}")
        
        # Test token validation
        valid_token = sdk.validate_fcm_token("test_token_with_sufficient_length_to_pass_basic_validation_checks_123456789")
        invalid_token = sdk.validate_fcm_token("short")
        
        print(f"✅ Token validation working: valid={valid_token}, invalid={invalid_token}")
        
        # Test batch notifications
        notifications = [
            {
                "fcm_token": "token1",
                "title": "Batch Test 1",
                "body": "First batch notification"
            },
            {
                "fcm_token": "token2", 
                "title": "Batch Test 2",
                "body": "Second batch notification"
            }
        ]
        
        batch_result = sdk.send_batch_notifications(notifications)
        print(f"✅ Batch notifications sent: {batch_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ SDK test failed: {e}")
        return False

def test_database_models():
    """Test that database models are properly defined."""
    print("\n🧪 Testing Database Models...")
    
    try:
        from app.db.models import NotificationPreference, NotificationHistory
        
        # Check NotificationPreference model attributes (updated to match new frontend requirements)
        pref_attrs = [
            'id', 'owner_id', 'therapy_reminders', 'task_notifications',
            'daily_check_ins', 'weekly_reports', 'system_updates', 'contextual_ai',
            'quiet_hours_enabled', 'quiet_hours_start_hour', 'quiet_hours_start_minute',
            'quiet_hours_end_hour', 'quiet_hours_end_minute', 'notification_frequency',
            'sound_enabled', 'vibration_enabled', 'badge_count_enabled',
            'created_at', 'updated_at'
        ]
        
        for attr in pref_attrs:
            if hasattr(NotificationPreference, attr):
                print(f"✅ NotificationPreference.{attr} exists")
            else:
                print(f"❌ NotificationPreference.{attr} missing")
                return False
        
        # Check NotificationHistory model attributes
        history_attrs = [
            'id', 'owner_id', 'notification_type', 'title', 'content',
            'sent_at', 'clicked_at', 'dismissed_at', 'relevance_score'
        ]
        
        for attr in history_attrs:
            if hasattr(NotificationHistory, attr):
                print(f"✅ NotificationHistory.{attr} exists")
            else:
                print(f"❌ NotificationHistory.{attr} missing")
                return False
        
        print("✅ All database model attributes present")
        return True
        
    except Exception as e:
        print(f"❌ Database model test failed: {e}")
        return False

def test_api_endpoints():
    """Test that API endpoints are properly defined."""
    print("\n🧪 Testing API Endpoints...")
    
    try:
        from app.api.notifications import router
        
        # Check that router has the expected routes
        routes = [route.path for route in router.routes]
        expected_routes = [
            "/api/v1/notifications/preferences",
            "/api/v1/notifications/fcm-token", 
            "/api/v1/notifications/history",
            "/api/v1/notifications/interaction",
            "/api/v1/notifications/analytics",
            "/api/v1/notifications/test"
        ]
        
        for expected_route in expected_routes:
            if any(expected_route in route for route in routes):
                print(f"✅ Route {expected_route} exists")
            else:
                print(f"❌ Route {expected_route} missing")
                return False
        
        print("✅ All API endpoints properly defined")
        return True
        
    except Exception as e:
        print(f"❌ API endpoint test failed: {e}")
        return False

def test_worker_tasks():
    """Test that Celery worker tasks are properly defined."""
    print("\n🧪 Testing Worker Tasks...")
    
    try:
        # Import worker tasks
        from app.worker import (
            send_task_reminder_notification,
            send_inbox_completion_notification,
            check_due_tasks_and_send_reminders
        )
        
        print("✅ Task reminder notification task imported")
        print("✅ Inbox completion notification task imported") 
        print("✅ Due tasks checker task imported")
        
        # Check task names
        expected_tasks = [
            "send_task_reminder_notification",
            "send_inbox_completion_notification", 
            "check_due_tasks_and_send_reminders"
        ]
        
        for task_name in expected_tasks:
            print(f"✅ Celery task '{task_name}' properly defined")
        
        return True
        
    except Exception as e:
        print(f"❌ Worker task test failed: {e}")
        return False

def main():
    """Run all notification system tests."""
    print("🚀 Starting Darvis Notification System Tests\n")
    
    tests = [
        ("Import Tests", test_imports),
        ("SDK Tests", test_notification_sdk),
        ("Database Model Tests", test_database_models),
        ("API Endpoint Tests", test_api_endpoints),
        ("Worker Task Tests", test_worker_tasks)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running {test_name}")
        print('='*50)
        
        if test_func():
            print(f"✅ {test_name} PASSED")
            passed += 1
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY: {passed}/{total} tests passed")
    print('='*50)
    
    if passed == total:
        print("🎉 All notification system tests PASSED!")
        print("✅ The notification system is ready for use!")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
