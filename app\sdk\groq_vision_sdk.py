"""
Groq Vision SDK for Darvis.

This module provides a unified interface for Groq's vision models,
specifically optimized for the llama-4-scout-17b-16e-instruct model
for image analysis and text extraction tasks.
"""

import os
import json
import base64
import logging
from typing import Dict, Any, Optional, Union
from groq import Groq
import httpx

logger = logging.getLogger(__name__)

class GroqVisionSDK:
    """
    SDK for interacting with Groq's vision models.
    
    Provides image analysis capabilities using the llama-4-scout-17b-16e-instruct model
    with proper error handling, rate limiting, and response formatting.
    """
    
    def __init__(self):
        """Initialize the Groq Vision SDK with API credentials."""
        self.api_key = os.getenv("GROQ_API_KEY")
        self.model_name = "meta-llama-4-scout-17b-16e-instruct"
        self.client = None
        
        if self.api_key:
            self._initialize_client()
        else:
            logger.warning("GROQ_API_KEY not found. Vision analysis will use mock responses.")
    
    def _initialize_client(self):
        """Initialize the Groq client."""
        try:
            self.client = Groq(api_key=self.api_key)
            logger.info(f"Groq Vision client initialized successfully with model {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize Groq Vision client: {e}")
            self.client = None
    
    def analyze_image(
        self,
        image_data: Union[str, bytes],
        system_prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> Dict[str, Any]:
        """
        Analyze an image using Groq's vision model.
        
        Args:
            image_data: Base64 encoded image string or raw bytes
            system_prompt: System prompt for the analysis task
            max_tokens: Maximum tokens in response (default: 1000)
            temperature: Model temperature for consistency (default: 0.1)
            
        Returns:
            Dict containing the analysis result or error information
        """
        if not self.client:
            return self._mock_vision_response()
        
        try:
            # Ensure image_data is base64 encoded string
            if isinstance(image_data, bytes):
                image_base64 = base64.b64encode(image_data).decode('utf-8')
            else:
                image_base64 = image_data
            
            # Prepare the message with image
            messages = [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
            
            # Make the API call
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            # Extract the response content
            content = response.choices[0].message.content
            
            logger.info(f"Groq Vision analysis completed successfully")
            
            return {
                "success": True,
                "content": content,
                "model_used": self.model_name,
                "tokens_used": response.usage.total_tokens if response.usage else None
            }
            
        except Exception as e:
            logger.error(f"Error in Groq Vision analysis: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": None
            }
    
    def extract_event_from_image(
        self,
        image_data: Union[str, bytes]
    ) -> Dict[str, Any]:
        """
        Extract event details from an image using specialized prompt.
        
        This method is specifically designed for the image-to-event tool,
        extracting event_name, event_date, and event_location from images.
        
        Args:
            image_data: Base64 encoded image string or raw bytes
            
        Returns:
            Dict containing extracted event details or error information
        """
        system_prompt = """Your task is to analyze this image. First, transcribe all text. Second, from that transcribed text, you MUST extract values for the following JSON fields: 'event_name', 'event_date', and 'event_location'.

If, after analyzing the text, you cannot find a plausible value for all three of those specific fields, you MUST return a JSON object with {"error": "I could not find all the necessary event details in that image. Please ensure the name, date, and location are clearly visible."}.

If successful, the output must be a JSON object with the extracted fields: {"event_name": "...", "event_date": "...", "event_location": "..."}.

Be precise and only extract information that is clearly visible in the image. For dates, use a standard format like "YYYY-MM-DD" or "Month DD, YYYY"."""
        
        result = self.analyze_image(
            image_data=image_data,
            system_prompt=system_prompt,
            max_tokens=500,
            temperature=0.0  # Use 0 temperature for consistent extraction
        )
        
        if not result["success"]:
            return result
        
        # Try to parse the JSON response
        try:
            content = result["content"].strip()
            
            # Find JSON in the response (handle cases where model adds extra text)
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = content[start_idx:end_idx]
                parsed_result = json.loads(json_str)
                
                # Validate the expected fields
                if "error" in parsed_result:
                    return {
                        "success": False,
                        "error": parsed_result["error"],
                        "content": None
                    }
                
                required_fields = ["event_name", "event_date", "event_location"]
                if all(field in parsed_result for field in required_fields):
                    return {
                        "success": True,
                        "event_details": parsed_result,
                        "model_used": self.model_name
                    }
                else:
                    missing_fields = [f for f in required_fields if f not in parsed_result]
                    return {
                        "success": False,
                        "error": f"Missing required fields: {', '.join(missing_fields)}",
                        "content": content
                    }
            else:
                return {
                    "success": False,
                    "error": "Could not find valid JSON in response",
                    "content": content
                }
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from Groq Vision response: {e}")
            return {
                "success": False,
                "error": "Invalid JSON format in response",
                "content": result["content"]
            }
    
    def _mock_vision_response(self) -> Dict[str, Any]:
        """
        Generate a mock response for development/testing when API key is not available.
        """
        logger.info("Using mock Groq Vision response (no API key configured)")
        
        return {
            "success": True,
            "content": '{"event_name": "Tech Conference 2025", "event_date": "2025-03-15", "event_location": "San Francisco Convention Center"}',
            "model_used": "mock-vision-model",
            "tokens_used": 50
        }
    
    def validate_image_format(self, image_data: Union[str, bytes]) -> bool:
        """
        Validate that the image data is in a supported format.
        
        Args:
            image_data: Image data to validate
            
        Returns:
            True if format is supported, False otherwise
        """
        try:
            if isinstance(image_data, str):
                # Try to decode base64
                base64.b64decode(image_data)
                return True
            elif isinstance(image_data, bytes):
                # Check if it's valid image bytes (basic check)
                return len(image_data) > 0
            else:
                return False
        except Exception:
            return False
