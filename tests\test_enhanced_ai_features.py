import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add the darvis-ai-core directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "darvis-ai-core"))

from app.sdk.embedding_sdk import EmbeddingSDK
from app.sdk.llm_provider_sdk import LLMProviderSDK

class TestVoyageAIEmbeddings:
    """Test cases for Voyage AI embedding integration."""
    
    def test_voyage_ai_initialization(self):
        """Test Voyage AI embedding SDK initialization."""
        embedding_sdk = EmbeddingSDK()
        assert embedding_sdk.model_name == "voyage-3-large"
        assert embedding_sdk.client is None  # No API key in test environment
    
    @patch('app.sdk.embedding_sdk.voyageai')
    @patch.dict(os.environ, {'VOYAGE_API_KEY': 'test_key'})
    def test_voyage_ai_client_initialization(self, mock_voyageai):
        """Test Voyage AI client initialization with API key."""
        mock_client = Mock()
        mock_voyageai.Client.return_value = mock_client
        
        embedding_sdk = EmbeddingSDK()
        embedding_sdk._initialize_client()
        
        mock_voyageai.Client.assert_called_once_with(api_key='test_key')
    
    def test_1024_dimensional_embeddings(self):
        """Test that embeddings are 1024-dimensional."""
        embedding_sdk = EmbeddingSDK()
        
        # Test mock embedding (no API key)
        embedding = embedding_sdk.generate_embedding("Test text")
        assert len(embedding) == 1024
        assert all(isinstance(x, float) for x in embedding)
    
    @patch('app.sdk.embedding_sdk.voyageai')
    def test_voyage_ai_embedding_generation(self, mock_voyageai):
        """Test Voyage AI embedding generation."""
        # Mock the client and response
        mock_client = Mock()
        mock_result = Mock()
        mock_result.embeddings = [[0.1] * 1024]  # 1024-dimensional embedding
        mock_client.embed.return_value = mock_result
        
        embedding_sdk = EmbeddingSDK()
        embedding_sdk.client = mock_client
        
        embedding = embedding_sdk.generate_embedding("Test text")
        
        assert len(embedding) == 1024
        mock_client.embed.assert_called_once_with(
            texts=["Test text"],
            model="voyage-3-large",
            input_type="document"
        )
    
    @patch('app.sdk.embedding_sdk.voyageai')
    def test_voyage_ai_batch_embeddings(self, mock_voyageai):
        """Test Voyage AI batch embedding generation."""
        # Mock the client and response
        mock_client = Mock()
        mock_result = Mock()
        mock_result.embeddings = [[0.1] * 1024, [0.2] * 1024]  # Two 1024-dimensional embeddings
        mock_client.embed.return_value = mock_result
        
        embedding_sdk = EmbeddingSDK()
        embedding_sdk.client = mock_client
        
        texts = ["First text", "Second text"]
        embeddings = embedding_sdk.generate_embeddings(texts)
        
        assert len(embeddings) == 2
        assert all(len(emb) == 1024 for emb in embeddings)
        mock_client.embed.assert_called_once_with(
            texts=texts,
            model="voyage-3-large",
            input_type="document"
        )

class TestGroqModelSwapping:
    """Test cases for Groq model swapping functionality."""
    
    def test_llm_provider_model_parameter(self):
        """Test LLM provider accepts model parameter."""
        llm_provider = LLMProviderSDK(provider="groq", model="llama3-8b-8192")
        assert llm_provider.model == "llama3-8b-8192"
    
    @patch('app.sdk.llm_provider_sdk.ChatGroq')
    def test_groq_model_swapping(self, mock_chat_groq):
        """Test Groq model swapping functionality."""
        from langchain_core.messages import HumanMessage, AIMessage
        
        # Mock the Groq client
        mock_client = Mock()
        mock_response = AIMessage(content="Test response")
        mock_client.invoke.return_value = mock_response
        mock_chat_groq.return_value = mock_client
        
        llm_provider = LLMProviderSDK(provider="groq", model="llama3-8b-8192")
        llm_provider.client = mock_client
        llm_provider.client.groq_api_key = "test_key"
        
        messages = [HumanMessage(content="Test message")]
        
        # Test with custom model
        response = llm_provider.invoke(messages, model_name="llama3-70b-8192")
        
        # Verify new client was created with custom model
        mock_chat_groq.assert_called_with(
            groq_api_key="test_key",
            model_name="llama3-70b-8192",
            temperature=0.7,
            max_tokens=1024
        )
        assert response.content == "Test response"
    
    @patch('app.sdk.llm_provider_sdk.ChatGroq')
    def test_groq_streaming(self, mock_chat_groq):
        """Test Groq streaming functionality."""
        from langchain_core.messages import HumanMessage
        
        # Mock the streaming client
        mock_client = Mock()
        mock_chunks = [
            Mock(content="Hello"),
            Mock(content=" world"),
            Mock(content="!")
        ]
        mock_client.stream.return_value = iter(mock_chunks)
        mock_chat_groq.return_value = mock_client
        
        llm_provider = LLMProviderSDK(provider="groq", model="llama3-8b-8192")
        llm_provider.client = mock_client
        llm_provider.client.groq_api_key = "test_key"
        
        messages = [HumanMessage(content="Test message")]
        
        # Test streaming
        chunks = list(llm_provider.stream(messages, model_name="llama3-8b-8192"))
        
        # Verify streaming client was created
        mock_chat_groq.assert_called_with(
            groq_api_key="test_key",
            model_name="llama3-8b-8192",
            temperature=0.7,
            max_tokens=1024,
            streaming=True
        )
        
        # Verify chunks were processed correctly
        assert len(chunks) == 3
        assert chunks[0]["choices"][0]["delta"]["content"] == "Hello"
        assert chunks[1]["choices"][0]["delta"]["content"] == " world"
        assert chunks[2]["choices"][0]["delta"]["content"] == "!"

class TestStreamingAPI:
    """Test cases for streaming API functionality."""
    
    def test_chat_message_with_streaming(self):
        """Test chat message model with streaming parameter."""
        from app.api.chat import ChatMessage
        
        message = ChatMessage(
            message="Hello, stream this response",
            conversation_id="conv_123",
            model_name="llama3-70b-8192",
            stream=True,
            context={"test": True}
        )
        
        assert message.stream is True
        assert message.model_name == "llama3-70b-8192"
        assert message.conversation_id == "conv_123"
    
    def test_chat_response_with_model_used(self):
        """Test chat response model with model_used field."""
        from app.api.chat import ChatResponse
        
        response = ChatResponse(
            response="Test response",
            conversation_id="conv_123",
            user_id="user_456",
            model_used="llama3-70b-8192",
            relevant_memories_count=2,
            success=True
        )
        
        assert response.model_used == "llama3-70b-8192"
        assert response.relevant_memories_count == 2

class TestIntegrationFeatures:
    """Test cases for integrated enhanced features."""
    
    @patch('darvis-ai-core.main_orchestrator.embedding_service')
    @patch('darvis-ai-core.main_orchestrator.llm_provider')
    def test_streaming_with_memory(self, mock_llm_provider, mock_embedding_service):
        """Test streaming functionality with memory integration."""
        from main_orchestrator import DarvisOrchestrator
        
        # Mock embedding service
        mock_embedding_service.generate_embedding.return_value = [0.1] * 1024
        
        # Mock LLM provider streaming
        mock_chunks = [
            {"choices": [{"delta": {"content": "Hello"}}]},
            {"choices": [{"delta": {"content": " there"}}]},
            {"choices": [{"delta": {"content": "!"}}]}
        ]
        mock_llm_provider.stream.return_value = iter(mock_chunks)
        
        orchestrator = DarvisOrchestrator()
        
        # Test streaming
        chunks = list(orchestrator.stream_message(
            user_message="Hello",
            user_id="test_user",
            model_name="llama3-70b-8192"
        ))
        
        # Verify we get metadata, content chunks, and completion
        chunk_types = [chunk.get("type") for chunk in chunks]
        assert "metadata" in chunk_types
        assert "content" in chunk_types
        assert "complete" in chunk_types
        
        # Verify model was passed correctly
        mock_llm_provider.stream.assert_called_once()
        call_args = mock_llm_provider.stream.call_args
        assert call_args[1] == "llama3-70b-8192"  # model_name parameter

if __name__ == "__main__":
    pytest.main([__file__])
