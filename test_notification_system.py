"""
Comprehensive test suite for the notification system.

This test suite validates all notification functionality including:
1. Notification creation and validation
2. Queue management and delivery tracking
3. Error handling and edge cases
4. Background task processing
5. API endpoints and CRUD operations
"""

import pytest
import json
from datetime import datetime, timedelta, timezone
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from unittest.mock import patch, MagicMock

from app.main import app
from app.db.database import get_db
from app.db.models import Base, User, Notification, NotificationPreference
from app.db import crud
from app.api.auth import get_current_user

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_notification_system.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

# Mock Firebase authentication
def mock_get_current_user():
    return {"uid": "test_user_123", "email": "<EMAIL>"}

app.dependency_overrides[get_current_user] = mock_get_current_user

# Create test client
client = TestClient(app)

# Test constants
TEST_USER_ID = "test_user_123"
TEST_USER_EMAIL = "<EMAIL>"

@pytest.fixture(scope="module")
def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def test_db():
    """Get test database session."""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()

@pytest.fixture
def test_user(test_db):
    """Create a test user."""
    # Check if user already exists
    existing_user = crud.get_user_by_email(db=test_db, email=TEST_USER_EMAIL)
    if existing_user:
        return existing_user
    
    user = crud.create_user(db=test_db, user_id=TEST_USER_ID, email=TEST_USER_EMAIL)
    # Set FCM token for notification testing
    user.fcm_token = "test_fcm_token_123"
    test_db.commit()
    test_db.refresh(user)
    return user

class TestNotificationValidation:
    """Test notification creation validation and edge cases."""
    
    def test_create_notification_valid_data(self, setup_database, test_user):
        """Test creating notification with valid data."""
        notification_data = {
            "title": "Test Notification",
            "body": "This is a test notification",
            "type": "therapy_reminder",
            "priority": "normal",
            "scheduled_time": (datetime.now(timezone.utc) + timedelta(hours=1)).isoformat(),
            "navigation_route": "/therapy"
        }
        
        response = client.post("/api/v1/notifications/", json=notification_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "notification_id" in data
    
    def test_create_notification_past_time(self, setup_database, test_user):
        """Test creating notification with past scheduled time."""
        notification_data = {
            "title": "Test Notification",
            "body": "This is a test notification",
            "type": "therapy_reminder",
            "priority": "normal",
            "scheduled_time": (datetime.now(timezone.utc) - timedelta(hours=1)).isoformat()
        }
        
        response = client.post("/api/v1/notifications/", json=notification_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_create_notification_invalid_type(self, setup_database, test_user):
        """Test creating notification with invalid type."""
        notification_data = {
            "title": "Test Notification",
            "body": "This is a test notification",
            "type": "invalid_type",
            "priority": "normal",
            "scheduled_time": (datetime.now(timezone.utc) + timedelta(hours=1)).isoformat()
        }
        
        response = client.post("/api/v1/notifications/", json=notification_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_create_notification_rate_limiting(self, setup_database, test_user):
        """Test notification rate limiting."""
        notification_data = {
            "title": "Test Notification",
            "body": "This is a test notification",
            "type": "therapy_reminder",
            "priority": "normal",
            "scheduled_time": (datetime.now(timezone.utc) + timedelta(hours=1)).isoformat()
        }
        
        # Create 50 notifications (the daily limit)
        for i in range(50):
            notification_data["title"] = f"Test Notification {i}"
            response = client.post("/api/v1/notifications/", json=notification_data)
            if i < 49:  # First 49 should succeed
                assert response.status_code == 201
        
        # 51st notification should be rate limited
        notification_data["title"] = "Rate Limited Notification"
        response = client.post("/api/v1/notifications/", json=notification_data)
        assert response.status_code == 429  # Too Many Requests

class TestNotificationCRUD:
    """Test notification CRUD operations."""
    
    def test_get_notification(self, setup_database, test_user, test_db):
        """Test getting a specific notification."""
        # Create a notification directly in database
        notification = crud.create_notification(
            db=test_db,
            user_id=TEST_USER_ID,
            title="Test Notification",
            body="Test body",
            notification_type="therapy_reminder",
            priority="normal",
            scheduled_time=datetime.now(timezone.utc) + timedelta(hours=1)
        )
        
        response = client.get(f"/api/v1/notifications/{notification.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == notification.id
        assert data["title"] == "Test Notification"
    
    def test_update_notification(self, setup_database, test_user, test_db):
        """Test updating a notification."""
        # Create a notification
        notification = crud.create_notification(
            db=test_db,
            user_id=TEST_USER_ID,
            title="Original Title",
            body="Original body",
            notification_type="therapy_reminder",
            priority="normal",
            scheduled_time=datetime.now(timezone.utc) + timedelta(hours=1)
        )
        
        update_data = {
            "title": "Updated Title",
            "body": "Updated body",
            "priority": "high"
        }
        
        response = client.put(f"/api/v1/notifications/{notification.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "Updated Title"
        assert data["body"] == "Updated body"
        assert data["priority"] == "high"
    
    def test_delete_notification(self, setup_database, test_user, test_db):
        """Test deleting an undelivered notification."""
        # Create a notification
        notification = crud.create_notification(
            db=test_db,
            user_id=TEST_USER_ID,
            title="To Delete",
            body="This will be deleted",
            notification_type="therapy_reminder",
            priority="normal",
            scheduled_time=datetime.now(timezone.utc) + timedelta(hours=1)
        )
        
        response = client.delete(f"/api/v1/notifications/{notification.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["deleted"] is True
    
    def test_mark_notification_read(self, setup_database, test_user, test_db):
        """Test marking a notification as read."""
        # Create a delivered notification
        notification = crud.create_notification(
            db=test_db,
            user_id=TEST_USER_ID,
            title="Read Test",
            body="Mark me as read",
            notification_type="therapy_reminder",
            priority="normal",
            scheduled_time=datetime.now(timezone.utc) + timedelta(hours=1)
        )
        notification.is_delivered = True
        test_db.commit()
        
        response = client.post(f"/api/v1/notifications/{notification.id}/read")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["marked_read"] is True

class TestNotificationQueue:
    """Test notification queue management and delivery tracking."""
    
    def test_delivery_attempt_tracking(self, test_db):
        """Test recording delivery attempts."""
        # Create a notification
        notification = crud.create_notification(
            db=test_db,
            user_id=TEST_USER_ID,
            title="Delivery Test",
            body="Test delivery tracking",
            notification_type="therapy_reminder",
            priority="normal",
            scheduled_time=datetime.now(timezone.utc) + timedelta(hours=1)
        )
        
        # Record a failed delivery attempt
        result = crud.record_delivery_attempt(
            db=test_db,
            notification_id=notification.id,
            success=False,
            failure_reason="FCM token invalid"
        )
        
        assert result is not None
        assert result.delivery_attempts == 1
        assert result.failure_reason == "FCM token invalid"
        assert result.retry_after is not None
        assert not result.is_delivered
        assert not result.is_failed  # Not failed until 3 attempts
    
    def test_notification_failure_after_retries(self, test_db):
        """Test notification marked as failed after 3 attempts."""
        # Create a notification
        notification = crud.create_notification(
            db=test_db,
            user_id=TEST_USER_ID,
            title="Failure Test",
            body="Test failure after retries",
            notification_type="therapy_reminder",
            priority="normal",
            scheduled_time=datetime.now(timezone.utc) + timedelta(hours=1)
        )
        
        # Record 3 failed delivery attempts
        for i in range(3):
            crud.record_delivery_attempt(
                db=test_db,
                notification_id=notification.id,
                success=False,
                failure_reason=f"Attempt {i+1} failed"
            )
        
        # Refresh notification from database
        test_db.refresh(notification)
        
        assert notification.delivery_attempts == 3
        assert notification.is_failed is True
        assert not notification.is_delivered
    
    def test_get_delivery_stats(self, setup_database, test_user):
        """Test getting delivery statistics."""
        response = client.get("/api/v1/notifications/delivery-stats?days=7")
        
        assert response.status_code == 200
        data = response.json()
        assert "total_notifications" in data
        assert "delivered_notifications" in data
        assert "failed_notifications" in data
        assert "delivery_rate" in data
        assert "failure_rate" in data

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
