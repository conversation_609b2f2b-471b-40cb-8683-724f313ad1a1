import psycopg2

# Your current database (what you've been developing against)
conn = psycopg2.connect(
    host="*************",
    database="darvis-postgres", 
    user="postgres",
    password="qroinoruwob23u410841rqouUBUBUBo808",
    port="5432"
)

cur = conn.cursor()

print("📋 COMPLETE DATABASE AUDIT")
print("=" * 50)

# Get all tables
cur.execute("""
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema='public' 
    ORDER BY table_name;
""")

all_tables = [row[0] for row in cur.fetchall()]

print(f"📊 Total tables in database: {len(all_tables)}")
print("\n🗂️ ALL TABLES:")
for table in all_tables:
    print(f"  • {table}")

print("\n🔍 TABLE DETAILS:")
for table in all_tables:
    print(f"\n📋 {table.upper()}:")
    cur.execute(f"""
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = '{table}' 
        ORDER BY ordinal_position;
    """)
    
    columns = cur.fetchall()
    for col_name, data_type, nullable, default in columns:
        null_str = "NULL" if nullable == "YES" else "NOT NULL"
        default_str = f" DEFAULT {default}" if default else ""
        print(f"    {col_name}: {data_type} {null_str}{default_str}")

# Check for recent tables (likely new ones)
print("\n🆕 RECENTLY CREATED TABLES:")
cur.execute("""
    SELECT schemaname, tablename, tableowner 
    FROM pg_tables 
    WHERE schemaname = 'public'
    ORDER BY tablename;
""")

recent_tables = cur.fetchall()
therapy_tables = ['therapy_knowledge_chunks', 'therapy_sessions', 'therapy_progress']
new_looking_tables = []

for schema, table, owner in recent_tables:
    if any(keyword in table.lower() for keyword in ['therapy', 'notification', 'inbox', 'lesson', 'contact', 'pronunciation']):
        new_looking_tables.append(table)

print("Tables that look like they might be new:")
for table in new_looking_tables:
    print(f"  🆕 {table}")

conn.close()
print("\n✅ Audit complete!")

python -c "from app.db.models import Base
print('📋 MODELS IN YOUR CODE:')
for table_name in Base.metadata.tables.keys():
    print(f'  • {table_name}')
"