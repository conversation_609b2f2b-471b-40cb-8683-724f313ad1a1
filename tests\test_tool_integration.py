import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add the darvis-ai-core directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "darvis-ai-core"))

from app.sdk.composio_tools_sdk import ComposioToolsSDK
from main_orchestrator import DarvisOrchestrator

class TestComposioToolsSDK:
    """Test cases for Composio tools SDK integration."""
    
    def test_composio_sdk_initialization(self):
        """Test Composio SDK initialization."""
        sdk = ComposioToolsSDK()
        assert sdk.client is None  # No API key in test environment
    
    @patch('app.sdk.composio_tools_sdk.Composio')
    @patch.dict(os.environ, {'COMPOSIO_API_KEY': 'test_key'})
    def test_composio_client_initialization(self, mock_composio):
        """Test Composio client initialization with API key."""
        mock_client = Mock()
        mock_composio.return_value = mock_client
        
        sdk = ComposioToolsSDK()
        sdk._initialize_client()
        
        mock_composio.assert_called_once_with(api_key='test_key')
    
    def test_mock_calendar_event_creation(self):
        """Test mock calendar event creation."""
        sdk = ComposioToolsSDK()
        
        result = sdk.create_calendar_event(
            user_id="test_user",
            summary="Test Meeting",
            start_time="2025-01-24T15:00:00Z",
            end_time="2025-01-24T16:00:00Z",
            description="Test meeting description"
        )
        
        assert result["success"] is True
        assert result["summary"] == "Test Meeting"
        assert result["start_time"] == "2025-01-24T15:00:00Z"
        assert result["end_time"] == "2025-01-24T16:00:00Z"
        assert "mock" in result
        assert "event_id" in result
    
    @patch('app.sdk.composio_tools_sdk.Composio')
    def test_real_calendar_event_creation(self, mock_composio):
        """Test real calendar event creation with mocked Composio."""
        # Mock the client and tools
        mock_client = Mock()
        mock_tools = Mock()
        mock_result = Mock()
        mock_result.successful = True
        mock_result.data = {
            "id": "test_event_123",
            "htmlLink": "https://calendar.google.com/event?eid=test_event_123"
        }
        
        mock_tools.execute.return_value = mock_result
        mock_client.tools = mock_tools
        mock_composio.return_value = mock_client
        
        sdk = ComposioToolsSDK()
        sdk.client = mock_client
        
        result = sdk.create_calendar_event(
            user_id="test_user",
            summary="Real Test Meeting",
            start_time="2025-01-24T15:00:00Z",
            end_time="2025-01-24T16:00:00Z",
            description="Real test meeting description",
            location="Conference Room A"
        )
        
        assert result["success"] is True
        assert result["event_id"] == "test_event_123"
        assert result["event_link"] == "https://calendar.google.com/event?eid=test_event_123"
        assert result["summary"] == "Real Test Meeting"
        
        # Verify the tool was called correctly
        mock_tools.execute.assert_called_once_with(
            "GOOGLECALENDAR_CREATE_EVENT",
            user_id="test_user",
            arguments={
                "summary": "Real Test Meeting",
                "start": {
                    "dateTime": "2025-01-24T15:00:00Z",
                    "timeZone": "UTC"
                },
                "end": {
                    "dateTime": "2025-01-24T16:00:00Z",
                    "timeZone": "UTC"
                },
                "description": "Real test meeting description",
                "location": "Conference Room A"
            }
        )

class TestToolDetectionAndExtraction:
    """Test cases for tool detection and extraction in the orchestrator."""
    
    def test_tool_intent_detection(self):
        """Test tool intent detection from user messages."""
        orchestrator = DarvisOrchestrator()
        
        # Test calendar-related messages
        calendar_messages = [
            "Schedule a meeting tomorrow at 3pm",
            "Book an appointment for next week",
            "Create a calendar event for the team meeting",
            "Plan a call with the client at 2:30 PM"
        ]
        
        for message in calendar_messages:
            intent = orchestrator._detect_tool_intent(message)
            # Should detect tool intent (returns PRP content or None)
            # In test environment, PRP might not load, so we just check it doesn't crash
            assert intent is None or isinstance(intent, str)
    
    def test_tool_call_extraction(self):
        """Test extraction of tool calls from AI responses."""
        orchestrator = DarvisOrchestrator()
        
        # Test AI response with tool call
        ai_response = """I'll schedule that meeting for you.

TOOL_CALL: create_calendar_event
PARAMETERS: {"summary": "Team Meeting", "start_time": "2025-01-24T15:00:00Z", "end_time": "2025-01-24T16:00:00Z", "description": "Weekly team sync"}
REASONING: User requested to schedule a team meeting for tomorrow at 3pm

I've scheduled the meeting for you."""
        
        tool_calls = orchestrator._extract_tool_calls(ai_response)
        
        assert len(tool_calls) == 1
        assert tool_calls[0]["tool_name"] == "create_calendar_event"
        assert tool_calls[0]["parameters"]["summary"] == "Team Meeting"
        assert tool_calls[0]["parameters"]["start_time"] == "2025-01-24T15:00:00Z"
        assert "team meeting" in tool_calls[0]["reasoning"].lower()
    
    def test_multiple_tool_calls_extraction(self):
        """Test extraction of multiple tool calls from AI response."""
        orchestrator = DarvisOrchestrator()
        
        ai_response = """I'll help you with both tasks.

TOOL_CALL: create_calendar_event
PARAMETERS: {"summary": "First Meeting", "start_time": "2025-01-24T15:00:00Z", "end_time": "2025-01-24T16:00:00Z"}
REASONING: First meeting requested

TOOL_CALL: create_calendar_event
PARAMETERS: {"summary": "Second Meeting", "start_time": "2025-01-24T17:00:00Z", "end_time": "2025-01-24T18:00:00Z"}
REASONING: Second meeting requested

Both meetings have been scheduled."""
        
        tool_calls = orchestrator._extract_tool_calls(ai_response)
        
        assert len(tool_calls) == 2
        assert tool_calls[0]["parameters"]["summary"] == "First Meeting"
        assert tool_calls[1]["parameters"]["summary"] == "Second Meeting"

class TestToolExecutionNode:
    """Test cases for the tool execution node."""
    
    @patch('main_orchestrator.composio_tools')
    def test_tool_execution_node(self, mock_composio_tools):
        """Test the tool execution node."""
        # Mock successful tool execution
        mock_composio_tools.create_calendar_event.return_value = {
            "success": True,
            "event_id": "test_event_123",
            "message": "Event 'Test Meeting' created successfully"
        }
        
        orchestrator = DarvisOrchestrator()
        
        # Create state with tool calls
        state = {
            "user_id": "test_user",
            "conversation_id": "test_conv",
            "tool_calls": [{
                "tool_name": "create_calendar_event",
                "parameters": {
                    "summary": "Test Meeting",
                    "start_time": "2025-01-24T15:00:00Z",
                    "end_time": "2025-01-24T16:00:00Z"
                },
                "reasoning": "User requested meeting"
            }]
        }
        
        result = orchestrator._tool_execution_node(state)
        
        assert "tool_results" in result
        assert len(result["tool_results"]) == 1
        assert result["tool_results"][0]["success"] is True
        assert result["tool_calls"] == []  # Should be cleared after execution
        
        # Verify the tool was called
        mock_composio_tools.create_calendar_event.assert_called_once_with(
            user_id="test_user",
            summary="Test Meeting",
            start_time="2025-01-24T15:00:00Z",
            end_time="2025-01-24T16:00:00Z",
            description=None,
            location=None
        )

class TestConditionalRouting:
    """Test cases for conditional routing in the graph."""
    
    def test_should_use_tools_with_tool_calls(self):
        """Test conditional routing when tool calls are present."""
        orchestrator = DarvisOrchestrator()
        
        state_with_tools = {
            "tool_calls": [{
                "tool_name": "create_calendar_event",
                "parameters": {"summary": "Test"}
            }]
        }
        
        result = orchestrator._should_use_tools(state_with_tools)
        assert result == "tools"
    
    def test_should_use_tools_without_tool_calls(self):
        """Test conditional routing when no tool calls are present."""
        orchestrator = DarvisOrchestrator()
        
        state_without_tools = {
            "tool_calls": []
        }
        
        result = orchestrator._should_use_tools(state_without_tools)
        assert result == "end"
        
        # Test with missing tool_calls key
        state_no_key = {}
        result = orchestrator._should_use_tools(state_no_key)
        assert result == "end"

if __name__ == "__main__":
    pytest.main([__file__])
