from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from firebase_admin import auth
from typing import Dict, Any
import logging

# Configure logging
logger = logging.getLogger(__name__)

# HTTP Bearer token security scheme
security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    FastAPI dependency to verify Firebase ID tokens and return user information.

    Args:
        credentials: HTTP Bearer token from Authorization header

    Returns:
        Dict containing decoded user data including UID

    Raises:
        HTTPException: 401 Unauthorized if token verification fails
    """
    try:
        # Extract the token from the credentials
        id_token = credentials.credentials

        # Verify the ID token using Firebase Admin SDK
        decoded_token = auth.verify_id_token(id_token)

        # Log successful authentication (without sensitive data)
        logger.info(f"User authenticated successfully: {decoded_token.get('uid')}")

        return decoded_token

    except auth.InvalidIdTokenError as e:
        logger.warning(f"Invalid ID token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except auth.ExpiredIdTokenError as e:
        logger.warning(f"Expired ID token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except auth.RevokedIdTokenError as e:
        logger.warning(f"Revoked ID token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication token has been revoked",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Unexpected error during token verification: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )