"""
Therapy API endpoints for <PERSON><PERSON>.

This module provides API endpoints for therapy-specific functionality including:
- Therapy session management
- Progress tracking and analytics
- Empowerment dashboard data
- Practice task management
- Mood tracking
"""

import logging
import os
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

# Add the darvis-ai-core directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "darvis-ai-core"))

from app.api.auth import get_current_user
from app.db.database import get_db
from app.db import crud
from app.db.models import User

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/therapy", tags=["Therapy"])


# Pydantic models for therapy API
class TherapySessionCreate(BaseModel):
    """Request model for creating a therapy session."""
    conversation_id: str = Field(..., description="Conversation ID for the therapy session")
    session_mode: str = Field("chat", description="Session mode: chat, voice, or mixed")
    session_focus: Optional[str] = Field(None, description="Focus area for the session")
    session_goals: Optional[List[str]] = Field(None, description="Goals for the session")


class TherapyProgressEntry(BaseModel):
    """Request model for creating therapy progress entries."""
    progress_type: str = Field(..., description="Type of progress: mood_entry, practice_task, insight, milestone")
    content: str = Field(..., description="Description of the progress entry")
    mood_score: Optional[int] = Field(None, ge=1, le=10, description="Mood score (1-10)")
    anxiety_level: Optional[int] = Field(None, ge=1, le=10, description="Anxiety level (1-10)")
    emotional_tags: Optional[List[str]] = Field(None, description="Emotional descriptors")
    task_name: Optional[str] = Field(None, description="Name of practice task")
    task_completed: Optional[bool] = Field(False, description="Whether task was completed")
    intervention_used: Optional[str] = Field(None, description="Therapeutic intervention used")
    user_feedback_rating: Optional[int] = Field(None, ge=1, le=5, description="User's rating of helpfulness")


class TaskCompletionUpdate(BaseModel):
    """Request model for updating task completion."""
    completed: bool = Field(..., description="Whether the task was completed")
    effectiveness_rating: Optional[int] = Field(None, ge=1, le=5, description="Effectiveness rating")
    notes: Optional[str] = Field(None, description="Additional notes about the task")


class TherapySessionResponse(BaseModel):
    """Response model for therapy sessions."""
    id: str
    conversation_id: str
    session_mode: str
    current_modality: str
    session_focus: Optional[str]
    session_goals: Optional[List[str]]
    started_at: datetime
    ended_at: Optional[datetime]
    session_duration_minutes: Optional[int]
    key_insights: Optional[List[str]]
    practice_tasks_assigned: Optional[List[str]]


class TherapyProgressResponse(BaseModel):
    """Response model for therapy progress entries."""
    id: str
    progress_type: str
    content: str
    mood_score: Optional[int]
    anxiety_level: Optional[int]
    emotional_tags: Optional[List[str]]
    task_name: Optional[str]
    task_completed: bool
    intervention_used: Optional[str]
    user_feedback_rating: Optional[int]
    created_at: datetime


class TherapyAnalyticsResponse(BaseModel):
    """Response model for therapy analytics."""
    total_entries: int
    average_mood: float
    task_completion_rate: float
    total_tasks: int
    completed_tasks: int
    intervention_effectiveness: Dict[str, float]
    period_days: int


# API Endpoints
@router.post("/sessions", response_model=TherapySessionResponse)
async def create_therapy_session(
    session_data: TherapySessionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new therapy session.
    
    This endpoint creates a new therapy session linked to a conversation,
    enabling therapy-specific tracking and context management.
    """
    try:
        # Create therapy session
        therapy_session = crud.create_therapy_session(
            db=db,
            conversation_id=session_data.conversation_id,
            user_id=current_user.id,
            session_mode=session_data.session_mode,
            session_focus=session_data.session_focus,
            session_goals=session_data.session_goals
        )
        
        logger.info(f"Created therapy session {therapy_session.id} for user {current_user.id}")
        
        return TherapySessionResponse(
            id=therapy_session.id,
            conversation_id=therapy_session.conversation_id,
            session_mode=therapy_session.session_mode,
            current_modality=therapy_session.current_modality,
            session_focus=therapy_session.session_focus,
            session_goals=therapy_session.session_goals,
            started_at=therapy_session.started_at,
            ended_at=therapy_session.ended_at,
            session_duration_minutes=therapy_session.session_duration_minutes,
            key_insights=therapy_session.key_insights,
            practice_tasks_assigned=therapy_session.practice_tasks_assigned
        )
        
    except Exception as e:
        logger.error(f"Error creating therapy session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create therapy session"
        )


@router.get("/sessions", response_model=List[TherapySessionResponse])
async def get_therapy_sessions(
    limit: int = 10,
    include_active: bool = True,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get therapy sessions for the current user.
    
    Returns a list of therapy sessions with optional filtering for active sessions.
    """
    try:
        sessions = crud.get_user_therapy_sessions(
            db=db,
            user_id=current_user.id,
            limit=limit,
            include_active=include_active
        )
        
        return [
            TherapySessionResponse(
                id=session.id,
                conversation_id=session.conversation_id,
                session_mode=session.session_mode,
                current_modality=session.current_modality,
                session_focus=session.session_focus,
                session_goals=session.session_goals,
                started_at=session.started_at,
                ended_at=session.ended_at,
                session_duration_minutes=session.session_duration_minutes,
                key_insights=session.key_insights,
                practice_tasks_assigned=session.practice_tasks_assigned
            )
            for session in sessions
        ]
        
    except Exception as e:
        logger.error(f"Error retrieving therapy sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve therapy sessions"
        )


@router.post("/progress", response_model=TherapyProgressResponse)
async def create_progress_entry(
    progress_data: TherapyProgressEntry,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new therapy progress entry.
    
    This endpoint allows users to log mood, complete practice tasks,
    record insights, and track therapeutic milestones.
    """
    try:
        progress_entry = crud.create_therapy_progress_entry(
            db=db,
            user_id=current_user.id,
            progress_type=progress_data.progress_type,
            content=progress_data.content,
            mood_score=progress_data.mood_score,
            anxiety_level=progress_data.anxiety_level,
            emotional_tags=progress_data.emotional_tags,
            task_name=progress_data.task_name,
            task_completed=progress_data.task_completed,
            intervention_used=progress_data.intervention_used,
            user_feedback_rating=progress_data.user_feedback_rating
        )
        
        logger.info(f"Created therapy progress entry {progress_entry.id} for user {current_user.id}")
        
        return TherapyProgressResponse(
            id=progress_entry.id,
            progress_type=progress_entry.progress_type,
            content=progress_entry.content,
            mood_score=progress_entry.mood_score,
            anxiety_level=progress_entry.anxiety_level,
            emotional_tags=progress_entry.emotional_tags,
            task_name=progress_entry.task_name,
            task_completed=progress_entry.task_completed,
            intervention_used=progress_entry.intervention_used,
            user_feedback_rating=progress_entry.user_feedback_rating,
            created_at=progress_entry.created_at
        )
        
    except Exception as e:
        logger.error(f"Error creating therapy progress entry: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create progress entry"
        )


@router.get("/progress", response_model=List[TherapyProgressResponse])
async def get_progress_entries(
    progress_type: Optional[str] = None,
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get therapy progress entries for the current user.
    
    Returns progress entries with optional filtering by type.
    """
    try:
        progress_entries = crud.get_user_therapy_progress(
            db=db,
            user_id=current_user.id,
            progress_type=progress_type,
            limit=limit
        )
        
        return [
            TherapyProgressResponse(
                id=entry.id,
                progress_type=entry.progress_type,
                content=entry.content,
                mood_score=entry.mood_score,
                anxiety_level=entry.anxiety_level,
                emotional_tags=entry.emotional_tags,
                task_name=entry.task_name,
                task_completed=entry.task_completed,
                intervention_used=entry.intervention_used,
                user_feedback_rating=entry.user_feedback_rating,
                created_at=entry.created_at
            )
            for entry in progress_entries
        ]
        
    except Exception as e:
        logger.error(f"Error retrieving therapy progress entries: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve progress entries"
        )


@router.put("/progress/{progress_id}/task-completion")
async def update_task_completion(
    progress_id: str,
    completion_data: TaskCompletionUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update task completion status and effectiveness rating.
    
    This endpoint allows users to mark practice tasks as completed
    and provide feedback on their effectiveness.
    """
    try:
        success = crud.update_therapy_progress_task_completion(
            db=db,
            progress_id=progress_id,
            completed=completion_data.completed,
            effectiveness_rating=completion_data.effectiveness_rating,
            notes=completion_data.notes
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Progress entry not found"
            )
        
        logger.info(f"Updated task completion for progress entry {progress_id}")
        
        return {"success": True, "message": "Task completion updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating task completion: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update task completion"
        )


@router.get("/analytics", response_model=TherapyAnalyticsResponse)
async def get_therapy_analytics(
    days: int = 30,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get therapy progress analytics for the empowerment dashboard.
    
    Returns comprehensive analytics including mood trends, task completion rates,
    and intervention effectiveness for the specified time period.
    """
    try:
        analytics = crud.get_therapy_progress_analytics(
            db=db,
            user_id=current_user.id,
            days=days
        )
        
        return TherapyAnalyticsResponse(
            total_entries=analytics["total_entries"],
            average_mood=analytics["average_mood"],
            task_completion_rate=analytics["task_completion_rate"],
            total_tasks=analytics["total_tasks"],
            completed_tasks=analytics["completed_tasks"],
            intervention_effectiveness=analytics["intervention_effectiveness"],
            period_days=analytics["period_days"]
        )
        
    except Exception as e:
        logger.error(f"Error retrieving therapy analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve therapy analytics"
        )
