# PRP-TOOL-001: Create Calendar Event

## Trigger
- User intent to schedule an event, meeting, or appointment
- Keywords: "schedule", "meeting", "appointment", "calendar", "event", "book", "plan"
- Time-related requests: "tomorrow at 3pm", "next Monday", "in 2 hours"

## Context
- User message containing event details
- Current date and time for relative time parsing
- User's timezone (if available)
- User's calendar access permissions

## Rules
1. **Extract Event Details**: Parse the user's message to identify:
   - **summary** (event title): The main purpose or title of the event
   - **start_time**: When the event begins (convert to ISO 8601 format)
   - **end_time**: When the event ends (default to 1 hour after start if not specified)
   - **description** (optional): Additional details about the event
   - **location** (optional): Where the event takes place

2. **Validate Required Information**: Ensure minimum required fields are present:
   - Event title/summary must be provided or inferred
   - Start time must be specified or reasonably inferred
   - If critical information is missing, ask the user for clarification

3. **Format Time Correctly**: Convert all time references to ISO 8601 format:
   - Handle relative times: "tomorrow", "next week", "in 2 hours"
   - Parse absolute times: "3pm", "2:30 PM", "14:30"
   - Default timezone to user's timezone or UTC if unknown

4. **Execute Tool Call**: Call the `create_calendar_event` tool with extracted parameters:
   ```json
   {
     "summary": "Meeting with team",
     "start_time": "2025-01-24T15:00:00Z",
     "end_time": "2025-01-24T16:00:00Z",
     "description": "Weekly team sync meeting",
     "location": "Conference Room A"
   }
   ```

5. **Handle Tool Response**: Process the result from the calendar tool:
   - **Success**: Confirm event creation with details (title, time, calendar link if available)
   - **Failure**: Explain the error and suggest solutions (e.g., authentication, permissions)

6. **Provide User Feedback**: Give clear confirmation or error messages:
   - Success: "✅ I've scheduled '[Event Title]' for [Date] at [Time]. The event has been added to your calendar."
   - Failure: "❌ I couldn't create the calendar event. [Specific error reason]. Would you like me to try again?"

## Example Dialogue

**User**: "Schedule a meeting with the marketing team tomorrow at 2 PM for 1 hour to discuss the Q1 campaign"

**AI**: "I'll schedule that meeting for you."

*[Tool Call: create_calendar_event]*
```json
{
  "summary": "Meeting with marketing team - Q1 campaign discussion",
  "start_time": "2025-01-25T14:00:00Z",
  "end_time": "2025-01-25T15:00:00Z",
  "description": "Discuss Q1 campaign strategy and planning",
  "location": ""
}
```

*[Tool Response: Success]*

**AI**: "✅ I've scheduled 'Meeting with marketing team - Q1 campaign discussion' for tomorrow (January 25th) at 2:00 PM - 3:00 PM. The event has been added to your Google Calendar."

## Output
- Clear confirmation of successful event creation with all relevant details
- Error message with specific reason and suggested next steps if creation fails
- Offer to modify or reschedule if the user wants to make changes

## Evaluation
- **Metric**: Successful calendar event creation and user satisfaction
- **Success Condition**: 
  1. The Composio tool returns a success status
  2. An event with correct details appears in the user's Google Calendar
  3. User receives clear confirmation with accurate event information
  4. All extracted time information is correctly formatted and timezone-aware
