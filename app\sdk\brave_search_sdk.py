"""
Brave Search SDK for <PERSON><PERSON>.

This module provides a unified interface for Brave Search API,
enabling real-time web search capabilities for up-to-date information.
"""

import os
import logging
from typing import Dict, Any, List, Optional
import httpx
import json

logger = logging.getLogger(__name__)

class BraveSearchSDK:
    """
    SDK for interacting with Brave Search API.
    
    Provides web search capabilities for retrieving up-to-date information
    with proper error handling, rate limiting, and response formatting.
    """
    
    def __init__(self):
        """Initialize the Brave Search SDK with API credentials."""
        self.api_key = os.getenv("BRAVE_SEARCH_API_KEY")
        self.base_url = "https://api.search.brave.com/res/v1/web/search"
        self.client = None
        
        if self.api_key:
            self._initialize_client()
        else:
            logger.warning("BRAVE_SEARCH_API_KEY not found. Search will use mock responses.")
    
    def _initialize_client(self):
        """Initialize the HTTP client for Brave Search."""
        try:
            self.client = httpx.Client(
                headers={
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip",
                    "X-Subscription-Token": self.api_key
                },
                timeout=30.0
            )
            logger.info("Brave Search client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Brave Search client: {e}")
            self.client = None
    
    def search(
        self,
        query: str,
        count: int = 5,
        offset: int = 0,
        country: str = "US",
        search_lang: str = "en",
        ui_lang: str = "en-US",
        safesearch: str = "moderate",
        freshness: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Perform a web search using Brave Search API.
        
        Args:
            query: Search query string
            count: Number of results to return (1-20, default: 5)
            offset: Number of results to skip (default: 0)
            country: Country code for localized results (default: "US")
            search_lang: Language for search (default: "en")
            ui_lang: UI language (default: "en-US")
            safesearch: Safe search setting (strict, moderate, off)
            freshness: Freshness filter (pd=past day, pw=past week, pm=past month, py=past year)
            
        Returns:
            Dict containing search results or error information
        """
        if not self.client:
            return self._mock_search_response(query)
        
        try:
            # Prepare search parameters
            params = {
                "q": query,
                "count": min(max(count, 1), 20),  # Ensure count is between 1-20
                "offset": max(offset, 0),
                "country": country,
                "search_lang": search_lang,
                "ui_lang": ui_lang,
                "safesearch": safesearch
            }
            
            # Add freshness filter if specified
            if freshness:
                params["freshness"] = freshness
            
            # Make the API request
            response = self.client.get(self.base_url, params=params)
            response.raise_for_status()
            
            # Parse the response
            data = response.json()
            
            # Extract relevant information
            results = []
            web_results = data.get("web", {}).get("results", [])
            
            for result in web_results:
                results.append({
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "description": result.get("description", ""),
                    "published": result.get("age", ""),
                    "snippet": result.get("description", "")
                })
            
            logger.info(f"Brave Search completed successfully for query: '{query}' - {len(results)} results")
            
            return {
                "success": True,
                "query": query,
                "results": results,
                "total_results": len(results),
                "search_metadata": {
                    "country": country,
                    "language": search_lang,
                    "safesearch": safesearch
                }
            }
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error in Brave Search: {e.response.status_code} - {e.response.text}")
            return {
                "success": False,
                "error": f"Search API error: {e.response.status_code}",
                "query": query,
                "results": []
            }
        except httpx.TimeoutException:
            logger.error(f"Timeout error in Brave Search for query: '{query}'")
            return {
                "success": False,
                "error": "Search request timed out",
                "query": query,
                "results": []
            }
        except Exception as e:
            logger.error(f"Unexpected error in Brave Search: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "results": []
            }
    
    def search_recent(
        self,
        query: str,
        count: int = 5,
        timeframe: str = "pw"  # pw=past week, pd=past day, pm=past month
    ) -> Dict[str, Any]:
        """
        Search for recent information with freshness filter.
        
        Args:
            query: Search query string
            count: Number of results to return (default: 5)
            timeframe: Time filter (pd, pw, pm, py)
            
        Returns:
            Dict containing recent search results
        """
        return self.search(
            query=query,
            count=count,
            freshness=timeframe
        )
    
    def format_search_results_for_ai(self, search_response: Dict[str, Any]) -> str:
        """
        Format search results for AI consumption.
        
        Args:
            search_response: Response from search() method
            
        Returns:
            Formatted string suitable for AI context
        """
        if not search_response.get("success"):
            return f"Search failed: {search_response.get('error', 'Unknown error')}"
        
        results = search_response.get("results", [])
        if not results:
            return f"No results found for query: '{search_response.get('query', '')}'"
        
        formatted_results = []
        formatted_results.append(f"Search results for '{search_response.get('query', '')}':")
        formatted_results.append("")
        
        for i, result in enumerate(results, 1):
            formatted_results.append(f"{i}. **{result.get('title', 'No title')}**")
            formatted_results.append(f"   URL: {result.get('url', 'No URL')}")
            formatted_results.append(f"   Description: {result.get('description', 'No description')}")
            if result.get('published'):
                formatted_results.append(f"   Published: {result.get('published')}")
            formatted_results.append("")
        
        return "\n".join(formatted_results)
    
    def _mock_search_response(self, query: str) -> Dict[str, Any]:
        """
        Generate a mock search response for development/testing.
        
        Args:
            query: Search query string
            
        Returns:
            Mock search response
        """
        logger.info(f"Using mock Brave Search response for query: '{query}' (no API key configured)")
        
        return {
            "success": True,
            "query": query,
            "results": [
                {
                    "title": f"Mock Result 1 for '{query}'",
                    "url": "https://example.com/result1",
                    "description": f"This is a mock search result for the query '{query}'. In a real implementation, this would contain actual search results from Brave Search API.",
                    "published": "2 hours ago",
                    "snippet": f"Mock content related to {query}..."
                },
                {
                    "title": f"Mock Result 2 for '{query}'",
                    "url": "https://example.com/result2", 
                    "description": f"Another mock search result providing information about '{query}'. This demonstrates the format of search results.",
                    "published": "1 day ago",
                    "snippet": f"Additional mock information about {query}..."
                }
            ],
            "total_results": 2,
            "search_metadata": {
                "country": "US",
                "language": "en",
                "safesearch": "moderate"
            }
        }
    
    def close(self):
        """Close the HTTP client."""
        if self.client:
            self.client.close()

# Create a global instance for easy import
brave_search = BraveSearchSDK()
