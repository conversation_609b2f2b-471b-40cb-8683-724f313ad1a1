from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import (
    SyncStatusCreate, SyncStatusUpdate, SyncStatusResponse
)

router = APIRouter(prefix="/api/v1/sync", tags=["Sync"])

@router.post("/status", response_model=SyncStatusResponse, status_code=status.HTTP_201_CREATED)
def create_sync_status(
    sync_data: SyncStatusCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create or update sync status for an entity.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Request Body**:
    - **entity_type**: Type of entity (note, task, calendar_event) - **Required**
    - **entity_id**: ID of the entity being synced - **Required**
    - **device_id**: Device identifier - **Required**
    - **sync_version**: Sync version number - Default: 1
    
    **Returns**:
    - **201 Created**: Sync status created/updated
    - **400 Bad Request**: Invalid sync data
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    # Validate entity type
    if sync_data.entity_type not in ["note", "task", "calendar_event"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid entity type. Must be 'note', 'task', or 'calendar_event'"
        )
    
    # Verify entity exists and belongs to user
    if sync_data.entity_type == "note":
        entity = crud.get_note_by_id(db=db, note_id=sync_data.entity_id, user_id=user_id)
    elif sync_data.entity_type == "task":
        entity = crud.get_task_by_id(db=db, task_id=sync_data.entity_id, user_id=user_id)
    elif sync_data.entity_type == "calendar_event":
        entity = crud.get_calendar_event_by_id(db=db, event_id=sync_data.entity_id, user_id=user_id)
    
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{sync_data.entity_type.replace('_', ' ').title()} not found"
        )
    
    try:
        sync_status = crud.create_sync_status(
            db=db,
            user_id=user_id,
            entity_type=sync_data.entity_type,
            entity_id=sync_data.entity_id,
            device_id=sync_data.device_id,
            sync_version=sync_data.sync_version or 1
        )
        
        return sync_status
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create sync status: {str(e)}"
        )

@router.get("/conflicts", response_model=List[SyncStatusResponse])
def get_sync_conflicts(
    device_id: str = Query(..., description="Device identifier"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all sync conflicts for a user and device.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Query Parameters**:
    - **device_id**: Device identifier - **Required**
    
    **Returns**:
    - **200 OK**: List of sync conflicts
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    try:
        conflicts = crud.get_sync_conflicts(db=db, user_id=user_id, device_id=device_id)
        return conflicts
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve sync conflicts: {str(e)}"
        )

@router.put("/conflicts/{sync_id}/resolve", response_model=SyncStatusResponse)
def resolve_sync_conflict(
    sync_id: str,
    resolution_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Resolve a sync conflict.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Path Parameters**:
    - **sync_id**: ID of the sync status to resolve
    
    **Request Body**: Resolution data containing the resolved state
    
    **Returns**:
    - **200 OK**: Conflict resolved successfully
    - **404 Not Found**: Sync status not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    try:
        resolved_sync = crud.resolve_sync_conflict(
            db=db,
            sync_id=sync_id,
            user_id=user_id,
            resolution_data=resolution_data
        )
        
        if not resolved_sync:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sync status not found"
            )
        
        return resolved_sync
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resolve sync conflict: {str(e)}"
        )

@router.get("/status/{entity_type}/{entity_id}")
def get_entity_sync_status(
    entity_type: str,
    entity_id: str,
    device_id: str = Query(..., description="Device identifier"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get sync status for a specific entity and device.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Path Parameters**:
    - **entity_type**: Type of entity (note, task, calendar_event)
    - **entity_id**: ID of the entity
    
    **Query Parameters**:
    - **device_id**: Device identifier - **Required**
    
    **Returns**:
    - **200 OK**: Sync status information
    - **404 Not Found**: Sync status not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    # Validate entity type
    if entity_type not in ["note", "task", "calendar_event"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid entity type. Must be 'note', 'task', or 'calendar_event'"
        )
    
    try:
        sync_status = db.query(crud.SyncStatus).filter(
            crud.and_(
                crud.SyncStatus.entity_type == entity_type,
                crud.SyncStatus.entity_id == entity_id,
                crud.SyncStatus.device_id == device_id,
                crud.SyncStatus.owner_id == user_id
            )
        ).first()
        
        if not sync_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sync status not found"
            )
        
        return sync_status
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve sync status: {str(e)}"
        )

@router.post("/batch", response_model=Dict[str, Any])
def batch_sync_operation(
    sync_operations: List[Dict[str, Any]],
    device_id: str = Query(..., description="Device identifier"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Perform batch sync operations for multiple entities.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Query Parameters**:
    - **device_id**: Device identifier - **Required**
    
    **Request Body**: Array of sync operations, each containing:
    - **operation**: Type of operation (create, update, delete)
    - **entity_type**: Type of entity (note, task, calendar_event)
    - **entity_id**: ID of the entity
    - **entity_data**: Entity data (for create/update operations)
    - **sync_version**: Sync version number
    
    **Returns**:
    - **200 OK**: Batch operation results
    - **400 Bad Request**: Invalid operation data
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    results = {
        "successful": [],
        "failed": [],
        "conflicts": []
    }
    
    try:
        for operation in sync_operations:
            try:
                op_type = operation.get("operation")
                entity_type = operation.get("entity_type")
                entity_id = operation.get("entity_id")
                entity_data = operation.get("entity_data", {})
                sync_version = operation.get("sync_version", 1)
                
                # Validate operation
                if op_type not in ["create", "update", "delete"]:
                    results["failed"].append({
                        "entity_id": entity_id,
                        "error": "Invalid operation type"
                    })
                    continue
                
                if entity_type not in ["note", "task", "calendar_event"]:
                    results["failed"].append({
                        "entity_id": entity_id,
                        "error": "Invalid entity type"
                    })
                    continue
                
                # Process the operation
                if op_type == "create":
                    # Create entity and sync status
                    if entity_type == "note":
                        entity = crud.create_user_note(db=db, user_id=user_id, **entity_data)
                    elif entity_type == "task":
                        entity = crud.create_user_task(db=db, user_id=user_id, **entity_data)
                    elif entity_type == "calendar_event":
                        entity = crud.create_calendar_event(db=db, user_id=user_id, **entity_data)
                    
                    # Create sync status
                    crud.create_sync_status(
                        db=db,
                        user_id=user_id,
                        entity_type=entity_type,
                        entity_id=entity.id,
                        device_id=device_id,
                        sync_version=sync_version
                    )
                    
                    results["successful"].append({
                        "operation": op_type,
                        "entity_type": entity_type,
                        "entity_id": entity.id
                    })
                
                elif op_type == "update":
                    # Update entity and sync status
                    if entity_type == "note":
                        entity = crud.update_note(db=db, note_id=entity_id, user_id=user_id, note_data=entity_data)
                    elif entity_type == "task":
                        entity = crud.update_task(db=db, task_id=entity_id, user_id=user_id, task_data=entity_data)
                    elif entity_type == "calendar_event":
                        entity = crud.update_calendar_event(db=db, event_id=entity_id, user_id=user_id, event_data=entity_data)
                    
                    if entity:
                        # Update sync status
                        crud.create_sync_status(
                            db=db,
                            user_id=user_id,
                            entity_type=entity_type,
                            entity_id=entity_id,
                            device_id=device_id,
                            sync_version=sync_version
                        )
                        
                        results["successful"].append({
                            "operation": op_type,
                            "entity_type": entity_type,
                            "entity_id": entity_id
                        })
                    else:
                        results["failed"].append({
                            "entity_id": entity_id,
                            "error": "Entity not found"
                        })
                
                elif op_type == "delete":
                    # Delete entity
                    success = False
                    if entity_type == "note":
                        success = crud.delete_note(db=db, note_id=entity_id, user_id=user_id)
                    elif entity_type == "task":
                        success = crud.delete_task(db=db, task_id=entity_id, user_id=user_id)
                    elif entity_type == "calendar_event":
                        success = crud.delete_calendar_event(db=db, event_id=entity_id, user_id=user_id)
                    
                    if success:
                        results["successful"].append({
                            "operation": op_type,
                            "entity_type": entity_type,
                            "entity_id": entity_id
                        })
                    else:
                        results["failed"].append({
                            "entity_id": entity_id,
                            "error": "Entity not found"
                        })
                
            except Exception as e:
                results["failed"].append({
                    "entity_id": operation.get("entity_id", "unknown"),
                    "error": str(e)
                })
        
        return results
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process batch sync operations: {str(e)}"
        )
