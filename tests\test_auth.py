from fastapi.testclient import TestClient
from app.main import app
from app.api.auth import get_current_user
import pytest

client = TestClient(app)

# This is a mock function that will override our real dependency
def override_get_current_user():
    return {"uid": "test_user_123", "email": "<EMAIL>"}

# Tell our app to use the mock function instead of the real one during this test
app.dependency_overrides[get_current_user] = override_get_current_user

def test_get_me_unauthenticated():
    """
    Tests that accessing the protected endpoint without a token fails with 401.
    We need to temporarily remove the override for this test.
    """
    # Temporarily remove the override
    app.dependency_overrides.pop(get_current_user, None)
    
    response = client.get("/api/v1/users/me")
    assert response.status_code == 401  # Unauthorized
    
    # Restore the override for other tests
    app.dependency_overrides[get_current_user] = override_get_current_user


def test_get_me_authenticated():
    """
    Tests that a user can access the protected endpoint when the dependency is overridden.
    """
    response = client.get("/api/v1/users/me")
    assert response.status_code == 200
    assert response.json()["uid"] == "test_user_123"

# Cleanup after tests are done
@pytest.fixture(autouse=True, scope="module")
def cleanup_dependency_overrides():
    yield
    # Clear the overrides after all tests in this module have run
    app.dependency_overrides.clear()
