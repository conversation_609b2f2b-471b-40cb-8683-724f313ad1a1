"""
Pronunciations API for Darvis.

This module provides CRUD endpoints for user-defined pronunciations that allow users
to customize how specific words are pronounced by the TTS system.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Dict, Any, List

from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import (
    PronunciationCreate,
    PronunciationUpdate,
    PronunciationResponse,
    PronunciationListResponse
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/pronunciations", tags=["Pronunciations"])

@router.post("/", response_model=PronunciationResponse)
async def create_pronunciation(
    pronunciation: PronunciationCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new pronunciation customization.
    
    Users can define how specific words should be pronounced by the TTS system
    using phonetic representations or SSML markup.
    """
    try:
        user_id = current_user["uid"]
        
        # Check if pronunciation for this word already exists for this user
        existing_pronunciation = crud.get_pronunciation_by_word(
            db, user_id, pronunciation.word_to_replace
        )
        if existing_pronunciation:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="A pronunciation for this word already exists"
            )
        
        # Create the pronunciation
        db_pronunciation = crud.create_pronunciation(
            db=db,
            user_id=user_id,
            word_to_replace=pronunciation.word_to_replace,
            phonetic_pronunciation=pronunciation.phonetic_pronunciation
        )
        
        logger.info(f"Created pronunciation for '{pronunciation.word_to_replace}' for user {user_id}")
        return db_pronunciation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating pronunciation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create pronunciation"
        )

@router.get("/", response_model=PronunciationListResponse)
async def get_pronunciations(
    skip: int = Query(0, ge=0, description="Number of pronunciations to skip"),
    limit: int = Query(100, ge=1, le=100, description="Number of pronunciations to return"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all pronunciations for the authenticated user with pagination.
    """
    try:
        user_id = current_user["uid"]
        
        # Get pronunciations with pagination
        pronunciations = crud.get_user_pronunciations(db, user_id, skip, limit)
        total = crud.count_user_pronunciations(db, user_id)
        
        return PronunciationListResponse(pronunciations=pronunciations, total=total)
        
    except Exception as e:
        logger.error(f"Error fetching pronunciations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch pronunciations"
        )

@router.get("/{pronunciation_id}", response_model=PronunciationResponse)
async def get_pronunciation(
    pronunciation_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific pronunciation by ID.
    """
    try:
        user_id = current_user["uid"]
        
        pronunciation = crud.get_pronunciation_by_id(db, pronunciation_id, user_id)
        if not pronunciation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Pronunciation not found"
            )
        
        return pronunciation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching pronunciation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch pronunciation"
        )

@router.put("/{pronunciation_id}", response_model=PronunciationResponse)
async def update_pronunciation(
    pronunciation_id: str,
    pronunciation_update: PronunciationUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a specific pronunciation.
    """
    try:
        user_id = current_user["uid"]
        
        # Check if pronunciation exists
        existing_pronunciation = crud.get_pronunciation_by_id(db, pronunciation_id, user_id)
        if not existing_pronunciation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Pronunciation not found"
            )
        
        # If updating word, check for conflicts
        if pronunciation_update.word_to_replace:
            conflicting_pronunciation = crud.get_pronunciation_by_word(
                db, user_id, pronunciation_update.word_to_replace
            )
            if conflicting_pronunciation and conflicting_pronunciation.id != pronunciation_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="A pronunciation for this word already exists"
                )
        
        # Update the pronunciation
        updated_pronunciation = crud.update_pronunciation(
            db=db,
            pronunciation_id=pronunciation_id,
            user_id=user_id,
            word_to_replace=pronunciation_update.word_to_replace,
            phonetic_pronunciation=pronunciation_update.phonetic_pronunciation
        )
        
        if not updated_pronunciation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Pronunciation not found"
            )
        
        logger.info(f"Updated pronunciation {pronunciation_id} for user {user_id}")
        return updated_pronunciation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating pronunciation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update pronunciation"
        )

@router.delete("/{pronunciation_id}")
async def delete_pronunciation(
    pronunciation_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a specific pronunciation.
    """
    try:
        user_id = current_user["uid"]
        
        success = crud.delete_pronunciation(db, pronunciation_id, user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Pronunciation not found"
            )
        
        logger.info(f"Deleted pronunciation {pronunciation_id} for user {user_id}")
        return {"message": "Pronunciation deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting pronunciation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete pronunciation"
        )

@router.get("/word/{word}", response_model=PronunciationResponse)
async def get_pronunciation_by_word(
    word: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a pronunciation by word (for internal use by the TTS system).
    """
    try:
        user_id = current_user["uid"]
        
        pronunciation = crud.get_pronunciation_by_word(db, user_id, word)
        if not pronunciation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Pronunciation not found"
            )
        
        return pronunciation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching pronunciation by word: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch pronunciation"
        )

@router.get("/dict/all")
async def get_pronunciations_dict(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all pronunciations as a dictionary for efficient TTS processing.
    Returns a mapping of word -> phonetic_pronunciation.
    """
    try:
        user_id = current_user["uid"]
        
        pronunciations_dict = crud.get_all_user_pronunciations_dict(db, user_id)
        
        return {"pronunciations": pronunciations_dict}
        
    except Exception as e:
        logger.error(f"Error fetching pronunciations dictionary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch pronunciations dictionary"
        )
