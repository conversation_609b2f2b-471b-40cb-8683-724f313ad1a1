"""
Configuration settings for <PERSON>vis Backend.

This module centralizes all configuration settings using Pydantic for validation
and python-dotenv for environment variable loading.
"""
import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Settings(BaseSettings):
    """Application settings with validation."""

    # =============================================================================
    # DATABASE CONFIGURATION
    # =============================================================================
    database_url: str = Field(..., env="DATABASE_URL")
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")

    # =============================================================================
    # CLOUDINARY CONFIGURATION
    # =============================================================================
    cloudinary_cloud_name: str = Field(..., env="CLOUDINARY_CLOUD_NAME")
    cloudinary_api_key: str = Field(..., env="CLOUDINARY_API_KEY")
    cloudinary_api_secret: str = Field(..., env="CLOUDINARY_API_SECRET")

    # =============================================================================
    # FIREBASE CONFIGURATION
    # =============================================================================
    google_application_credentials: Optional[str] = Field(default=None, env="GOOGLE_APPLICATION_CREDENTIALS")
    google_application_credentials_json: Optional[str] = Field(default=None, env="GOOGLE_APPLICATION_CREDENTIALS_JSON")

    # =============================================================================
    # AI PROVIDER CONFIGURATION
    # =============================================================================
    groq_api_key: Optional[str] = Field(default=None, env="GROQ_API_KEY")
    voyage_api_key: Optional[str] = Field(default=None, env="VOYAGE_API_KEY")
    deepinfra_api_key: Optional[str] = Field(default=None, env="DEEPINFRA_API_KEY")
    brave_search_api_key: Optional[str] = Field(default=None, env="BRAVE_SEARCH_API_KEY")
    composio_api_key: Optional[str] = Field(default=None, env="COMPOSIO_API_KEY")

    # =============================================================================
    # LIVEKIT CONFIGURATION
    # =============================================================================
    livekit_api_key: Optional[str] = Field(default=None, env="LIVEKIT_API_KEY")
    livekit_api_secret: Optional[str] = Field(default=None, env="LIVEKIT_API_SECRET")
    livekit_url: str = Field(default="wss://darvis.livekit.cloud", env="LIVEKIT_URL")

    # =============================================================================
    # DEVELOPMENT SETTINGS
    # =============================================================================
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")

    # =============================================================================
    # SECURITY SETTINGS
    # =============================================================================
    secret_key: str = Field(default="your_super_secure_secret_key_here", env="SECRET_KEY")
    cors_origins: str = Field(default="http://localhost:3000,http://localhost:8080", env="CORS_ORIGINS")
    max_upload_size: int = Field(default=10, env="MAX_UPLOAD_SIZE")

    # =============================================================================
    # OPTIONAL CONFIGURATIONS
    # =============================================================================
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    appinsights_instrumentationkey: Optional[str] = Field(default=None, env="APPINSIGHTS_INSTRUMENTATIONKEY")

    # =============================================================================
    # CELERY CONFIGURATION (Background Tasks)
    # =============================================================================
    celery_broker_url: str = Field(default="redis://localhost:6379", env="CELERY_BROKER_URL")
    celery_result_backend: str = Field(default="redis://localhost:6379", env="CELERY_RESULT_BACKEND")

    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        case_sensitive = False


# Create global settings instance
settings = Settings()
