"""
Database migration script for Phase 2 features.

This script adds all the new tables and columns required for:
- Enhanced Notes (rich text, categories, templates, sharing)
- Enhanced Tasks (subtasks, time tracking, dependencies, templates)
- Calendar Events (complete calendar system)
- Attachments (file upload support)
- Sync Status (cross-device synchronization)

Run this script to upgrade the database schema for Phase 2.
"""

import os
import sys
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.core.config import settings
from app.db.models import Base

def run_migration():
    """Run the Phase 2 database migration"""
    
    # Create database engine
    engine = create_engine(settings.database_url)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    try:
        print("🚀 Starting Phase 2 Database Migration...")
        
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()
        print(f"📊 Found {len(existing_tables)} existing tables")
        
        # --- 1) Add new columns in a guarded way, commit after this block ---
        print("\n📝 Adding new columns to existing tables...")
        try:
            # Enhanced Notes columns
            if 'notes' in existing_tables:
                existing_columns = [c['name'] for c in inspector.get_columns('notes')]
                
                notes_new_columns = [
                    ("content_type", "VARCHAR DEFAULT 'plain'"),
                    ("content_data", "JSON"),
                    ("category", "VARCHAR"),
                    ("is_template", "BOOLEAN DEFAULT FALSE"),
                    ("is_shared", "BOOLEAN DEFAULT FALSE"),
                    ("shared_with", "JSON DEFAULT '[]'")
                ]
                
                for column_name, column_def in notes_new_columns:
                    if column_name not in existing_columns:
                        try:
                            session.execute(text(f"ALTER TABLE notes ADD COLUMN {column_name} {column_def}"))
                            print(f"  ✅ Added notes.{column_name}")
                        except Exception as e:
                            print(f"  ⚠️  Failed to add notes.{column_name}: {e}")
            
            # Enhanced Tasks columns
            if 'tasks' in existing_tables:
                existing_columns = [col['name'] for col in inspector.get_columns('tasks')]
                
                tasks_new_columns = [
                    ("parent_task_id", "VARCHAR"),
                    ("estimated_duration_minutes", "INTEGER"),
                    ("actual_duration_minutes", "INTEGER"),
                    ("time_tracking_started_at", "TIMESTAMP WITH TIME ZONE"),
                    ("depends_on_task_ids", "JSON DEFAULT '[]'"),
                    ("is_template", "BOOLEAN DEFAULT FALSE"),
                    ("template_data", "JSON"),
                    ("assigned_to_user_id", "VARCHAR"),
                    ("assigned_by_user_id", "VARCHAR")
                ]
                
                for column_name, column_def in tasks_new_columns:
                    if column_name not in existing_columns:
                        try:
                            session.execute(text(f"ALTER TABLE tasks ADD COLUMN {column_name} {column_def}"))
                            print(f"  ✅ Added tasks.{column_name}")
                        except Exception as e:
                            print(f"  ⚠️  Failed to add tasks.{column_name}: {e}")
            
            session.commit()
        except Exception as e:
            session.rollback()
            print(f"  ❌ Error adding columns block: {e}")
        
        # --- 2) Add foreign keys / constraints only if missing, commit after ---
        print("\n🔗 Adding foreign key constraints (if missing)...")
        try:
            fk_names = {fk['name'] for fk in inspector.get_foreign_keys('tasks')}
            # parent_task fk
            if 'fk_tasks_parent_task' not in fk_names:
                try:
                    session.execute(text(""" 
                        ALTER TABLE tasks 
                        ADD CONSTRAINT fk_tasks_parent_task 
                        FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE CASCADE
                    """))
                    print("  ✅ Added parent_task foreign key constraint")
                except Exception as e:
                    print(f"  ⚠️  Failed to add parent_task foreign key: {e}")
                    session.rollback()
            # assigned_to / assigned_by fks (guarded similarly)
            fk_names = {fk['name'] for fk in inspector.get_foreign_keys('tasks')}
            if 'fk_tasks_assigned_to' not in fk_names:
                try:
                    session.execute(text(""" 
                        ALTER TABLE tasks 
                        ADD CONSTRAINT fk_tasks_assigned_to 
                        FOREIGN KEY (assigned_to_user_id) REFERENCES users(id) ON DELETE SET NULL
                    """))
                    print("  ✅ Added assigned_to foreign key constraint")
                except Exception as e:
                    print(f"  ⚠️  Failed to add assigned_to foreign key: {e}")
                    session.rollback()
            if 'fk_tasks_assigned_by' not in fk_names:
                try:
                    session.execute(text(""" 
                        ALTER TABLE tasks 
                        ADD CONSTRAINT fk_tasks_assigned_by 
                        FOREIGN KEY (assigned_by_user_id) REFERENCES users(id) ON DELETE SET NULL
                    """))
                    print("  ✅ Added assigned_by foreign key constraint")
                except Exception as e:
                    print(f"  ⚠️  Failed to add assigned_by foreign key: {e}")
                    session.rollback()
            session.commit()
        except Exception as e:
            session.rollback()
            print(f"  ❌ Error adding FK constraints block: {e}")
        
        # --- 3) Create new tables, commit after table creation ---
        print("\n🏗️  Creating new tables...")
        try:
            # Calendar Events table
            if 'calendar_events' not in existing_tables:
                try:
                    session.execute(text("""
                        CREATE TABLE calendar_events (
                            id VARCHAR PRIMARY KEY,
                            owner_id VARCHAR NOT NULL,
                            start_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
                            end_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
                            is_all_day BOOLEAN DEFAULT FALSE,
                            timezone VARCHAR DEFAULT 'UTC',
                            is_recurring BOOLEAN DEFAULT FALSE,
                            recurrence_rule VARCHAR,
                            recurrence_end_date TIMESTAMP WITH TIME ZONE,
                            category VARCHAR,
                            tags JSON DEFAULT '[]',
                            color VARCHAR,
                            location VARCHAR,
                            location_data JSON,
                            reminder_minutes_before JSON DEFAULT '[]',
                            external_event_id VARCHAR,
                            external_calendar_id VARCHAR,
                            sync_status VARCHAR DEFAULT 'local',
                            attendees JSON DEFAULT '[]',
                            created_by_user_id VARCHAR,
                            owner_id VARCHAR NOT NULL,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        )
                    """))
                    print("  ✅ Created calendar_events table")
                except Exception as e:
                    print(f"  ⚠️  Failed to create calendar_events: {e}")
            
            # Attachments table
            if 'attachments' not in existing_tables:
                try:
                    session.execute(text("""
                        CREATE TABLE attachments (
                            id VARCHAR PRIMARY KEY,
                            filename VARCHAR NOT NULL,
                            original_filename VARCHAR NOT NULL,
                            file_size INTEGER NOT NULL,
                            mime_type VARCHAR NOT NULL,
                            storage_provider VARCHAR DEFAULT 'cloudinary',
                            storage_url VARCHAR NOT NULL,
                            storage_public_id VARCHAR,
                            note_id VARCHAR,
                            task_id VARCHAR,
                            calendar_event_id VARCHAR,
                            owner_id VARCHAR NOT NULL,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        )
                    """))
                    print("  ✅ Created attachments table")
                except Exception as e:
                    print(f"  ⚠️  Failed to create attachments: {e}")
            
            # Sync Status table
            if 'sync_status' not in existing_tables:
                try:
                    session.execute(text("""
                        CREATE TABLE sync_status (
                            id VARCHAR PRIMARY KEY,
                            entity_type VARCHAR NOT NULL,
                            entity_id VARCHAR NOT NULL,
                            device_id VARCHAR NOT NULL,
                            last_sync_at TIMESTAMP WITH TIME ZONE NOT NULL,
                            sync_version INTEGER DEFAULT 1,
                            has_conflicts BOOLEAN DEFAULT FALSE,
                            conflict_data JSON,
                            resolved_at TIMESTAMP WITH TIME ZONE,
                            sync_status VARCHAR DEFAULT 'pending',
                            error_message VARCHAR,
                            owner_id VARCHAR NOT NULL,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                            updated_at TIMESTAMP WITH TIME ZONE
                        )
                    """))
                    print("  ✅ Created sync_status table")
                except Exception as e:
                    print(f"  ⚠️  Failed to create sync_status: {e}")
            
            session.commit()
        except Exception as e:
            session.rollback()
            print(f"  ❌ Error creating tables block: {e}")
        
        # --- 4) Create indexes, each index in its own try/commit to avoid abort cascade ---
        print("\n📈 Creating indexes for performance...")
        indexes = [
            ("idx_calendar_events_owner_start", "CREATE INDEX IF NOT EXISTS idx_calendar_events_owner_start ON calendar_events(owner_id, start_datetime)"),
            ("idx_calendar_events_category", "CREATE INDEX IF NOT EXISTS idx_calendar_events_category ON calendar_events(category)"),
            ("idx_attachments_owner", "CREATE INDEX IF NOT EXISTS idx_attachments_owner ON attachments(owner_id)"),
            ("idx_attachments_note", "CREATE INDEX IF NOT EXISTS idx_attachments_note ON attachments(note_id)"),
            ("idx_attachments_task", "CREATE INDEX IF NOT EXISTS idx_attachments_task ON attachments(task_id)"),
            ("idx_attachments_calendar_event", "CREATE INDEX IF NOT EXISTS idx_attachments_calendar_event ON attachments(calendar_event_id)"),
            ("idx_sync_status_entity", "CREATE INDEX IF NOT EXISTS idx_sync_status_entity ON sync_status(entity_type, entity_id)"),
            ("idx_sync_status_device", "CREATE INDEX IF NOT EXISTS idx_sync_status_device ON sync_status(device_id)"),
            ("idx_tasks_parent", "CREATE INDEX IF NOT EXISTS idx_tasks_parent ON tasks(parent_task_id)"),
            ("idx_tasks_assigned_to", "CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON tasks(assigned_to_user_id)"),
            ("idx_notes_category", "CREATE INDEX IF NOT EXISTS idx_notes_category ON notes(category)"),
            ("idx_notes_template", "CREATE INDEX IF NOT EXISTS idx_notes_template ON notes(is_template)"),
            ("idx_tasks_template", "CREATE INDEX IF NOT EXISTS idx_tasks_template ON tasks(is_template)")
        ]
        for index_name, index_sql in indexes:
            try:
                session.execute(text(index_sql))
                session.commit()
                print(f"  ✅ Created {index_name}")
            except Exception as e:
                session.rollback()
                print(f"  ⚠️  Failed to create {index_name}: {e}")
        
        print("\n🎉 Phase 2 Migration completed successfully!")
        print("\n📋 Summary of changes:")
        print("  • Enhanced Notes with rich text, categories, templates, and sharing")
        print("  • Enhanced Tasks with subtasks, time tracking, dependencies, and templates")
        print("  • New Calendar Events system with full event management")
        print("  • New Attachments system for file uploads")
        print("  • New Sync Status system for cross-device synchronization")
        print("  • Added performance indexes for all new features")
        
        # Verify new tables exist
        inspector = inspect(engine)
        new_tables = inspector.get_table_names()
        print(f"\n✅ Database now has {len(new_tables)} tables")
        
        required_new_tables = ['calendar_events', 'attachments', 'sync_status']
        for table in required_new_tables:
            if table in new_tables:
                print(f"  ✅ {table} table created successfully")
            else:
                print(f"  ❌ {table} table missing!")
        
    except Exception as e:
        session.rollback()
        print(f"\n❌ Migration failed: {e}")
        raise
    
    finally:
        session.close()

if __name__ == "__main__":
    run_migration()
