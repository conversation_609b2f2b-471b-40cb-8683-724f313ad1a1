"""
Contact Management API for <PERSON>vis.

This module provides CRUD endpoints for managing user contacts with detailed
information including social media, meeting context, and AI memory prompts.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional

from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import (
    ContactCreate,
    ContactUpdate,
    ContactResponse,
    ContactListResponse
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/contacts", tags=["Contacts"])

@router.post("/", response_model=ContactResponse, status_code=status.HTTP_201_CREATED)
async def create_contact(
    contact: ContactCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new contact for the authenticated user with comprehensive contact management.
    
    This endpoint allows users to store detailed contact information including social media
    handles, meeting context, and AI memory prompts for personalized interactions.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Request Body**:
    - **name**: Contact's full name (1-200 characters) - **Required**
    - **phone**: Phone number (optional, max 50 characters)
    - **met_at**: Context about where/how you met (optional, max 500 characters)
    - **social_media**: Dictionary of social media handles (optional)
    - **memory_prompt**: AI memory prompt about this person (optional, max 2000 characters)
    
    **Returns**:
    - **201 Created**: Contact successfully created with full contact details
    - **400 Bad Request**: Invalid input data (name too long, invalid format, etc.)
    - **401 Unauthorized**: Invalid or expired authentication token
    
    **Features**:
    - Automatic contact ID generation
    - User ownership assignment
    - Timestamp tracking (created_at, updated_at)
    - Social media integration
    - AI memory prompts for personalized interactions
    """
    try:
        user_id = current_user["uid"]
        
        # Create contact with all provided information
        db_contact = crud.create_contact(
            db=db,
            user_id=user_id,
            name=contact.name,
            phone=contact.phone,
            met_at=contact.met_at,
            social_media=contact.social_media,
            memory_prompt=contact.memory_prompt
        )
        
        logger.info(f"Created contact {db_contact.id} for user {user_id}")
        return db_contact
        
    except Exception as e:
        logger.error(f"Error creating contact for user {current_user.get('uid')}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create contact"
        )

@router.get("/", response_model=ContactListResponse)
async def get_contacts(
    skip: int = Query(0, ge=0, description="Number of contacts to skip"),
    limit: int = Query(100, ge=1, le=100, description="Number of contacts to return"),
    search: Optional[str] = Query(None, description="Search in name, phone, met_at, and memory_prompt"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retrieve contacts for the authenticated user with search and pagination capabilities.
    
    This endpoint provides comprehensive contact management with search functionality
    across all contact fields and intelligent pagination for large contact lists.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Query Parameters**:
    - **skip**: Number of contacts to skip for pagination (default: 0)
    - **limit**: Maximum contacts to return (1-100, default: 100)
    - **search**: Search query across name, phone, met_at, and memory_prompt fields
    
    **Returns**:
    - **200 OK**: List of contacts with total count
    - **401 Unauthorized**: Invalid or expired authentication token
    
    **Response includes**:
    - Array of contact objects with full details
    - Total count of matching contacts (for pagination)
    - Contacts ordered alphabetically by name
    """
    try:
        user_id = current_user["uid"]
        
        # Get contacts with search and pagination
        contacts = crud.get_user_contacts(db, user_id, skip, limit, search)
        total = crud.count_user_contacts(db, user_id)
        
        return ContactListResponse(contacts=contacts, total=total)
        
    except Exception as e:
        logger.error(f"Error retrieving contacts for user {current_user.get('uid')}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve contacts"
        )

@router.get("/{contact_id}", response_model=ContactResponse)
async def get_contact(
    contact_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retrieve a specific contact by ID.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Returns**:
    - **200 OK**: Contact details
    - **404 Not Found**: Contact not found or doesn't belong to user
    - **401 Unauthorized**: Invalid authentication token
    """
    user_id = current_user["uid"]
    db_contact = crud.get_contact_by_id(db=db, contact_id=contact_id, user_id=user_id)
    
    if db_contact is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contact not found"
        )
    
    return db_contact

@router.put("/{contact_id}", response_model=ContactResponse)
async def update_contact(
    contact_id: str,
    contact_update: ContactUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a specific contact's information.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Returns**:
    - **200 OK**: Updated contact details
    - **404 Not Found**: Contact not found or doesn't belong to user
    - **400 Bad Request**: No fields to update
    - **401 Unauthorized**: Invalid authentication token
    """
    user_id = current_user["uid"]
    
    # Convert Pydantic model to dict, excluding unset fields
    update_data = contact_update.model_dump(exclude_unset=True)
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No fields to update"
        )
    
    db_contact = crud.update_contact(db=db, contact_id=contact_id, user_id=user_id, contact_data=update_data)
    if db_contact is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contact not found"
        )
    
    logger.info(f"Updated contact {contact_id} for user {user_id}")
    return db_contact

@router.delete("/{contact_id}")
async def delete_contact(
    contact_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a specific contact.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Returns**:
    - **200 OK**: Contact successfully deleted
    - **404 Not Found**: Contact not found or doesn't belong to user
    - **401 Unauthorized**: Invalid authentication token
    """
    user_id = current_user["uid"]
    
    success = crud.delete_contact(db=db, contact_id=contact_id, user_id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contact not found"
        )
    
    logger.info(f"Deleted contact {contact_id} for user {user_id}")
    return {"message": "Contact deleted successfully"}
