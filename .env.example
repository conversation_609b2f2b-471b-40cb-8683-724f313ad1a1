# Darvis Backend Environment Configuration Template
# Copy this file to .env and fill in your actual values
# DO NOT commit the .env file to version control

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database URL
# Format: postgresql://username:password@host:port/database_name
# Example: postgresql://darvis_user:secure_password@localhost:5432/darvis_db
# DATABASE_URL=postgresql://user:password@localhost:5432/darvis_db

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis URL for caching and session management
# Format: redis://username:password@host:port
# For local development: redis://localhost:6379
# REDIS_URL=redis://localhost:6379

DATABASE_URL=*********************************************************************************************/darvis-postgres

REDIS_URL=redis://default:tnoitnwionwt4208208WAEnoernourquob224924@darvisapp-darvisredis-xz7cbw:6379

# =============================================================================
# CELERY CONFIGURATION (Background Tasks)
# =============================================================================

# Celery broker URL (usually same as Redis URL)
CELERY_BROKER_URL=redis://localhost:6379

# Celery result backend (usually same as Redis URL)
CELERY_RESULT_BACKEND=redis://localhost:6379

# =============================================================================
# FIREBASE AUTHENTICATION
# =============================================================================

# Path to Firebase service account JSON file
# Download from Firebase Console > Project Settings > Service Accounts
GOOGLE_APPLICATION_CREDENTIALS=/path/to/firebase-service-account.json

# Alternative: Firebase service account JSON as environment variable
# Paste the entire JSON content here (useful for Docker/cloud deployments)
GOOGLE_APPLICATION_CREDENTIALS_JSON=

# =============================================================================
# LIVEKIT VOICE AI CONFIGURATION
# =============================================================================

# LiveKit API credentials for voice AI sessions
# Get these from your LiveKit Cloud dashboard
LIVEKIT_API_KEY=your_livekit_api_key_here
LIVEKIT_API_SECRET=your_livekit_api_secret_here

# LiveKit WebSocket URL
# For LiveKit Cloud: wss://your-project.livekit.cloud
# For self-hosted: wss://your-livekit-server.com
LIVEKIT_URL=wss://darvis.livekit.cloud

# =============================================================================
# AI MODEL PROVIDERS
# =============================================================================

# Groq API Key for fast LLM inference and STT (Speech-to-Text)
# Get from: https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key_here

# Voyage AI API Key for state-of-the-art embeddings (voyage-3-large)
# Get from: https://dash.voyageai.com/api-keys
VOYAGE_API_KEY=your_voyage_api_key_here

# DeepInfra API Key for TTS (Text-to-Speech) with Kokoro model
# Get from: https://deepinfra.com/dash/api_keys
DEEPINFRA_API_KEY=your_deepinfra_api_key_here
# Web Search Integration
BRAVE_SEARCH_API_KEY=your_brave_search_api_key_here

# =============================================================================
# TOOL INTEGRATIONS
# =============================================================================

# Composio API Key for external tool integrations (Google Calendar, Gmail, etc.)
# Get from: https://app.composio.dev/settings
COMPOSIO_API_KEY=your_composio_api_key_here

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Environment mode (development, staging, production)
ENVIRONMENT=development

# Enable debug logging (true/false)
DEBUG=true

# API host and port for development
HOST=0.0.0.0
PORT=8000

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Secret key for session management and JWT tokens
# Generate a secure random string for production
SECRET_KEY=your_super_secure_secret_key_here

# CORS allowed origins (comma-separated)
# For development: http://localhost:3000,http://localhost:8080
# For production: https://yourdomain.com
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# =============================================================================
# OPTIONAL CONFIGURATIONS
# =============================================================================

# Sentry DSN for error tracking (optional)
SENTRY_DSN=

# Application insights key (optional)
APPINSIGHTS_INSTRUMENTATIONKEY=

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Maximum file upload size in MB
MAX_UPLOAD_SIZE=10

# =============================================================================
# EXAMPLE VALUES FOR QUICK SETUP
# =============================================================================
# Uncomment and modify these for local development:

# DATABASE_URL=postgresql://postgres:password@localhost:5432/darvis_dev
# REDIS_URL=redis://localhost:6379
# CELERY_BROKER_URL=redis://localhost:6379
# CELERY_RESULT_BACKEND=redis://localhost:6379
# ENVIRONMENT=development
# DEBUG=true
# HOST=0.0.0.0
# PORT=8000
# LOG_LEVEL=DEBUG
# CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:5173

CLOUDINARY_CLOUD_NAME=cloudname
CLOUDINARY_API_KEY=apikey
CLOUDINARY_API_SECRET=apisecret

