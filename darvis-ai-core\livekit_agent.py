"""
LiveKit Voice AI Agent for Darvis.

This module implements a sophisticated voice AI agent using the LiveKit Agents framework.
It provides real-time voice interaction with Deepgram STT, Darvis AI orchestrator, and Cartesia TTS.
"""

import logging
import asyncio
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
from dotenv import load_dotenv

# LiveKit Agents Framework - Correct imports
from livekit import agents
from livekit.agents import AgentSession, Agent, stt, tts
from livekit.plugins.turn_detector.multilingual import MultilingualModel
import httpx
import io
import wave

# Darvis Core Components
from darvis_ai_core.main_orchestrator import DarvisOrchestrator
from app.db.database import get_db
from app.db import crud

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class GroqSTT(stt.STT):
    """
    Custom Groq STT implementation for LiveKit Agents.

    Integrates Groq's Whisper models with LiveKit's STT interface,
    providing high-performance speech recognition as you requested.
    """

    def __init__(self, model: str = "whisper-large-v3"):
        """Initialize Groq STT with specified model."""
        super().__init__(capabilities=stt.STTCapabilities(streaming=False))
        self.model = model
        self.api_key = os.getenv("GROQ_API_KEY")

        if not self.api_key:
            raise ValueError("GROQ_API_KEY environment variable is required")

    async def _recognize_impl(self, buffer: stt.SpeechBuffer, *, language: str | None = None) -> stt.SpeechEvent:
        """
        Implement speech recognition using Groq API.

        Args:
            buffer: Audio buffer to transcribe
            language: Optional language hint

        Returns:
            SpeechEvent with transcription results
        """
        try:
            # Convert audio buffer to format suitable for Groq API
            audio_data = buffer.data

            # Create form data for Groq API
            files = {
                'file': ('audio.wav', audio_data, 'audio/wav'),
                'model': (None, self.model),
                'response_format': (None, 'json'),
            }

            if language:
                files['language'] = (None, language)

            # Make request to Groq STT API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.groq.com/openai/v1/audio/transcriptions",
                    headers={"Authorization": f"Bearer {self.api_key}"},
                    files=files,
                    timeout=30.0
                )
                response.raise_for_status()

                result = response.json()
                text = result.get("text", "").strip()

                if text:
                    return stt.SpeechEvent(
                        type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                        alternatives=[stt.SpeechData(text=text, confidence=1.0)]
                    )
                else:
                    return stt.SpeechEvent(type=stt.SpeechEventType.END_OF_SPEECH)

        except Exception as e:
            logger.error(f"Error in Groq STT recognition: {e}")
            return stt.SpeechEvent(type=stt.SpeechEventType.END_OF_SPEECH)

class KokoroTTS(tts.TTS):
    """
    Custom Kokoro TTS implementation for LiveKit Agents via DeepInfra.

    Integrates the Kokoro-82M model from DeepInfra as you specifically requested,
    providing high-quality, efficient text-to-speech synthesis with user
    pronunciation customization support.
    """

    def __init__(self, voice: str = "af_bella", speed: float = 1.0, user_id: str = None):
        """Initialize Kokoro TTS with DeepInfra API."""
        super().__init__(
            capabilities=tts.TTSCapabilities(streaming=False),
            sample_rate=22050,
            num_channels=1,
        )
        self.voice = voice
        self.speed = speed
        self.user_id = user_id
        self.api_key = os.getenv("DEEPINFRA_API_KEY")

        if not self.api_key:
            raise ValueError("DEEPINFRA_API_KEY environment variable is required")

    def _apply_pronunciation_customizations(self, text: str) -> str:
        """
        Apply user-defined pronunciation customizations to the text.

        Args:
            text: Original text to synthesize

        Returns:
            Text with pronunciation customizations applied
        """
        if not self.user_id:
            return text

        try:
            # Import here to avoid circular imports
            from app.db.database import get_db
            from app.db import crud

            # Get user's pronunciation customizations
            db = next(get_db())
            pronunciations_dict = crud.get_all_user_pronunciations_dict(db, self.user_id)
            db.close()

            if not pronunciations_dict:
                return text

            # Apply pronunciation replacements (case-insensitive)
            modified_text = text
            for word_to_replace, phonetic_pronunciation in pronunciations_dict.items():
                # Use word boundaries to avoid partial matches
                import re
                pattern = r'\b' + re.escape(word_to_replace) + r'\b'
                modified_text = re.sub(pattern, phonetic_pronunciation, modified_text, flags=re.IGNORECASE)

            if modified_text != text:
                logger.info(f"Applied pronunciation customizations for user {self.user_id}")
                logger.debug(f"Original: {text}")
                logger.debug(f"Modified: {modified_text}")

            return modified_text

        except Exception as e:
            logger.error(f"Error applying pronunciation customizations: {e}")
            return text  # Return original text on error

    async def _synthesize_impl(self, text: str) -> tts.SynthesizedAudio:
        """
        Implement text-to-speech using Kokoro TTS via DeepInfra.

        Applies user pronunciation customizations before synthesis.

        Args:
            text: Text to synthesize

        Returns:
            SynthesizedAudio with generated speech
        """
        try:
            # Apply user pronunciation customizations first
            customized_text = self._apply_pronunciation_customizations(text)

            # Make request to DeepInfra Kokoro TTS API (as per your provided link)
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.deepinfra.com/v1/inference/hexgrad/Kokoro-82M",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "text": customized_text,  # Use customized text
                        "preset_voice": [self.voice],
                        "speed": self.speed,
                        "output_format": "wav",
                        "sample_rate": 22050
                    },
                    timeout=30.0
                )
                response.raise_for_status()

                # The API returns audio data directly
                audio_data = response.content

                return tts.SynthesizedAudio(
                    text=text,  # Return original text for display
                    data=audio_data,
                    sample_rate=22050,
                    num_channels=1,
                )

        except Exception as e:
            logger.error(f"Error in Kokoro TTS synthesis: {e}")
            # Return empty audio on error
            return tts.SynthesizedAudio(
                text=text,
                data=b"",
                sample_rate=22050,
                num_channels=1,
            )

class DarvisLLM:
    """
    Custom LLM implementation that integrates with Darvis orchestrator.

    This class provides the LLM interface required by LiveKit Agents
    while routing requests through Darvis's sophisticated AI orchestration.
    """

    def __init__(self, user_id: str, conversation_id: Optional[str] = None):
        """Initialize the Darvis LLM."""
        self.user_id = user_id
        self.conversation_id = conversation_id
        self.orchestrator = DarvisOrchestrator()

    async def agenerate(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        Generate a response using Darvis orchestrator.

        Args:
            messages: List of chat messages
            **kwargs: Additional generation parameters

        Returns:
            AI-generated response text
        """
        try:
            # Extract the latest user message
            user_message = None
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    user_message = msg.get("content", "")
                    break

            if not user_message:
                return "I didn't catch that. Could you please repeat?"

            # Process message through Darvis orchestrator
            response = await self.orchestrator.process_message(
                user_id=self.user_id,
                message=user_message,
                conversation_id=self.conversation_id,
                context={}
            )

            # Extract text response from orchestrator output
            if isinstance(response, dict) and "response" in response:
                return response["response"]
            elif hasattr(response, "content"):
                return response.content
            else:
                return str(response)

        except Exception as e:
            logger.error(f"Error in Darvis LLM generation: {e}")
            return "I apologize, but I'm experiencing technical difficulties. Please try again."

class DarvisAssistant(Agent):
    """
    Main Darvis AI Assistant Agent.

    This agent provides intelligent responses using Darvis's orchestrator
    while maintaining the LiveKit Agent interface for voice interactions.
    """

    def __init__(self, user_id: str, mode: str = "general", conversation_id: Optional[str] = None):
        """
        Initialize the Darvis Assistant.

        Args:
            user_id: User identifier
            mode: Conversation mode (therapy, productivity, general)
            conversation_id: Optional conversation ID for context
        """
        # Get mode-specific instructions
        instructions = self._get_mode_instructions(mode)
        super().__init__(instructions=instructions)

        self.user_id = user_id
        self.mode = mode
        self.conversation_id = conversation_id
        self.darvis_llm = DarvisLLM(user_id, conversation_id)

    def _get_mode_instructions(self, mode: str) -> str:
        """Get agent instructions based on conversation mode."""
        instructions = {
            "therapy": (
                "You are Darvis, a compassionate AI therapy assistant. "
                "Provide supportive, empathetic responses while maintaining professional boundaries. "
                "Use active listening techniques and ask thoughtful follow-up questions. "
                "Keep responses concise for voice interaction."
            ),
            "productivity": (
                "You are Darvis, a productivity-focused AI assistant. "
                "Help users manage tasks, organize their workflow, and achieve their goals. "
                "Be direct, actionable, and efficient in your responses. "
                "Prioritize practical solutions and clear next steps."
            ),
            "general": (
                "You are Darvis, a helpful AI assistant. "
                "Provide informative, engaging responses while being conversational and natural. "
                "Keep responses appropriate for voice interaction - clear and concise."
            )
        }
        return instructions.get(mode, instructions["general"])

# LiveKit Agent Entry Point - Following Official Documentation Pattern
async def entrypoint(ctx: agents.JobContext):
    """
    LiveKit agent entry point following official documentation.

    This function is called when a new agent is dispatched to a room.
    It extracts user context and starts the appropriate Darvis agent.
    """
    try:
        logger.info(f"Agent dispatched to room: {ctx.room.name}")

        # Extract user context from room metadata
        room_metadata = ctx.room.metadata or "{}"
        import json
        metadata = json.loads(room_metadata) if room_metadata else {}

        user_id = metadata.get("user_id")
        mode = metadata.get("mode", "general")
        conversation_id = metadata.get("conversation_id")

        if not user_id:
            logger.error("No user_id found in room metadata")
            return

        # Create AgentSession with YOUR REQUESTED components
        session = AgentSession(
            stt=GroqSTT(model="whisper-large-v3"),  # YOUR REQUESTED Groq STT
            llm=DarvisLLM(user_id, conversation_id),  # Our custom Darvis LLM
            tts=KokoroTTS(voice="af_bella", speed=1.0, user_id=user_id),  # YOUR REQUESTED Kokoro TTS with user customizations
            turn_detection=MultilingualModel(),  # Sophisticated turn detection for natural conversation flow
        )

        # Create Darvis Assistant
        assistant = DarvisAssistant(
            user_id=user_id,
            mode=mode,
            conversation_id=conversation_id
        )

        # Start the session
        await session.start(room=ctx.room, agent=assistant)

        # Generate initial greeting
        greeting = get_mode_greeting(mode)
        await session.generate_reply(instructions=f"Greet the user with: {greeting}")

        logger.info(f"Darvis voice session started successfully for user {user_id}")

    except Exception as e:
        logger.error(f"Error in agent entrypoint: {e}")
        raise

def get_mode_greeting(mode: str) -> str:
    """Get greeting message based on conversation mode."""
    greetings = {
        "therapy": "Hello, I'm Darvis. I'm here to listen and support you. How are you feeling today?",
        "productivity": "Hi there! I'm Darvis, your productivity assistant. What would you like to accomplish today?",
        "general": "Hello! I'm Darvis, your AI assistant. How can I help you today?"
    }
    return greetings.get(mode, greetings["general"])

# CLI runner for development and production - Following Official Documentation
if __name__ == "__main__":
    agents.cli.run_app(
        agents.WorkerOptions(
            entrypoint_fnc=entrypoint,
            # Add any additional worker options here
        )
    )
