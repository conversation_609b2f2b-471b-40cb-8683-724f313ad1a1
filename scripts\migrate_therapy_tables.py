#!/usr/bin/env python3
"""
Database migration script for therapy mode tables.

This script creates the new therapy-specific tables and indexes for optimal performance.
Run this script after updating the models to add therapy functionality.
"""

import os
import sys
import logging
from sqlalchemy import text

# Add the parent directory to the Python path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.database import engine, SessionLocal
from app.db.models import Base

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_therapy_tables():
    """Create all therapy-related tables."""
    try:
        logger.info("Creating therapy tables...")
        
        # Create all tables defined in models
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ Therapy tables created successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating therapy tables: {e}")
        return False


def create_therapy_indexes():
    """Create optimized indexes for therapy tables."""
    db = SessionLocal()
    
    try:
        logger.info("Creating therapy-specific indexes...")
        
        # Index for therapy knowledge chunk vector similarity search
        db.execute(text("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_therapy_chunks_embedding_cosine 
            ON therapy_knowledge_chunks USING ivfflat (embedding vector_cosine_ops) 
            WITH (lists = 100);
        """))
        
        # Index for therapy technique filtering
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_therapy_technique 
            ON therapy_knowledge_chunks(therapy_technique);
        """))
        
        # Index for emotional themes filtering (GIN index for JSON arrays)
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_therapy_emotional_themes 
            ON therapy_knowledge_chunks USING GIN (emotional_themes);
        """))
        
        # Index for therapy session lookup by conversation
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_therapy_session_conversation 
            ON therapy_sessions(conversation_id);
        """))
        
        # Index for therapy session lookup by user
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_therapy_session_user 
            ON therapy_sessions(user_id);
        """))
        
        # Index for therapy session active status
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_therapy_session_active 
            ON therapy_sessions(user_id, ended_at) 
            WHERE ended_at IS NULL;
        """))
        
        # Index for therapy progress by user and type
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_therapy_progress_user_type 
            ON therapy_progress(user_id, progress_type);
        """))
        
        # Index for therapy progress by creation date
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_therapy_progress_created 
            ON therapy_progress(user_id, created_at DESC);
        """))
        
        # Index for therapy progress task completion
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_therapy_progress_tasks 
            ON therapy_progress(user_id, task_completed) 
            WHERE progress_type = 'practice_task';
        """))
        
        db.commit()
        logger.info("✅ Therapy indexes created successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating therapy indexes: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()


def update_notification_preferences():
    """Update existing notification preferences to include therapy settings."""
    db = SessionLocal()
    
    try:
        logger.info("Updating notification preferences for therapy mode...")
        
        # Add therapy notification columns to existing preferences
        # Note: These columns should already exist from the model updates
        # This is just to ensure they have default values
        
        db.execute(text("""
            UPDATE notification_preferences 
            SET 
                therapy_session_reminders_enabled = COALESCE(therapy_session_reminders_enabled, true),
                therapy_practice_followups_enabled = COALESCE(therapy_practice_followups_enabled, true),
                therapy_progress_celebrations_enabled = COALESCE(therapy_progress_celebrations_enabled, true),
                therapy_emotional_checkins_enabled = COALESCE(therapy_emotional_checkins_enabled, false),
                therapy_crisis_support_enabled = COALESCE(therapy_crisis_support_enabled, true),
                therapy_insight_summaries_enabled = COALESCE(therapy_insight_summaries_enabled, true),
                therapy_reengagement_enabled = COALESCE(therapy_reengagement_enabled, true),
                therapy_quiet_hours_start = COALESCE(therapy_quiet_hours_start, 21),
                therapy_quiet_hours_end = COALESCE(therapy_quiet_hours_end, 9),
                therapy_max_notifications_per_week = COALESCE(therapy_max_notifications_per_week, 3)
            WHERE 
                therapy_session_reminders_enabled IS NULL 
                OR therapy_practice_followups_enabled IS NULL;
        """))
        
        db.commit()
        logger.info("✅ Notification preferences updated successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error updating notification preferences: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()


def verify_therapy_setup():
    """Verify that therapy tables and indexes were created correctly."""
    db = SessionLocal()
    
    try:
        logger.info("Verifying therapy setup...")
        
        # Check if therapy tables exist
        tables_to_check = [
            'therapy_knowledge_chunks',
            'therapy_sessions', 
            'therapy_progress'
        ]
        
        for table in tables_to_check:
            result = db.execute(text(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = '{table}'
                );
            """)).scalar()
            
            if result:
                logger.info(f"✅ Table {table} exists")
            else:
                logger.error(f"❌ Table {table} does not exist")
                return False
        
        # Check if vector extension is available
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT FROM pg_extension 
                WHERE extname = 'vector'
            );
        """)).scalar()
        
        if result:
            logger.info("✅ pgvector extension is available")
        else:
            logger.warning("⚠️  pgvector extension not found - vector operations may not work")
        
        # Check if indexes exist
        indexes_to_check = [
            'idx_therapy_chunks_embedding_cosine',
            'idx_therapy_technique',
            'idx_therapy_session_conversation',
            'idx_therapy_progress_user_type'
        ]
        
        for index in indexes_to_check:
            result = db.execute(text(f"""
                SELECT EXISTS (
                    SELECT FROM pg_indexes 
                    WHERE indexname = '{index}'
                );
            """)).scalar()
            
            if result:
                logger.info(f"✅ Index {index} exists")
            else:
                logger.warning(f"⚠️  Index {index} does not exist")
        
        logger.info("✅ Therapy setup verification completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error verifying therapy setup: {e}")
        return False
        
    finally:
        db.close()


def main():
    """Main migration function."""
    logger.info("🚀 Starting therapy mode database migration...")
    
    success = True
    
    # Step 1: Create therapy tables
    if not create_therapy_tables():
        success = False
    
    # Step 2: Create therapy indexes
    if not create_therapy_indexes():
        success = False
    
    # Step 3: Update notification preferences
    if not update_notification_preferences():
        success = False
    
    # Step 4: Verify setup
    if not verify_therapy_setup():
        success = False
    
    if success:
        logger.info("🎉 Therapy mode migration completed successfully!")
        logger.info("Next steps:")
        logger.info("1. Run the therapy knowledge population script")
        logger.info("2. Test therapy mode functionality")
        logger.info("3. Configure therapy notification schedules")
    else:
        logger.error("💥 Therapy mode migration failed!")
        logger.error("Please check the errors above and retry")
        sys.exit(1)


if __name__ == "__main__":
    main()
