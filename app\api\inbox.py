"""
Frictionless Inbox API endpoints for capturing and processing web content.

This module provides endpoints for users to capture URLs for background processing,
content extraction, and AI-powered summarization.
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import InboxItemCreate, InboxItemResponse, InboxItemListResponse
import logging

# Import Celery task (with fallback for development)
try:
    from app.worker import process_inbox_item
    CELERY_AVAILABLE = True
except ImportError:
    CELERY_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("Celery not available, using background tasks fallback")

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/inbox", tags=["Inbox"])

@router.post("/capture", response_model=InboxItemResponse, status_code=status.HTTP_201_CREATED)
def capture_url(
    inbox_item: InboxItemCreate,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Capture a URL for background processing and content extraction.
    
    This endpoint immediately saves the URL to the database with "pending" status
    and triggers a background task to process the content. The user receives
    an immediate response while processing happens asynchronously.
    
    - **original_url**: The URL to capture and process (1-2000 characters)
    
    Returns the created inbox item with generated ID and timestamps.
    """
    try:
        user_id = current_user["uid"]
        
        # Create the inbox item with pending status
        db_item = crud.create_inbox_item(
            db=db,
            user_id=user_id,
            original_url=inbox_item.original_url
        )
        
        # Trigger background processing task
        if CELERY_AVAILABLE:
            # Use Celery for production background processing
            process_inbox_item.delay(db_item.id)
            logger.info(f"Celery task queued for inbox item: {db_item.id}")
        else:
            # Fallback to FastAPI background tasks for development
            background_tasks.add_task(process_inbox_item_background, db_item.id)
            logger.info(f"Background task queued for inbox item: {db_item.id}")
        
        logger.info(f"Captured URL for user {user_id}: {inbox_item.original_url}")
        
        return db_item
        
    except Exception as e:
        logger.error(f"Error capturing URL: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to capture URL for processing"
        )

@router.get("/", response_model=InboxItemListResponse)
def get_inbox_items(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all inbox items for the authenticated user with filtering and pagination.
    
    - **skip**: Number of items to skip (default: 0)
    - **limit**: Maximum number of items to return (default: 100)
    - **status_filter**: Filter by status (pending, processing, complete, failed)
    
    Returns a list of inbox items ordered by creation date (newest first).
    """
    user_id = current_user["uid"]
    
    inbox_items = crud.get_user_inbox_items(
        db=db,
        user_id=user_id,
        skip=skip,
        limit=limit,
        status=status_filter
    )
    
    return InboxItemListResponse(inbox_items=inbox_items, total=len(inbox_items))

@router.get("/{item_id}", response_model=InboxItemResponse)
def get_inbox_item(
    item_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific inbox item by ID"""
    user_id = current_user["uid"]
    
    db_item = crud.get_inbox_item_by_id(db=db, item_id=item_id, user_id=user_id)
    if db_item is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Inbox item not found"
        )
    
    return db_item

@router.delete("/{item_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_inbox_item(
    item_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a specific inbox item"""
    user_id = current_user["uid"]
    
    success = crud.delete_inbox_item(db=db, item_id=item_id, user_id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Inbox item not found"
        )
    
    return None

def process_inbox_item_background(item_id: str):
    """
    Background task fallback for processing inbox items when Celery is not available.

    This function provides basic processing functionality for development environments
    where Celery might not be set up. In production, use the Celery task instead.

    Args:
        item_id: The ID of the inbox item to process
    """
    try:
        from app.sdk.jina_reader_sdk import get_content_from_url
        from app.db.database import SessionLocal

        logger.info(f"Background processing (fallback) for inbox item: {item_id}")

        # Get database session
        db = SessionLocal()

        try:
            # Update status to processing
            crud.update_inbox_item_status(db, item_id, "processing")

            # Get the item
            db_item = db.query(crud.InboxItem).filter(crud.InboxItem.id == item_id).first()
            if not db_item:
                logger.error(f"Inbox item not found: {item_id}")
                return

            # Extract content
            content_result = get_content_from_url(db_item.original_url)

            if content_result["success"]:
                # Save content and mark as complete
                crud.update_inbox_item_content(db, item_id, clean_content=content_result["content"])
                crud.update_inbox_item_content(db, item_id, summary="Content extracted successfully (basic processing)")
                crud.update_inbox_item_status(db, item_id, "complete")
                logger.info(f"Successfully processed inbox item: {item_id}")
            else:
                # Mark as failed
                crud.update_inbox_item_status(db, item_id, "failed")
                logger.error(f"Failed to extract content for item {item_id}: {content_result['error']}")

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error in background processing fallback: {e}")
        # Try to mark as failed
        try:
            db = SessionLocal()
            crud.update_inbox_item_status(db, item_id, "failed")
            db.close()
        except:
            pass
