"""
Therapy Retrieval SDK for <PERSON><PERSON>.

This module provides intelligent retrieval of therapy knowledge content using vector similarity
and contextual filtering. Follows Darvis SDK adapter pattern for consistency and maintainability.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
import json
import redis
from datetime import datetime, timedelta

from app.sdk.embedding_sdk import EmbeddingSD<PERSON>
from app.db import crud
from app.db.models import TherapyKnowledgeChunk, TherapySession

# Configure logging
logger = logging.getLogger(__name__)

class TherapyRetrievalError(Exception):
    """Custom exception for therapy retrieval errors."""
    pass

class TherapyRetrievalSDK:
    """
    SDK for intelligent therapy knowledge retrieval and contextual content delivery.
    
    This service provides sophisticated retrieval of therapy content using vector similarity,
    contextual filtering, and user-specific personalization for optimal therapeutic support.
    """

    def __init__(self, db: Session, embedding_sdk: EmbeddingSDK, redis_client=None):
        """
        Initialize the therapy retrieval SDK.

        Args:
            db: Database session for therapy knowledge access
            embedding_sdk: Embedding service for query vectorization
            redis_client: Optional Redis client for caching
        """
        self.db = db
        self.embedding_sdk = embedding_sdk
        self.redis_client = redis_client
        self._initialize_cache()

    def _initialize_cache(self):
        """Initialize Redis caching for therapy content."""
        try:
            if self.redis_client:
                # Test Redis connection
                self.redis_client.ping()
                self.cache_enabled = True
                logger.info("Therapy retrieval cache initialized successfully")
            else:
                self.cache_enabled = False
                logger.warning("Redis not available, therapy retrieval caching disabled")
        except Exception as e:
            logger.error(f"Failed to initialize therapy retrieval cache: {e}")
            self.cache_enabled = False

    def retrieve_therapy_content(
        self,
        query: str,
        user_id: str,
        session_context: Optional[Dict[str, Any]] = None,
        top_k: int = 5,
        therapy_technique: Optional[str] = None,
        emotional_themes: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant therapy content using hybrid approach.
        
        Combines vector similarity search with contextual filtering and user personalization
        for optimal therapeutic content delivery.

        Args:
            query: User's message or therapeutic query
            user_id: User identifier for personalization
            session_context: Current therapy session context
            top_k: Number of results to return
            therapy_technique: Specific technique filter (CBT, ACT, etc.)
            emotional_themes: Emotional context filters

        Returns:
            List of relevant therapy content with metadata
        """
        try:
            # Generate cache key for potential caching
            cache_key = self._generate_cache_key(query, user_id, therapy_technique, emotional_themes)
            
            # Check cache first
            if self.cache_enabled:
                cached_result = self._get_cached_content(cache_key)
                if cached_result:
                    logger.debug(f"Retrieved therapy content from cache for user {user_id}")
                    return cached_result

            # Generate query embedding
            query_embedding = self.embedding_sdk.generate_embedding(query)
            
            # Enhance search with session context
            enhanced_emotional_themes = self._enhance_emotional_context(
                emotional_themes, session_context, user_id
            )
            
            # Perform vector similarity search with filters
            knowledge_chunks = crud.search_therapy_knowledge(
                db=self.db,
                query_embedding=query_embedding,
                therapy_technique=therapy_technique,
                emotional_themes=enhanced_emotional_themes,
                limit=top_k * 2  # Get more results for re-ranking
            )

            # Re-rank results based on user-specific effectiveness
            ranked_results = self._rerank_by_user_effectiveness(
                knowledge_chunks, user_id, session_context
            )

            # Format results with relevance scoring
            formatted_results = self._format_therapy_results(
                ranked_results[:top_k], query_embedding
            )

            # Cache results for future use
            if self.cache_enabled and formatted_results:
                self._cache_content(cache_key, formatted_results)

            # Log retrieval for analytics
            self._log_retrieval_analytics(user_id, query, len(formatted_results))

            logger.info(f"Retrieved {len(formatted_results)} therapy content pieces for user {user_id}")
            return formatted_results

        except Exception as e:
            logger.error(f"Error retrieving therapy content for user {user_id}: {e}")
            raise TherapyRetrievalError(f"Failed to retrieve therapy content: {str(e)}")

    def retrieve_personalized_interventions(
        self,
        user_id: str,
        emotional_state: Dict[str, Any],
        session_focus: Optional[str] = None,
        limit: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Retrieve personalized therapeutic interventions based on user history and current state.

        Args:
            user_id: User identifier
            emotional_state: Current emotional state data
            session_focus: Current session focus area
            limit: Number of interventions to return

        Returns:
            List of personalized therapeutic interventions
        """
        try:
            # Get user's most effective interventions from history
            user_progress = crud.get_user_therapy_progress(
                db=self.db,
                user_id=user_id,
                progress_type="intervention_feedback",
                limit=50
            )

            # Analyze intervention effectiveness
            effective_interventions = self._analyze_intervention_effectiveness(user_progress)

            # Build query based on emotional state and session focus
            query_parts = []
            if session_focus:
                query_parts.append(session_focus)
            
            emotional_descriptors = emotional_state.get('descriptors', [])
            if emotional_descriptors:
                query_parts.extend(emotional_descriptors[:3])  # Top 3 emotional descriptors

            query = " ".join(query_parts) if query_parts else "therapeutic intervention"

            # Retrieve content with preference for effective interventions
            therapy_content = self.retrieve_therapy_content(
                query=query,
                user_id=user_id,
                session_context={"emotional_state": emotional_state, "focus": session_focus},
                top_k=limit * 2,
                emotional_themes=emotional_descriptors
            )

            # Filter and prioritize based on user's effective interventions
            personalized_interventions = self._prioritize_by_effectiveness(
                therapy_content, effective_interventions, limit
            )

            logger.info(f"Retrieved {len(personalized_interventions)} personalized interventions for user {user_id}")
            return personalized_interventions

        except Exception as e:
            logger.error(f"Error retrieving personalized interventions for user {user_id}: {e}")
            return []

    def _enhance_emotional_context(
        self,
        emotional_themes: Optional[List[str]],
        session_context: Optional[Dict[str, Any]],
        user_id: str
    ) -> Optional[List[str]]:
        """Enhance emotional context with session and user history data."""
        enhanced_themes = list(emotional_themes) if emotional_themes else []
        
        # Add themes from session context
        if session_context:
            if session_context.get('emotional_state'):
                state_themes = session_context['emotional_state'].get('descriptors', [])
                enhanced_themes.extend(state_themes)
            
            if session_context.get('focus'):
                enhanced_themes.append(session_context['focus'])

        # Add themes from recent user sessions
        recent_sessions = crud.get_user_therapy_sessions(
            db=self.db,
            user_id=user_id,
            limit=3,
            include_active=False
        )
        
        for session in recent_sessions:
            if session.session_focus:
                enhanced_themes.append(session.session_focus)

        # Remove duplicates and return
        return list(set(enhanced_themes)) if enhanced_themes else None

    def _rerank_by_user_effectiveness(
        self,
        knowledge_chunks: List[TherapyKnowledgeChunk],
        user_id: str,
        session_context: Optional[Dict[str, Any]]
    ) -> List[TherapyKnowledgeChunk]:
        """Re-rank therapy content based on user-specific effectiveness data."""
        # Get user's intervention effectiveness data
        user_progress = crud.get_user_therapy_progress(
            db=self.db,
            user_id=user_id,
            limit=100
        )

        # Build effectiveness scores for different techniques
        technique_scores = {}
        for progress in user_progress:
            if progress.intervention_used and progress.user_feedback_rating:
                technique = progress.intervention_used
                if technique not in technique_scores:
                    technique_scores[technique] = []
                technique_scores[technique].append(progress.user_feedback_rating)

        # Calculate average effectiveness for each technique
        avg_technique_scores = {}
        for technique, scores in technique_scores.items():
            avg_technique_scores[technique] = sum(scores) / len(scores)

        # Re-rank chunks based on technique effectiveness
        def effectiveness_score(chunk):
            base_score = chunk.effectiveness_score
            technique_bonus = 0
            
            if chunk.therapy_technique and chunk.therapy_technique in avg_technique_scores:
                # Boost score based on user's historical effectiveness with this technique
                technique_bonus = avg_technique_scores[chunk.therapy_technique] * 0.2
            
            return base_score + technique_bonus

        return sorted(knowledge_chunks, key=effectiveness_score, reverse=True)

    def _format_therapy_results(
        self,
        knowledge_chunks: List[TherapyKnowledgeChunk],
        query_embedding: List[float]
    ) -> List[Dict[str, Any]]:
        """Format therapy knowledge chunks into structured results."""
        formatted_results = []
        
        for chunk in knowledge_chunks:
            # Calculate relevance score (cosine similarity)
            relevance_score = self._calculate_cosine_similarity(
                query_embedding, chunk.embedding
            )
            
            result = {
                "id": chunk.id,
                "content": chunk.content,
                "technique": chunk.therapy_technique,
                "chapter": chunk.chapter,
                "section": chunk.section,
                "emotional_themes": chunk.emotional_themes,
                "keywords": chunk.keywords,
                "relevance_score": relevance_score,
                "effectiveness_score": chunk.effectiveness_score,
                "source_document": chunk.source_document
            }
            
            formatted_results.append(result)
        
        return formatted_results

    def _calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        import numpy as np
        
        vec1_np = np.array(vec1)
        vec2_np = np.array(vec2)
        
        dot_product = np.dot(vec1_np, vec2_np)
        norm1 = np.linalg.norm(vec1_np)
        norm2 = np.linalg.norm(vec2_np)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return float(dot_product / (norm1 * norm2))

    def _analyze_intervention_effectiveness(
        self,
        user_progress: List
    ) -> Dict[str, float]:
        """Analyze which interventions have been most effective for the user."""
        intervention_ratings = {}
        
        for progress in user_progress:
            if progress.intervention_used and progress.user_feedback_rating:
                intervention = progress.intervention_used
                if intervention not in intervention_ratings:
                    intervention_ratings[intervention] = []
                intervention_ratings[intervention].append(progress.user_feedback_rating)
        
        # Calculate average effectiveness
        effectiveness_scores = {}
        for intervention, ratings in intervention_ratings.items():
            effectiveness_scores[intervention] = sum(ratings) / len(ratings)
        
        return effectiveness_scores

    def _prioritize_by_effectiveness(
        self,
        therapy_content: List[Dict[str, Any]],
        effective_interventions: Dict[str, float],
        limit: int
    ) -> List[Dict[str, Any]]:
        """Prioritize therapy content based on user's effective interventions."""
        def priority_score(content):
            base_score = content.get('relevance_score', 0)
            technique = content.get('technique', '')
            
            # Boost score if this technique has been effective for the user
            if technique in effective_interventions:
                effectiveness_bonus = effective_interventions[technique] * 0.3
                return base_score + effectiveness_bonus
            
            return base_score
        
        prioritized = sorted(therapy_content, key=priority_score, reverse=True)
        return prioritized[:limit]

    def _generate_cache_key(
        self,
        query: str,
        user_id: str,
        therapy_technique: Optional[str],
        emotional_themes: Optional[List[str]]
    ) -> str:
        """Generate cache key for therapy content retrieval."""
        key_parts = [
            f"therapy_retrieval",
            f"user:{user_id}",
            f"query:{hash(query)}",
        ]
        
        if therapy_technique:
            key_parts.append(f"technique:{therapy_technique}")
        
        if emotional_themes:
            themes_str = ",".join(sorted(emotional_themes))
            key_parts.append(f"themes:{hash(themes_str)}")
        
        return ":".join(key_parts)

    def _get_cached_content(self, cache_key: str) -> Optional[List[Dict[str, Any]]]:
        """Retrieve cached therapy content."""
        try:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.warning(f"Error retrieving cached therapy content: {e}")
        return None

    def _cache_content(self, cache_key: str, content: List[Dict[str, Any]], ttl: int = 3600):
        """Cache therapy content for future retrieval."""
        try:
            self.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(content, default=str)
            )
        except Exception as e:
            logger.warning(f"Error caching therapy content: {e}")

    def _log_retrieval_analytics(self, user_id: str, query: str, result_count: int):
        """Log retrieval analytics for improving therapy content delivery."""
        try:
            analytics_data = {
                "user_id": user_id,
                "query_length": len(query),
                "result_count": result_count,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            if self.cache_enabled:
                analytics_key = f"therapy_analytics:{user_id}:{datetime.utcnow().strftime('%Y-%m-%d')}"
                self.redis_client.lpush(analytics_key, json.dumps(analytics_data))
                self.redis_client.expire(analytics_key, 86400 * 7)  # Keep for 7 days
                
        except Exception as e:
            logger.warning(f"Error logging therapy retrieval analytics: {e}")


# Create a global instance for easy importing
def create_therapy_retrieval_sdk(db: Session, embedding_sdk: EmbeddingSDK, redis_client=None) -> TherapyRetrievalSDK:
    """
    Factory function to create a therapy retrieval SDK instance.
    
    Args:
        db: Database session
        embedding_sdk: Embedding service instance
        redis_client: Optional Redis client for caching
        
    Returns:
        Configured TherapyRetrievalSDK instance
    """
    return TherapyRetrievalSDK(db, embedding_sdk, redis_client)
