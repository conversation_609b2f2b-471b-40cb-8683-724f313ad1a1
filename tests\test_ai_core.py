import pytest
import sys
import os
from unittest.mock import Mock, patch

# Add the darvis-ai-core directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "darvis-ai-core"))

from prp_manager import PRPManager
from main_orchestrator import DarvisOrchestrator

class TestPRPManager:
    """Test cases for the PRP Manager."""
    
    def test_prp_manager_initialization(self):
        """Test that PRP manager initializes correctly."""
        prp_manager = PRPManager()
        assert prp_manager.prompts_dir.name == "prompts"
        assert prp_manager._cache == {}
    
    def test_parse_prp_content(self):
        """Test PRP content parsing."""
        prp_manager = PRPManager()
        
        content = """# PRP-SYS-001: Test Persona

## Trigger
- This is a test trigger.

## Rules
1. First rule
2. Second rule
3. Third rule

## Evaluation
- Test evaluation criteria
"""
        
        result = prp_manager._parse_prp_content(content)
        
        assert result["title"] == "PRP-SYS-001: Test Persona"
        assert "This is a test trigger." in result["trigger"]
        assert len(result["rules"]) == 3
        assert result["rules"][0] == "First rule"
        assert "Test evaluation criteria" in result["evaluation"]

class TestDarvisOrchestrator:
    """Test cases for the Darvis Orchestrator."""
    
    def test_orchestrator_initialization(self):
        """Test that orchestrator initializes correctly."""
        orchestrator = DarvisOrchestrator()
        assert orchestrator.graph is not None
    
    @patch('main_orchestrator.llm_provider')
    @patch('main_orchestrator.prp_manager')
    def test_process_message_mock(self, mock_prp_manager, mock_llm_provider):
        """Test message processing with mocked dependencies."""
        # Mock PRP manager
        mock_prp_manager.get_system_prp.return_value = {
            "title": "Test PRP",
            "rules": ["Be helpful", "Be concise"],
            "trigger": "Test trigger"
        }
        
        # Mock LLM provider
        from langchain_core.messages import AIMessage
        mock_llm_provider.create_messages.return_value = []
        mock_llm_provider.invoke.return_value = AIMessage(content="Hello! How can I help you?")
        
        orchestrator = DarvisOrchestrator()
        response = orchestrator.process_message(
            user_message="Hello",
            user_id="test_user_123"
        )
        
        assert isinstance(response, str)
        assert len(response) > 0
        assert "Hello! How can I help you?" in response
    
    def test_process_message_error_handling(self):
        """Test error handling in message processing."""
        orchestrator = DarvisOrchestrator()
        
        # Test with empty message
        response = orchestrator.process_message(
            user_message="",
            user_id="test_user_123"
        )
        
        assert isinstance(response, str)
        assert "error" in response.lower() or "sorry" in response.lower()

class TestChatAPI:
    """Test cases for the Chat API endpoint."""
    
    def test_chat_message_validation(self):
        """Test chat message model validation."""
        from app.api.chat import ChatMessage
        
        # Valid message
        valid_message = ChatMessage(message="Hello, how are you?")
        assert valid_message.message == "Hello, how are you?"
        assert valid_message.context is None
        
        # Test with context
        message_with_context = ChatMessage(
            message="Continue our conversation",
            context={"previous_topic": "weather"}
        )
        assert message_with_context.context["previous_topic"] == "weather"
    
    def test_chat_response_model(self):
        """Test chat response model."""
        from app.api.chat import ChatResponse
        
        response = ChatResponse(
            response="Hello! How can I help you?",
            user_id="test_user_123",
            success=True
        )
        
        assert response.response == "Hello! How can I help you?"
        assert response.user_id == "test_user_123"
        assert response.success is True

if __name__ == "__main__":
    pytest.main([__file__])
