# Start with an official, lightweight Python image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user
RUN useradd --create-home --shell /bin/bash app

# Set the working directory inside the container
WORKDIR /app

# Copy the requirements file first to leverage <PERSON><PERSON>'s layer caching
COPY requirements.txt .

# Install the Python libraries
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of our application code into the container
COPY . .

# Change ownership of the app directory to the app user
RUN chown -R app:app /app

# Switch to the non-root user
USER app

# Expose the port the app will run on
EXPOSE 8000

# Create a temporary file for the Google credentials from the environment variable and then start the server
CMD ["/bin/sh", "-c", "printf '%s' \"$GOOGLE_APPLICATION_CREDENTIALS_JSON\" > /tmp/gcp_creds.json && export GOOGLE_APPLICATION_CREDENTIALS=/tmp/gcp_creds.json && uvicorn app.main:app --host 0.0.0.0 --port 8000"]
