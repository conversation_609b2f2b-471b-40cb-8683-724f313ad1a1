"""
Comprehensive test suite for Phase 2 implementation.

This test suite validates all new Phase 2 functionality including:
1. Enhanced Notes with rich text, categories, templates
2. Enhanced Tasks with subtasks, time tracking, dependencies
3. Calendar Events with full CRUD operations
4. Attachments system
5. Sync functionality
"""

import pytest
import json
from datetime import datetime, timedelta, timezone
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from unittest.mock import patch, MagicMock

from app.main import app
from app.db.database import get_db
from app.db.models import Base, User
from app.db import crud
from app.api.auth import get_current_user

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_phase2.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

# Mock Firebase authentication
def mock_get_current_user():
    return {"uid": "test_user_phase2", "email": "<EMAIL>"}

app.dependency_overrides[get_current_user] = mock_get_current_user

# Create test client
client = TestClient(app)

# Test constants
TEST_USER_ID = "test_user_phase2"
TEST_USER_EMAIL = "<EMAIL>"

@pytest.fixture(scope="module")
def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def test_db():
    """Get test database session."""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()

@pytest.fixture
def test_user(test_db):
    """Create a test user."""
    # Check if user already exists
    existing_user = crud.get_user_by_email(db=test_db, email=TEST_USER_EMAIL)
    if existing_user:
        return existing_user
    
    user = crud.create_user(db=test_db, user_id=TEST_USER_ID, email=TEST_USER_EMAIL)
    return user

class TestEnhancedNotes:
    """Test enhanced notes functionality."""
    
    def test_create_note_with_rich_text(self, setup_database, test_user):
        """Test creating note with rich text content."""
        note_data = {
            "title": "Rich Text Note",
            "content": "This is a **bold** note with *italic* text",
            "content_type": "markdown",
            "content_data": {"formatting": {"bold": [10, 14], "italic": [25, 31]}},
            "category": "work",
            "tags": ["important", "markdown"]
        }
        
        response = client.post("/api/v1/notes/", json=note_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "Rich Text Note"
        assert data["content_type"] == "markdown"
        assert data["category"] == "work"
        assert "important" in data["tags"]
    
    def test_create_note_template(self, setup_database, test_user):
        """Test creating a note template."""
        template_data = {
            "title": "Meeting Notes Template",
            "content": "# Meeting Notes\n\n## Attendees\n\n## Agenda\n\n## Action Items",
            "content_type": "markdown",
            "is_template": True,
            "category": "templates",
            "tags": ["template", "meeting"]
        }
        
        response = client.post("/api/v1/notes/", json=template_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["is_template"] is True
        assert data["category"] == "templates"

class TestEnhancedTasks:
    """Test enhanced tasks functionality."""
    
    def test_create_task_with_time_tracking(self, setup_database, test_user):
        """Test creating task with time tracking features."""
        task_data = {
            "content": "Implement Phase 2 features",
            "priority": 2,
            "estimated_duration_minutes": 120,
            "tags": ["development", "phase2"],
            "due_date": (datetime.now(timezone.utc) + timedelta(days=3)).isoformat()
        }
        
        response = client.post("/api/v1/tasks/", json=task_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["content"] == "Implement Phase 2 features"
        assert data["estimated_duration_minutes"] == 120
        assert data["priority"] == 2
    
    def test_create_subtask(self, setup_database, test_user, test_db):
        """Test creating subtasks."""
        # First create a parent task
        parent_task = crud.create_user_task(
            db=test_db,
            user_id=TEST_USER_ID,
            content="Parent Task",
            priority=1
        )
        
        subtask_data = {
            "parent_task_id": parent_task.id,
            "content": "Subtask 1",
            "priority": 1
        }
        
        response = client.post(f"/api/v1/tasks/{parent_task.id}/subtasks", json=subtask_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["content"] == "Subtask 1"
        assert data["parent_task_id"] == parent_task.id
    
    def test_time_tracking(self, setup_database, test_user, test_db):
        """Test time tracking functionality."""
        # Create a task
        task = crud.create_user_task(
            db=test_db,
            user_id=TEST_USER_ID,
            content="Time tracking test task"
        )
        
        # Start time tracking
        response = client.post(f"/api/v1/tasks/{task.id}/time-tracking/start")
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_tracking"] is True
        assert data["task_id"] == task.id
        
        # Stop time tracking
        response = client.post(f"/api/v1/tasks/{task.id}/time-tracking/stop")
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_tracking"] is False
        assert data["duration_minutes"] is not None

class TestCalendarEvents:
    """Test calendar events functionality."""
    
    def test_create_calendar_event(self, setup_database, test_user):
        """Test creating a calendar event."""
        start_time = datetime.now(timezone.utc) + timedelta(days=1)
        end_time = start_time + timedelta(hours=2)
        
        event_data = {
            "title": "Team Meeting",
            "description": "Weekly team sync",
            "start_datetime": start_time.isoformat(),
            "end_datetime": end_time.isoformat(),
            "category": "work",
            "tags": ["meeting", "team"],
            "location": "Conference Room A",
            "reminder_minutes_before": [15, 5]
        }
        
        response = client.post("/api/v1/calendar/events", json=event_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "Team Meeting"
        assert data["category"] == "work"
        assert data["location"] == "Conference Room A"
        assert 15 in data["reminder_minutes_before"]
    
    def test_get_calendar_events(self, setup_database, test_user):
        """Test retrieving calendar events."""
        response = client.get("/api/v1/calendar/events")
        
        assert response.status_code == 200
        data = response.json()
        assert "events" in data
        assert "total" in data
    
    def test_recurring_event(self, setup_database, test_user):
        """Test creating a recurring event."""
        start_time = datetime.now(timezone.utc) + timedelta(days=1)
        end_time = start_time + timedelta(hours=1)
        
        event_data = {
            "title": "Daily Standup",
            "start_datetime": start_time.isoformat(),
            "end_datetime": end_time.isoformat(),
            "is_recurring": True,
            "recurrence_rule": "FREQ=DAILY;COUNT=30",
            "category": "work",
            "tags": ["standup", "daily"]
        }
        
        response = client.post("/api/v1/calendar/events", json=event_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["is_recurring"] is True
        assert data["recurrence_rule"] == "FREQ=DAILY;COUNT=30"

class TestSyncFunctionality:
    """Test sync functionality."""
    
    def test_create_sync_status(self, setup_database, test_user, test_db):
        """Test creating sync status."""
        # Create a note first
        note = crud.create_user_note(
            db=test_db,
            user_id=TEST_USER_ID,
            title="Sync Test Note",
            content="This note will be synced"
        )
        
        sync_data = {
            "entity_type": "note",
            "entity_id": note.id,
            "device_id": "test_device_123",
            "sync_version": 1
        }
        
        response = client.post("/api/v1/sync/status", json=sync_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["entity_type"] == "note"
        assert data["entity_id"] == note.id
        assert data["device_id"] == "test_device_123"
    
    def test_get_sync_conflicts(self, setup_database, test_user):
        """Test retrieving sync conflicts."""
        response = client.get("/api/v1/sync/conflicts?device_id=test_device_123")
        
        assert response.status_code == 200
        # Should return empty list if no conflicts
        assert isinstance(response.json(), list)

class TestIntegration:
    """Test integration between different Phase 2 features."""
    
    def test_task_with_attachments_and_subtasks(self, setup_database, test_user, test_db):
        """Test complex task with attachments and subtasks."""
        # Create parent task
        parent_task = crud.create_user_task(
            db=test_db,
            user_id=TEST_USER_ID,
            content="Complex Project Task",
            priority=3,
            estimated_duration_minutes=480
        )
        
        # Create subtask
        subtask_data = {
            "parent_task_id": parent_task.id,
            "content": "Research phase",
            "priority": 2
        }
        
        response = client.post(f"/api/v1/tasks/{parent_task.id}/subtasks", json=subtask_data)
        assert response.status_code == 201
        
        # Get subtasks
        response = client.get(f"/api/v1/tasks/{parent_task.id}/subtasks")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 1
        assert data["subtasks"][0]["content"] == "Research phase"

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
