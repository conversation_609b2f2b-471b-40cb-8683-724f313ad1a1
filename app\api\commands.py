"""
User Commands API for Darvis.

This module provides CRUD endpoints for user-defined commands that allow users
to create custom trigger phrases that execute specific actions in the AI system.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Dict, Any, List

from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import (
    UserCommandCreate,
    UserCommandUpdate,
    UserCommandResponse,
    UserCommandListResponse
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/commands", tags=["User Commands"])

@router.post("/", response_model=UserCommandResponse)
async def create_command(
    command: UserCommandCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new user-defined command.
    
    Users can create custom trigger phrases that execute specific actions
    like entering therapy mode, starting productivity sessions, etc.
    """
    try:
        user_id = current_user["uid"]
        
        # Check if trigger phrase already exists for this user
        existing_command = crud.get_user_command_by_trigger(
            db, user_id, command.trigger_phrase
        )
        if existing_command:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="A command with this trigger phrase already exists"
            )
        
        # Create the command
        db_command = crud.create_user_command(
            db=db,
            user_id=user_id,
            trigger_phrase=command.trigger_phrase,
            action_to_perform=command.action_to_perform
        )
        
        logger.info(f"Created command '{command.trigger_phrase}' for user {user_id}")
        return db_command
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating command: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create command"
        )

@router.get("/", response_model=UserCommandListResponse)
async def get_commands(
    skip: int = Query(0, ge=0, description="Number of commands to skip"),
    limit: int = Query(100, ge=1, le=100, description="Number of commands to return"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all commands for the authenticated user with pagination.
    """
    try:
        user_id = current_user["uid"]
        
        # Get commands with pagination
        commands = crud.get_user_commands(db, user_id, skip, limit)
        total = crud.count_user_commands(db, user_id)
        
        return UserCommandListResponse(commands=commands, total=total)
        
    except Exception as e:
        logger.error(f"Error fetching commands: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch commands"
        )

@router.get("/{command_id}", response_model=UserCommandResponse)
async def get_command(
    command_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific command by ID.
    """
    try:
        user_id = current_user["uid"]
        
        command = crud.get_user_command_by_id(db, command_id, user_id)
        if not command:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Command not found"
            )
        
        return command
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching command: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch command"
        )

@router.put("/{command_id}", response_model=UserCommandResponse)
async def update_command(
    command_id: str,
    command_update: UserCommandUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a specific command.
    """
    try:
        user_id = current_user["uid"]
        
        # Check if command exists
        existing_command = crud.get_user_command_by_id(db, command_id, user_id)
        if not existing_command:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Command not found"
            )
        
        # If updating trigger phrase, check for conflicts
        if command_update.trigger_phrase:
            conflicting_command = crud.get_user_command_by_trigger(
                db, user_id, command_update.trigger_phrase
            )
            if conflicting_command and conflicting_command.id != command_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="A command with this trigger phrase already exists"
                )
        
        # Update the command
        updated_command = crud.update_user_command(
            db=db,
            command_id=command_id,
            user_id=user_id,
            trigger_phrase=command_update.trigger_phrase,
            action_to_perform=command_update.action_to_perform
        )
        
        if not updated_command:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Command not found"
            )
        
        logger.info(f"Updated command {command_id} for user {user_id}")
        return updated_command
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating command: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update command"
        )

@router.delete("/{command_id}")
async def delete_command(
    command_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a specific command.
    """
    try:
        user_id = current_user["uid"]
        
        success = crud.delete_user_command(db, command_id, user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Command not found"
            )
        
        logger.info(f"Deleted command {command_id} for user {user_id}")
        return {"message": "Command deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting command: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete command"
        )

@router.get("/trigger/{trigger_phrase}", response_model=UserCommandResponse)
async def get_command_by_trigger(
    trigger_phrase: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a command by its trigger phrase (for internal use by the AI orchestrator).
    """
    try:
        user_id = current_user["uid"]
        
        command = crud.get_user_command_by_trigger(db, user_id, trigger_phrase)
        if not command:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Command not found"
            )
        
        return command
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching command by trigger: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch command"
        )
