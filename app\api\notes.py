from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import NoteC<PERSON>, NoteUpdate, NoteResponse, NoteListResponse

router = APIRouter(prefix="/api/v1/notes", tags=["Notes"])

@router.post("/", response_model=NoteResponse, status_code=status.HTTP_201_CREATED)
def create_note(
    note: NoteCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new note for the authenticated user with tags support"""
    user_id = current_user["uid"]
    db_note = crud.create_user_note(
        db=db,
        user_id=user_id,
        title=note.title or "",
        content=note.content,
        tags=note.tags or [],
        content_type=note.content_type or "plain",
        content_data=note.content_data,
        category=note.category,
        is_template=note.is_template or False,
        is_shared=note.is_shared or False,
        shared_with=note.shared_with or []
    )
    return db_note

@router.get("/", response_model=NoteListResponse)
def get_notes(
    skip: int = 0,
    limit: int = 100,
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    search: Optional[str] = Query(None, description="Search in title and content"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get notes for the authenticated user with filtering and search capabilities.

    - **skip**: Number of notes to skip (default: 0)
    - **limit**: Maximum number of notes to return (default: 100)
    - **tags**: Filter by one or more tags
    - **search**: Search query for title and content

    Returns notes ordered by last updated date.
    """
    user_id = current_user["uid"]
    notes = crud.get_user_notes(
        db=db,
        user_id=user_id,
        skip=skip,
        limit=limit,
        tags=tags,
        search_query=search
    )
    return NoteListResponse(notes=notes, total=len(notes))

@router.get("/{note_id}", response_model=NoteResponse)
def get_note(
    note_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific note by ID"""
    user_id = current_user["uid"]
    db_note = crud.get_note_by_id(db=db, note_id=note_id, user_id=user_id)
    if db_note is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found"
        )
    return db_note

@router.put("/{note_id}", response_model=NoteResponse)
def update_note(
    note_id: str,
    note_update: NoteUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a specific note"""
    user_id = current_user["uid"]
    
    # Convert Pydantic model to dict, excluding unset fields
    update_data = note_update.model_dump(exclude_unset=True)
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No fields to update"
        )
    
    db_note = crud.update_note(db=db, note_id=note_id, user_id=user_id, note_data=update_data)
    if db_note is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found"
        )
    return db_note

@router.delete("/{note_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_note(
    note_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a specific note"""
    user_id = current_user["uid"]
    success = crud.delete_note(db=db, note_id=note_id, user_id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found"
        )
    return None
