from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime
from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import (
    TaskCreate, TaskUpdate, TaskResponse, TaskListResponse,
    SubtaskCreate, SubtaskListResponse, TimeTrackingStart, TimeTrackingStop, TimeTrackingResponse
)

router = APIRouter(prefix="/api/v1/tasks", tags=["Tasks"])

@router.post("/", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
def create_task(
    task: TaskCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new task for the authenticated user with comprehensive task management features.

    This endpoint allows users to create detailed tasks with priority levels, due dates,
    tags for organization, and recurring task support. All tasks are automatically
    associated with the authenticated user.

    **Authentication Required**: Yes (Firebase ID token)

    **Request Body**:
    - **content**: Task description (1-1000 characters) - **Required**
    - **due_date**: Optional due date in ISO 8601 format (e.g., "2025-02-15T17:00:00Z")
    - **priority**: Task priority level (0=low, 1=medium, 2=high, 3=urgent) - Default: 0
    - **tags**: Array of strings for categorization (e.g., ["work", "urgent", "finance"])
    - **is_recurring**: Boolean indicating if task repeats - Default: false

    **Returns**:
    - **201 Created**: Task successfully created with full task details
    - **400 Bad Request**: Invalid input data (content too long, invalid priority, etc.)
    - **401 Unauthorized**: Invalid or expired authentication token

    **Features**:
    - Automatic task ID generation
    - User ownership assignment
    - Timestamp tracking (created_at, updated_at)
    - Tag-based organization
    - Priority-based sorting support
    """
    user_id = current_user["uid"]
    db_task = crud.create_user_task(
        db=db,
        user_id=user_id,
        content=task.content,
        due_date=task.due_date,
        priority=task.priority or 0,
        tags=task.tags or [],
        is_recurring=task.is_recurring or False,
        parent_task_id=task.parent_task_id,
        estimated_duration_minutes=task.estimated_duration_minutes,
        depends_on_task_ids=task.depends_on_task_ids or [],
        is_template=task.is_template or False,
        template_data=task.template_data,
        assigned_to_user_id=task.assigned_to_user_id
    )
    return db_task

@router.get("/", response_model=TaskListResponse)
def get_tasks(
    skip: int = 0,
    limit: int = 100,
    priority: Optional[int] = Query(None, ge=0, le=3, description="Filter by priority (0=low, 1=medium, 2=high, 3=urgent)"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    is_completed: Optional[bool] = Query(None, description="Filter by completion status"),
    is_recurring: Optional[bool] = Query(None, description="Filter by recurring status"),
    due_before: Optional[datetime] = Query(None, description="Filter tasks due before this date"),
    due_after: Optional[datetime] = Query(None, description="Filter tasks due after this date"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retrieve tasks for the authenticated user with powerful filtering and pagination capabilities.

    This endpoint provides comprehensive task management with multiple filtering options,
    pagination support, and intelligent sorting. Perfect for building task dashboards,
    filtered views, and productivity interfaces.

    **Authentication Required**: Yes (Firebase ID token)

    **Query Parameters**:
    - **skip**: Number of tasks to skip for pagination (default: 0)
    - **limit**: Maximum tasks to return (1-100, default: 100)
    - **priority**: Filter by priority level (0=low, 1=medium, 2=high, 3=urgent)
    - **tags**: Filter by one or more tags (e.g., ?tags=work&tags=urgent)
    - **is_completed**: Filter by completion status (true/false)
    - **is_recurring**: Filter by recurring status (true/false)
    - **due_before**: Filter tasks due before this date (ISO 8601 format)
    - **due_after**: Filter tasks due after this date (ISO 8601 format)

    **Returns**:
    - **200 OK**: List of tasks with total count
    - **401 Unauthorized**: Invalid or expired authentication token

    **Response includes**:
    - Array of task objects with full details
    - Total count of matching tasks (for pagination)
    - Tasks ordered by priority (urgent first) and due date
    """
    user_id = current_user["uid"]
    tasks = crud.get_user_tasks(
        db=db,
        user_id=user_id,
        skip=skip,
        limit=limit,
        priority=priority,
        tags=tags,
        is_completed=is_completed,
        is_recurring=is_recurring,
        due_before=due_before,
        due_after=due_after
    )
    return TaskListResponse(tasks=tasks, total=len(tasks))

@router.get("/{task_id}", response_model=TaskResponse)
def get_task(
    task_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific task by ID"""
    user_id = current_user["uid"]
    db_task = crud.get_task_by_id(db=db, task_id=task_id, user_id=user_id)
    if db_task is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    return db_task

@router.put("/{task_id}", response_model=TaskResponse)
def update_task(
    task_id: str,
    task_update: TaskUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a specific task"""
    user_id = current_user["uid"]
    
    # Convert Pydantic model to dict, excluding unset fields
    update_data = task_update.model_dump(exclude_unset=True)
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No fields to update"
        )
    
    db_task = crud.update_task(db=db, task_id=task_id, user_id=user_id, task_data=update_data)
    if db_task is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    return db_task

@router.delete("/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_task(
    task_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a specific task"""
    user_id = current_user["uid"]
    success = crud.delete_task(db=db, task_id=task_id, user_id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    return {"message": "Task deleted successfully"}

@router.post("/{task_id}/subtasks", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
def create_subtask(
    task_id: str,
    subtask: SubtaskCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a subtask under a parent task.

    **Authentication Required**: Yes (Firebase ID token)

    **Path Parameters**:
    - **task_id**: ID of the parent task

    **Request Body**:
    - **content**: Subtask description - **Required**
    - **priority**: Subtask priority (0-3) - Default: 0

    **Returns**:
    - **201 Created**: Subtask successfully created
    - **404 Not Found**: Parent task not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    # Verify parent task exists
    parent_task = crud.get_task_by_id(db=db, task_id=task_id, user_id=user_id)
    if not parent_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Parent task not found"
        )

    try:
        db_subtask = crud.create_subtask(
            db=db,
            user_id=user_id,
            parent_task_id=task_id,
            content=subtask.content,
            priority=subtask.priority or 0
        )

        if not db_subtask:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create subtask"
            )

        return db_subtask

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create subtask: {str(e)}"
        )

@router.get("/{task_id}/subtasks", response_model=SubtaskListResponse)
def get_subtasks(
    task_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all subtasks for a parent task.

    **Authentication Required**: Yes (Firebase ID token)

    **Path Parameters**:
    - **task_id**: ID of the parent task

    **Returns**:
    - **200 OK**: List of subtasks
    - **404 Not Found**: Parent task not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    # Verify parent task exists
    parent_task = crud.get_task_by_id(db=db, task_id=task_id, user_id=user_id)
    if not parent_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Parent task not found"
        )

    try:
        subtasks = crud.get_subtasks(db=db, parent_task_id=task_id, user_id=user_id)
        return SubtaskListResponse(subtasks=subtasks, total=len(subtasks))

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve subtasks: {str(e)}"
        )

@router.post("/{task_id}/time-tracking/start", response_model=TimeTrackingResponse)
def start_time_tracking(
    task_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Start time tracking for a task.

    **Authentication Required**: Yes (Firebase ID token)

    **Path Parameters**:
    - **task_id**: ID of the task to start tracking

    **Returns**:
    - **200 OK**: Time tracking started
    - **404 Not Found**: Task not found
    - **400 Bad Request**: Time tracking already active
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    # Verify task exists
    task = crud.get_task_by_id(db=db, task_id=task_id, user_id=user_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )

    if task.time_tracking_started_at:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Time tracking already active for this task"
        )

    try:
        updated_task = crud.start_time_tracking(db=db, task_id=task_id, user_id=user_id)

        return TimeTrackingResponse(
            task_id=task_id,
            started_at=updated_task.time_tracking_started_at,
            duration_minutes=None,
            is_tracking=True
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start time tracking: {str(e)}"
        )

@router.post("/{task_id}/time-tracking/stop", response_model=TimeTrackingResponse)
def stop_time_tracking(
    task_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Stop time tracking for a task.

    **Authentication Required**: Yes (Firebase ID token)

    **Path Parameters**:
    - **task_id**: ID of the task to stop tracking

    **Returns**:
    - **200 OK**: Time tracking stopped with duration
    - **404 Not Found**: Task not found
    - **400 Bad Request**: Time tracking not active
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    # Verify task exists
    task = crud.get_task_by_id(db=db, task_id=task_id, user_id=user_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )

    if not task.time_tracking_started_at:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Time tracking not active for this task"
        )

    try:
        updated_task = crud.stop_time_tracking(db=db, task_id=task_id, user_id=user_id)

        return TimeTrackingResponse(
            task_id=task_id,
            started_at=None,
            duration_minutes=updated_task.actual_duration_minutes,
            is_tracking=False
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop time tracking: {str(e)}"
        )
