from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import UserResponse, UserCreate
import cloudinary
import cloudinary.uploader
import cloudinary.api
from app.core.config import settings

router = APIRouter(prefix="/api/v1/users", tags=["Users"])

# Configure Cloudinary
cloudinary.config(
    cloud_name=settings.cloudinary_cloud_name,
    api_key=settings.cloudinary_api_key,
    api_secret=settings.cloudinary_api_secret
)

# Pydantic schemas for session tracking
class SessionCreate(BaseModel):
    timestamp: datetime
    session_type: str = Field(..., description="Type of session (e.g., 'app_open')")

class SessionResponse(BaseModel):
    success: bool
    session_id: str
    daily_count: int

class SessionDataResponse(BaseModel):
    daily_sessions: int
    last_session: str = None
    first_session_today: str = None
    total_sessions: int

# Enhanced profile schemas
class ProfileUpdateRequest(BaseModel):
    display_name: str = None
    darvis_name: str = None
    profile_picture_url: str = None
    profile_picture_public_id: str = None
    time_zone: str = None
    language: str = None
    theme: str = None
    biometric_auth_enabled: bool = None
    session_timeout: str = None
    app_lock_required: bool = None
    cloud_sync_enabled: bool = None
    auto_backup_enabled: bool = None

class ProfilePictureRequest(BaseModel):
    cloudinary_url: str
    cloudinary_public_id: str
    previous_public_id: str = None

class ProfilePictureResponse(BaseModel):
    success: bool
    profile_picture_updated: bool = False
    profile_picture_deleted: bool = False
    previous_image_deleted: bool = False
    cloudinary_image_deleted: bool = False

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
def register_user(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Register a new user in the Darvis database using Firebase authentication data.

    This endpoint creates a user profile in the Darvis database after successful Firebase authentication.
    It should be called by the Flutter app the first time a user logs in to establish their account.

    **Authentication Required**: Yes (Firebase ID token)

    **Process**:
    1. Validates the Firebase ID token from the Authorization header
    2. Extracts user information (UID, email) from the token
    3. Checks if the user already exists in the database
    4. Creates a new user record if they don't exist
    5. Returns the user profile information

    **Returns**:
    - **201 Created**: User successfully registered or already exists
    - **400 Bad Request**: Email is missing from Firebase token
    - **409 Conflict**: Email is already registered with another account
    - **500 Internal Server Error**: Database error during user creation

    **Note**: If the user already exists, this endpoint returns their existing profile
    instead of creating a duplicate.
    """
    user_id = current_user["uid"]
    email = current_user.get("email")
    
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email is required for user registration"
        )
    
    # Check if user already exists
    existing_user = crud.get_user_by_id(db=db, user_id=user_id)
    if existing_user:
        # User already exists, return existing user data
        return existing_user
    
    # Check if email is already in use by another user
    existing_email_user = crud.get_user_by_email(db=db, email=email)
    if existing_email_user:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Email is already registered with another account"
        )
    
    # Create new user
    try:
        db_user = crud.create_user(db=db, user_id=user_id, email=email)
        return db_user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user account"
        )

@router.get("/profile", response_model=UserResponse)
def get_user_profile(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retrieve the authenticated user's profile information.

    This endpoint returns the complete profile data for the currently authenticated user,
    including their email, registration date, and last update timestamp.

    **Authentication Required**: Yes (Firebase ID token)

    **Returns**:
    - **200 OK**: User profile data successfully retrieved
    - **404 Not Found**: User profile not found (user needs to register first)
    - **401 Unauthorized**: Invalid or expired authentication token

    **Response includes**:
    - User ID (Firebase UID)
    - Email address
    - Account creation timestamp
    - Last profile update timestamp
    """
    user_id = current_user["uid"]
    db_user = crud.get_user_by_id(db=db, user_id=user_id)
    
    if db_user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found. Please register first."
        )
    
    return db_user


@router.post("/session", response_model=SessionResponse, status_code=status.HTTP_201_CREATED)
def create_user_session(
    session_data: SessionCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new user session for dynamic greeting system.

    This endpoint tracks when users open the app to enable intelligent greeting logic.
    It records session data and returns the session count for the day.

    **Authentication Required**: Yes (Firebase ID token)

    **Request Body**:
    - **timestamp**: ISO timestamp of when the session started
    - **session_type**: Type of session (e.g., 'app_open', 'app_resume')

    **Returns**:
    - **201 Created**: Session successfully created
    - **400 Bad Request**: Invalid session data
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        # Create the session record
        session = crud.create_user_session(
            db=db,
            user_id=user_id,
            session_type=session_data.session_type,
            timestamp=session_data.timestamp
        )

        # Get today's session count
        today_sessions = crud.get_user_sessions_today(db=db, user_id=user_id)
        daily_count = len(today_sessions)

        # Update user's last activity
        crud.update_user_activity(db=db, user_id=user_id)

        return SessionResponse(
            success=True,
            session_id=session.id,
            daily_count=daily_count
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create session: {str(e)}"
        )


@router.get("/session-data", response_model=SessionDataResponse)
def get_user_session_data(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user session analytics data for dynamic greeting system.

    This endpoint provides session statistics needed for intelligent greeting logic,
    including daily session count, last session time, and total sessions.

    **Authentication Required**: Yes (Firebase ID token)

    **Returns**:
    - **200 OK**: Session data successfully retrieved
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        session_data = crud.get_user_session_data(db=db, user_id=user_id)

        return SessionDataResponse(
            daily_sessions=session_data["daily_sessions"],
            last_session=session_data["last_session"],
            first_session_today=session_data["first_session_today"],
            total_sessions=session_data["total_sessions"]
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve session data: {str(e)}"
        )


@router.put("/profile", response_model=UserResponse)
def update_user_profile(
    profile_data: ProfileUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update the authenticated user's profile information.

    This endpoint allows users to update their profile data including display name,
    Drix name, settings, and preferences. Only provided fields will be updated.

    **Authentication Required**: Yes (Firebase ID token)

    **Request Body**: Any combination of profile fields to update

    **Returns**:
    - **200 OK**: Profile successfully updated
    - **404 Not Found**: User profile not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    # Convert Pydantic model to dict, excluding None values
    update_data = {k: v for k, v in profile_data.dict().items() if v is not None}

    try:
        updated_user = crud.update_user_profile(db=db, user_id=user_id, profile_data=update_data)

        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        return updated_user

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update profile: {str(e)}"
        )


@router.post("/profile-picture", response_model=ProfilePictureResponse)
def update_profile_picture(
    picture_data: ProfilePictureRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update the authenticated user's profile picture.

    This endpoint handles Cloudinary profile picture updates, including cleanup
    of previous images when a new one is uploaded.

    **Authentication Required**: Yes (Firebase ID token)

    **Request Body**:
    - **cloudinary_url**: New Cloudinary image URL
    - **cloudinary_public_id**: New Cloudinary public ID for deletion
    - **previous_public_id**: Previous image public ID to delete (optional)

    **Returns**:
    - **200 OK**: Profile picture successfully updated
    - **404 Not Found**: User profile not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        # Update profile picture in database
        update_data = {
            "profile_picture_url": picture_data.cloudinary_url,
            "profile_picture_public_id": picture_data.cloudinary_public_id
        }

        updated_user = crud.update_user_profile(db=db, user_id=user_id, profile_data=update_data)

        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        # ✅ NEW: Actually delete previous image
        previous_deleted = False  # Initialize to False
        if picture_data.previous_public_id:
            try:
                result = cloudinary.uploader.destroy(picture_data.previous_public_id)
                previous_deleted = result.get('result') == 'ok'
            except Exception as e:
                previous_deleted = False

        return ProfilePictureResponse(
            success=True,
            profile_picture_updated=True,
            previous_image_deleted=previous_deleted,
            cloudinary_image_deleted=previous_deleted
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update profile picture: {str(e)}"
        )


@router.delete("/profile-picture", response_model=ProfilePictureResponse)
def delete_profile_picture(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete the authenticated user's profile picture.

    This endpoint removes the profile picture from both the database and Cloudinary.

    **Authentication Required**: Yes (Firebase ID token)

    **Returns**:
    - **200 OK**: Profile picture successfully deleted
    - **404 Not Found**: User profile not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        # Get current user to access public_id
        user = crud.get_user_by_id(db=db, user_id=user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        # Clear profile picture fields
        update_data = {
            "profile_picture_url": None,
            "profile_picture_public_id": None
        }

        updated_user = crud.update_user_profile(db=db, user_id=user_id, profile_data=update_data)

        # ✅ NEW: Actually delete from Cloudinary
        if user.profile_picture_public_id:
            try:
                result = cloudinary.uploader.destroy(user.profile_picture_public_id)
                cloudinary_deleted = result.get('result') == 'ok'
            except Exception as e:
                cloudinary_deleted = False
        else:
            cloudinary_deleted = False

        return ProfilePictureResponse(
            success=True,
            profile_picture_deleted=True,
            cloudinary_image_deleted=cloudinary_deleted
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete profile picture: {str(e)}"
        )
