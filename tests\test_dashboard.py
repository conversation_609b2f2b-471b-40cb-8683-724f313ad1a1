"""
Tests for the /api/v1/dashboard/daily-summary endpoint.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import MagicMock, patch

from app.main import app
from app.api.auth import get_current_user
from app.db.database import get_db
from app.db.models import User, Task, Note, ActionLog
from datetime import datetime, timedelta

# Mock user data that the overridden dependency will return
MOCK_USER_ID = "test_auth_user_123"
MOCK_USER_EMAIL = "<EMAIL>"

# Override the get_current_user dependency to simulate an authenticated user
def override_get_current_user():
    return {"uid": MOCK_USER_ID, "email": MOCK_USER_EMAIL}

# In-memory SQLite database for testing
@pytest.fixture(scope="module")
def db_session_mock():
    """
    Pytest fixture to provide a mocked database session.
    This avoids hitting the actual database and allows for controlled testing.
    """
    db = MagicMock(spec=Session)
    
    # Mock user
    mock_user = User(
        id=MOCK_USER_ID,
        email=MOCK_USER_EMAIL,
        display_name="Auth Tester",
        created_at=datetime.now() - timedelta(days=10)
    )
    
    # Mock tasks
    mock_tasks = [
        Task(id="task1", content="Due today task", due_date=datetime.now(), is_completed=False, priority=1, tags=["test"], owner_id=MOCK_USER_ID),
        Task(id="task2", content="Overdue task", due_date=datetime.now() - timedelta(days=1), is_completed=False, priority=2, tags=[], owner_id=MOCK_USER_ID),
        Task(id="task3", content="Completed task", due_date=datetime.now(), is_completed=True, priority=0, tags=[], owner_id=MOCK_USER_ID),
    ]

    # Mock notes
    mock_notes = [
        Note(id="note1", title="Recent Note 1", content="This is a test note.", tags=["testing"], created_at=datetime.now(), owner_id=MOCK_USER_ID)
    ]

    # Mock ActionLog for login tracking
    mock_logins = [
        ActionLog(conversation_id=f"login_{MOCK_USER_ID}", tool_name="user_login", created_at=datetime.now() - timedelta(days=1)),
        ActionLog(conversation_id=f"login_{MOCK_USER_ID}", tool_name="user_login", created_at=datetime.now() - timedelta(days=2)),
    ]

    # Configure the mock session's query method
    db.query.return_value.filter.return_value.count.side_effect = [
        0, # is_first_login_today check
        len(mock_tasks), # total_tasks
        1, # completed_today
        len(mock_notes), # total_notes
        1, # notes_today
    ]
    db.query.return_value.filter.return_value.all.side_effect = [
        [(datetime.now().date() - timedelta(days=1),), (datetime.now().date() - timedelta(days=2),)], # streak check
    ]
    db.query.return_value.distinct.return_value.order_by.return_value.all.side_effect = [
        [(datetime.now().date() - timedelta(days=1),), (datetime.now().date() - timedelta(days=2),)], # streak check
    ]


    with patch('app.db.crud.get_user_by_id', return_value=mock_user), \
         patch('app.db.crud.get_tasks_due_today', return_value=[t for t in mock_tasks if not t.is_completed]), \
         patch('app.db.crud.get_user_notes', return_value=mock_notes), \
         patch('app.db.crud.count_user_tasks', return_value=len(mock_tasks)), \
         patch('app.db.crud.get_tasks_completed_today', return_value=[t for t in mock_tasks if t.is_completed]), \
         patch('app.db.crud.count_user_notes', return_value=len(mock_notes)), \
         patch('app.db.crud.get_notes_created_today', return_value=mock_notes):
        yield db


# Apply dependency overrides for all tests in this module
@pytest.fixture(scope="module", autouse=True)
def apply_overrides(db_session_mock):
    app.dependency_overrides[get_current_user] = override_get_current_user
    app.dependency_overrides[get_db] = lambda: db_session_mock
    yield
    app.dependency_overrides.clear()

client = TestClient(app)

def test_get_daily_summary_authenticated():
    """
    Tests the /api/v1/dashboard/daily-summary endpoint with a mocked authenticated user.
    Verifies the status code and the structure of the JSON response.
    """
    # The 'Authorization' header is not strictly needed here because of the dependency override,
    # but we include it to represent a real-world request.
    headers = {"Authorization": "Bearer fake-firebase-token"}
    
    response = client.get("/api/v1/dashboard/daily-summary", headers=headers)
    
    assert response.status_code == 200
    
    data = response.json()
    
    # Verify top-level structure
    assert "user_profile" in data
    assert "greeting" in data
    assert "quick_stats" in data
    assert "today_tasks" in data
    assert "recent_notes" in data
    assert "generated_at" in data
    
    # Verify user_profile structure
    user_profile = data["user_profile"]
    assert user_profile["id"] == MOCK_USER_ID
    assert user_profile["email"] == MOCK_USER_EMAIL
    assert "display_name" in user_profile
    assert "created_at" in user_profile
    assert "member_since_days" in user_profile
    
    # Verify greeting structure
    greeting = data["greeting"]
    assert "greeting_message" in greeting
    assert "time_of_day" in greeting
    assert "is_first_login_today" in greeting
    assert "login_count_today" in greeting
    assert "current_streak" in greeting
    
    # Verify quick_stats structure
    quick_stats = data["quick_stats"]
    assert "total_tasks" in quick_stats
    assert "completed_tasks_today" in quick_stats
    assert "pending_tasks" in quick_stats
    assert "overdue_tasks" in quick_stats
    assert "total_notes" in quick_stats
    assert "notes_created_today" in quick_stats
    
    # Verify tasks and notes are lists
    assert isinstance(data["today_tasks"], list)
    assert isinstance(data["recent_notes"], list)

    # Verify a sample task
    if data["today_tasks"]:
        task = data["today_tasks"][0]
        assert "id" in task
        assert "content" in task
        assert "is_completed" in task
        assert "is_overdue" in task

    # Verify a sample note
    if data["recent_notes"]:
        note = data["recent_notes"][0]
        assert "id" in note
        assert "title" in note
        assert "content" in note
        assert "created_at" in note
