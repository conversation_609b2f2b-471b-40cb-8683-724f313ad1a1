from fastapi.testclient import TestClient
from app.main import app

# Create a test client that can make requests to our app
client = TestClient(app)

def test_read_root():
    """
    Tests that the root health check endpoint is working correctly.
    """
    # Make a GET request to the "/" endpoint
    response = client.get("/")
    # Assert that the HTTP status code is 200 (OK)
    assert response.status_code == 200
    # Assert that the response body is what we expect
    assert response.json() == {"status": "ok", "message": "Darvis API is online."}
