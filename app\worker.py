"""
Celery worker for background processing of inbox items.

This module handles the asynchronous processing of captured URLs including
content extraction via Jina AI Reader and AI-powered summarization.
"""

import os
import logging
from celery import Celery
from sqlalchemy.orm import Session
from app.db.database import get_db, SessionLocal
from app.db import crud
from app.sdk.jina_reader_sdk import get_content_from_url
from app.sdk.llm_provider_sdk import llm_provider
from app.sdk.notification_delivery_sdk import notification_delivery_sdk
from typing import Optional, Dict, Any
from datetime import datetime, timedelta, timezone

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Celery
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", REDIS_URL)
CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", REDIS_URL)

celery_app = Celery(
    "darvis_worker",
    broker=CELERY_BROKER_URL,
    backend=CELERY_RESULT_BACKEND,
    include=["app.worker"]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=300,  # 5 minutes max per task
    task_soft_time_limit=240,  # 4 minutes soft limit
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

def get_db_session() -> Session:
    """Get a database session for Celery tasks."""
    return SessionLocal()

@celery_app.task(bind=True, name="process_inbox_item")
def process_inbox_item(self, item_id: str):
    """
    Process an inbox item: extract content, generate summary, and update status.
    
    This task performs the following steps:
    1. Update status to "processing"
    2. Fetch the original URL from database
    3. Extract clean content using Jina AI Reader
    4. Generate AI summary of the content
    5. Save results and update status to "complete"
    6. Handle errors by setting status to "failed"
    
    Args:
        item_id: The ID of the inbox item to process
        
    Returns:
        Dictionary with processing results
    """
    db = None
    try:
        logger.info(f"Starting processing for inbox item: {item_id}")
        
        # Get database session
        db = get_db_session()
        
        # Step 1: Update status to processing
        db_item = crud.update_inbox_item_status(db, item_id, "processing")
        if not db_item:
            logger.error(f"Inbox item not found: {item_id}")
            return {"success": False, "error": "Item not found"}
        
        original_url = db_item.original_url
        logger.info(f"Processing URL: {original_url}")
        
        # Step 2: Extract content using Jina AI Reader
        content_result = get_content_from_url(original_url, timeout=60)
        
        if not content_result["success"]:
            error_msg = f"Content extraction failed: {content_result['error']}"
            logger.error(error_msg)
            crud.update_inbox_item_status(db, item_id, "failed")
            return {"success": False, "error": error_msg}
        
        clean_content = content_result["content"]
        logger.info(f"Extracted {len(clean_content)} characters of content")
        
        # Step 3: Save clean content to database
        crud.update_inbox_item_content(db, item_id, clean_content=clean_content)
        
        # Step 4: Generate AI summary
        summary = generate_content_summary(clean_content)
        
        # Step 5: Save summary and update status to complete
        crud.update_inbox_item_content(db, item_id, summary=summary)
        crud.update_inbox_item_status(db, item_id, "complete")

        # Step 6: Trigger completion notification
        send_inbox_completion_notification.delay(item_id)

        logger.info(f"Successfully processed inbox item: {item_id}")

        return {
            "success": True,
            "item_id": item_id,
            "content_length": len(clean_content),
            "summary_length": len(summary) if summary else 0
        }
        
    except Exception as e:
        error_msg = f"Unexpected error processing inbox item {item_id}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        # Update status to failed
        if db:
            try:
                crud.update_inbox_item_status(db, item_id, "failed")
            except Exception as db_error:
                logger.error(f"Failed to update status to failed: {db_error}")
        
        return {"success": False, "error": error_msg}
        
    finally:
        if db:
            db.close()

def generate_content_summary(content: str) -> Optional[str]:
    """
    Generate an AI-powered summary of the extracted content.
    
    Args:
        content: The clean content to summarize
        
    Returns:
        Generated summary or None if generation fails
    """
    try:
        if not content or len(content.strip()) < 50:
            return "Content too short to summarize."
        
        # Truncate content if too long (to avoid token limits)
        max_content_length = 8000  # Adjust based on model limits
        if len(content) > max_content_length:
            content = content[:max_content_length] + "..."
        
        # Create summarization prompt
        prompt = f"""Please provide a concise summary of the following content. Focus on the main points, key insights, and important information. Keep the summary between 2-4 sentences.

Content:
{content}

Summary:"""
        
        # Generate summary using LLM
        response = llm_provider.invoke([{"role": "user", "content": prompt}])
        
        if hasattr(response, 'content'):
            summary = response.content.strip()
        else:
            summary = str(response).strip()
        
        # Validate summary length
        if len(summary) > 1000:
            summary = summary[:1000] + "..."
        
        logger.info(f"Generated summary of {len(summary)} characters")
        return summary
        
    except Exception as e:
        logger.error(f"Error generating summary: {e}")
        return "Failed to generate summary due to processing error."

# Task for testing Celery setup
@celery_app.task(name="test_task")
def test_task(message: str = "Hello from Celery!"):
    """Simple test task to verify Celery is working."""
    logger.info(f"Test task executed: {message}")
    return {"message": message, "status": "success"}

# Health check task
@celery_app.task(name="health_check")
def health_check():
    """Health check task for monitoring."""
    return {"status": "healthy", "worker": "darvis_worker"}

# Notification Tasks
@celery_app.task(bind=True, name="send_task_reminder_notification")
def send_task_reminder_notification(self, task_id: str, user_id: str):
    """
    Send a task reminder notification for a specific task.

    Args:
        task_id: The ID of the task to remind about
        user_id: The user ID who owns the task
    """
    db = get_db_session()

    try:
        from app.db import crud
        from app.sdk.notification_delivery_sdk import notification_delivery_sdk
        from datetime import datetime, timedelta

        # Get the task
        task = crud.get_task_by_id(db, task_id, user_id)
        if not task:
            logger.warning(f"Task not found: {task_id}")
            return {"success": False, "error": "Task not found"}

        # Get user and FCM token
        user = crud.get_user_by_id(db, user_id)
        if not user or not user.fcm_token:
            logger.warning(f"User not found or no FCM token: {user_id}")
            return {"success": False, "error": "User not found or no FCM token"}

        # Get notification preferences
        preferences = crud.get_or_create_notification_preferences(db, user_id)
        if not preferences.task_reminders_enabled:
            logger.info(f"Task reminders disabled for user: {user_id}")
            return {"success": False, "error": "Task reminders disabled"}

        # Check if we're in quiet hours
        current_hour = datetime.now().hour
        if preferences.quiet_hours_start <= current_hour or current_hour <= preferences.quiet_hours_end:
            logger.info(f"In quiet hours for user: {user_id}")
            return {"success": False, "error": "In quiet hours"}

        # Calculate relevance score
        priority_weight = task.priority * 0.4
        urgency_weight = 0.6 if task.due_date and task.due_date <= datetime.now() + timedelta(days=1) else 0.3
        relevance_score = priority_weight + urgency_weight

        # Only send if relevance score is high enough
        if relevance_score < 0.5:
            logger.info(f"Relevance score too low: {relevance_score}")
            return {"success": False, "error": "Relevance score too low"}

        # Create notification content
        priority_text = ["Low", "Medium", "High", "Urgent"][task.priority] if task.priority <= 3 else "High"

        if task.due_date:
            time_until_due = task.due_date - datetime.now()
            if time_until_due.days == 0:
                due_text = "today"
            elif time_until_due.days == 1:
                due_text = "tomorrow"
            else:
                due_text = f"in {time_until_due.days} days"
        else:
            due_text = "soon"

        title = f"{priority_text} Priority Task Due"
        body = f"'{task.content[:50]}...' is due {due_text}"

        # Send notification
        result = notification_delivery_sdk.send_push_notification(
            fcm_token=user.fcm_token,
            title=title,
            body=body,
            data={
                "type": "task_reminder",
                "task_id": task_id,
                "priority": str(task.priority)
            }
        )

        # Record notification history
        crud.create_notification_history(
            db=db,
            user_id=user_id,
            notification_type="task_reminder",
            title=title,
            content=body,
            relevance_score=relevance_score
        )

        logger.info(f"Task reminder notification sent for task: {task_id}")
        return {"success": True, "task_id": task_id, "relevance_score": relevance_score}

    except Exception as e:
        logger.error(f"Error sending task reminder notification: {e}")
        return {"success": False, "error": str(e)}

    finally:
        db.close()

@celery_app.task(bind=True, name="send_inbox_completion_notification")
def send_inbox_completion_notification(self, inbox_item_id: str):
    """
    Send notification when inbox item processing is complete.

    Args:
        inbox_item_id: The ID of the completed inbox item
    """
    db = get_db_session()

    try:
        from app.db import crud
        from app.sdk.notification_delivery_sdk import notification_delivery_sdk

        # Get the inbox item
        inbox_item = crud.get_inbox_item_by_id(db, inbox_item_id)
        if not inbox_item:
            logger.warning(f"Inbox item not found: {inbox_item_id}")
            return {"success": False, "error": "Inbox item not found"}

        user_id = inbox_item.owner_id

        # Get user and FCM token
        user = crud.get_user_by_id(db, user_id)
        if not user or not user.fcm_token:
            logger.warning(f"User not found or no FCM token: {user_id}")
            return {"success": False, "error": "User not found or no FCM token"}

        # Get notification preferences
        preferences = crud.get_or_create_notification_preferences(db, user_id)
        if not preferences.inbox_updates_enabled:
            logger.info(f"Inbox notifications disabled for user: {user_id}")
            return {"success": False, "error": "Inbox notifications disabled"}

        # Create notification content
        title = "Content Processed"
        summary_preview = inbox_item.summary[:100] + "..." if inbox_item.summary and len(inbox_item.summary) > 100 else inbox_item.summary or "Processing complete"
        body = f"Your saved content has been processed. {summary_preview}"

        # Send notification
        result = notification_delivery_sdk.send_push_notification(
            fcm_token=user.fcm_token,
            title=title,
            body=body,
            data={
                "type": "inbox_completion",
                "inbox_item_id": inbox_item_id
            }
        )

        # Record notification history
        crud.create_notification_history(
            db=db,
            user_id=user_id,
            notification_type="inbox_completion",
            title=title,
            content=body,
            relevance_score=0.8  # High relevance for completed processing
        )

        logger.info(f"Inbox completion notification sent for item: {inbox_item_id}")
        return {"success": True, "inbox_item_id": inbox_item_id}

    except Exception as e:
        logger.error(f"Error sending inbox completion notification: {e}")
        return {"success": False, "error": str(e)}

    finally:
        db.close()

@celery_app.task(bind=True, name="check_due_tasks_and_send_reminders")
def check_due_tasks_and_send_reminders(self):
    """
    Periodic task to check for due tasks and send reminder notifications.
    This task should be scheduled to run every hour.
    """
    db = get_db_session()

    try:
        from app.db import crud
        from datetime import datetime, timedelta

        # Get all users with FCM tokens
        users = db.query(crud.User).filter(crud.User.fcm_token.isnot(None)).all()

        reminder_count = 0

        for user in users:
            # Get user's notification preferences
            preferences = crud.get_notification_preferences(db, user.id)
            if not preferences or not preferences.task_reminders_enabled:
                continue

            # Check if we've already sent max notifications today
            today_notifications = crud.get_notification_history(
                db=db,
                user_id=user.id,
                skip=0,
                limit=100,
                notification_type="task_reminder"
            )

            today_count = len([n for n in today_notifications if n.sent_at.date() == datetime.now().date()])
            if today_count >= preferences.max_notifications_per_day:
                continue

            # Get tasks due in the next 24 hours
            tomorrow = datetime.now() + timedelta(days=1)
            due_tasks = db.query(crud.Task).filter(
                crud.Task.owner_id == user.id,
                crud.Task.is_completed == False,
                crud.Task.due_date.isnot(None),
                crud.Task.due_date <= tomorrow
            ).all()

            # Send reminders for high-priority tasks
            for task in due_tasks:
                if task.priority >= 2:  # High or urgent priority
                    send_task_reminder_notification.delay(task.id, user.id)
                    reminder_count += 1

        logger.info(f"Scheduled {reminder_count} task reminder notifications")
        return {"success": True, "reminders_scheduled": reminder_count}

    except Exception as e:
        logger.error(f"Error checking due tasks: {e}")
        return {"success": False, "error": str(e)}

    finally:
        db.close()


# Therapy-specific notification functions
def send_therapy_session_reminder(user_id: str) -> Dict[str, Any]:
    """
    Send a gentle therapy session reminder notification.

    This function sends supportive, non-demanding reminders for users who haven't
    engaged with therapy mode recently, respecting their emotional boundaries.
    """
    db = SessionLocal()

    try:
        # Get user and check if they exist
        user = crud.get_user_by_id(db, user_id)
        if not user or not user.fcm_token:
            return {"success": False, "error": "User not found or no FCM token"}

        # Check notification preferences
        preferences = crud.get_or_create_notification_preferences(db, user_id)
        if not preferences.therapy_session_reminders_enabled:
            return {"success": False, "error": "Therapy session reminders disabled"}

        # Check if user has had recent therapy sessions
        recent_sessions = crud.get_user_therapy_sessions(db, user_id, limit=1, include_active=False)

        # Calculate days since last session
        days_since_last = 0
        if recent_sessions:
            last_session = recent_sessions[0]
            if last_session.ended_at:
                days_since_last = (datetime.now() - last_session.ended_at).days

        # Only send reminder if it's been 3+ days since last session
        if days_since_last < 3:
            return {"success": False, "error": "Too soon for reminder"}

        # Create gentle, supportive notification content
        title = "Your therapy space is here"
        body = "When you're ready, your supportive space is waiting. No pressure, just care."

        # Send notification
        result = notification_delivery_sdk.send_push_notification(
            fcm_token=user.fcm_token,
            title=title,
            body=body,
            data={
                "type": "therapy_session_reminder",
                "days_since_last": str(days_since_last)
            }
        )

        # Record notification history
        crud.create_notification_history(
            db=db,
            user_id=user_id,
            notification_type="therapy_session_reminder",
            title=title,
            content=body,
            relevance_score=0.7  # Moderate relevance for gentle reminders
        )

        logger.info(f"Therapy session reminder sent to user: {user_id}")
        return {"success": True, "user_id": user_id, "days_since_last": days_since_last}

    except Exception as e:
        logger.error(f"Error sending therapy session reminder: {e}")
        return {"success": False, "error": str(e)}

    finally:
        db.close()


def send_therapy_practice_followup(user_id: str, task_name: str) -> Dict[str, Any]:
    """
    Send a gentle follow-up notification for therapy practice tasks.

    This function sends supportive check-ins about practice tasks assigned
    during therapy sessions, encouraging progress without pressure.
    """
    db = SessionLocal()

    try:
        # Get user and check if they exist
        user = crud.get_user_by_id(db, user_id)
        if not user or not user.fcm_token:
            return {"success": False, "error": "User not found or no FCM token"}

        # Check notification preferences
        preferences = crud.get_or_create_notification_preferences(db, user_id)
        if not preferences.therapy_practice_followups_enabled:
            return {"success": False, "error": "Therapy practice follow-ups disabled"}

        # Create supportive notification content
        title = "How's your practice going?"
        body = f"Just checking in on your {task_name}. Every small step counts, and you're doing great."

        # Send notification
        result = notification_delivery_sdk.send_push_notification(
            fcm_token=user.fcm_token,
            title=title,
            body=body,
            data={
                "type": "therapy_practice_followup",
                "task_name": task_name
            }
        )

        # Record notification history
        crud.create_notification_history(
            db=db,
            user_id=user_id,
            notification_type="therapy_practice_followup",
            title=title,
            content=body,
            relevance_score=0.8  # Higher relevance for practice support
        )

        logger.info(f"Therapy practice follow-up sent to user: {user_id} for task: {task_name}")
        return {"success": True, "user_id": user_id, "task_name": task_name}

    except Exception as e:
        logger.error(f"Error sending therapy practice follow-up: {e}")
        return {"success": False, "error": str(e)}

    finally:
        db.close()


def send_therapy_progress_celebration(user_id: str, achievement: str) -> Dict[str, Any]:
    """
    Send a celebration notification for therapy progress milestones.

    This function acknowledges and celebrates user's therapeutic progress,
    reinforcing positive patterns and building motivation.
    """
    db = SessionLocal()

    try:
        # Get user and check if they exist
        user = crud.get_user_by_id(db, user_id)
        if not user or not user.fcm_token:
            return {"success": False, "error": "User not found or no FCM token"}

        # Check notification preferences
        preferences = crud.get_or_create_notification_preferences(db, user_id)
        if not preferences.therapy_progress_celebrations_enabled:
            return {"success": False, "error": "Therapy progress celebrations disabled"}

        # Create celebratory notification content
        title = "Celebrating your progress! 🌟"
        body = f"You've {achievement}. This is real progress, and you should feel proud of yourself."

        # Send notification
        result = notification_delivery_sdk.send_push_notification(
            fcm_token=user.fcm_token,
            title=title,
            body=body,
            data={
                "type": "therapy_progress_celebration",
                "achievement": achievement
            }
        )

        # Record notification history
        crud.create_notification_history(
            db=db,
            user_id=user_id,
            notification_type="therapy_progress_celebration",
            title=title,
            content=body,
            relevance_score=0.9  # High relevance for positive reinforcement
        )

        logger.info(f"Therapy progress celebration sent to user: {user_id} for: {achievement}")
        return {"success": True, "user_id": user_id, "achievement": achievement}

    except Exception as e:
        logger.error(f"Error sending therapy progress celebration: {e}")
        return {"success": False, "error": str(e)}

    finally:
        db.close()


# Notification Scheduling Tasks
@celery_app.task(bind=True, name="process_scheduled_notifications")
def process_scheduled_notifications(self):
    """
    Process and send scheduled notifications that are due.
    This task should be run every minute to check for due notifications.
    """
    db = get_db_session()

    try:
        from app.db import crud
        from app.sdk.notification_delivery_sdk import notification_delivery_sdk
        from datetime import datetime, timedelta

        # Get notifications that are ready for delivery (includes retry logic)
        current_time = datetime.now(timezone.utc)

        # Use the enhanced queue management function
        due_notifications = crud.get_notifications_ready_for_delivery(db=db, limit=50)

        processed_count = 0
        success_count = 0

        for notification in due_notifications:
            try:
                # Get user and FCM token
                user = crud.get_user_by_id(db, notification.user_id)
                if not user or not user.fcm_token:
                    logger.warning(f"User not found or no FCM token for notification {notification.id}")
                    # Mark as delivered to prevent retry
                    notification.is_delivered = True
                    notification.delivered_at = current_time
                    continue

                # Check user notification preferences
                preferences = crud.get_or_create_notification_preferences(db, notification.user_id)

                # Check if notification type is enabled
                if not _is_notification_type_enabled(preferences, notification.type):
                    logger.info(f"Notification type {notification.type} disabled for user {notification.user_id}")
                    notification.is_delivered = True
                    notification.delivered_at = current_time
                    continue

                # Check quiet hours
                if _is_in_quiet_hours(preferences, current_time):
                    logger.info(f"In quiet hours for user {notification.user_id}, delaying notification")
                    # Reschedule for after quiet hours
                    notification.scheduled_time = _get_next_available_time(preferences, current_time)
                    continue

                # Send the notification
                data = notification.navigation_data or {}
                if notification.navigation_route:
                    data["navigation_route"] = notification.navigation_route
                data["notification_id"] = notification.id
                data["type"] = notification.type

                result = notification_delivery_sdk.send_push_notification(
                    fcm_token=user.fcm_token,
                    title=notification.title,
                    body=notification.body,
                    data=data
                )

                # Record delivery attempt with enhanced tracking
                success = result.get("success", False)
                failure_reason = result.get("error") if not success else None

                crud.record_delivery_attempt(
                    db=db,
                    notification_id=notification.id,
                    success=success,
                    failure_reason=failure_reason
                )

                if success:
                    success_count += 1
                    logger.info(f"Successfully sent notification {notification.id}")
                else:
                    logger.error(f"Failed to send notification {notification.id}: {failure_reason}")

                processed_count += 1

            except Exception as e:
                logger.error(f"Error processing notification {notification.id}: {e}")
                # Mark as delivered to prevent infinite retry
                notification.is_delivered = True
                notification.delivered_at = current_time
                processed_count += 1

        # Commit all changes
        db.commit()

        logger.info(f"Processed {processed_count} notifications, {success_count} successful")
        return {
            "success": True,
            "processed": processed_count,
            "successful": success_count
        }

    except Exception as e:
        logger.error(f"Error in process_scheduled_notifications: {e}")
        return {"success": False, "error": str(e)}

    finally:
        db.close()

def _is_notification_type_enabled(preferences, notification_type: str) -> bool:
    """Check if a notification type is enabled in user preferences."""
    type_mapping = {
        "therapy_reminder": preferences.therapy_reminders,
        "task_notification": preferences.task_notifications,
        "daily_check_in": preferences.daily_check_ins,
        "weekly_report": preferences.weekly_reports,
        "system_update": preferences.system_updates,
        "contextual_ai": preferences.contextual_ai
    }
    return type_mapping.get(notification_type, True)  # Default to enabled

def _is_in_quiet_hours(preferences, current_time: datetime) -> bool:
    """Check if current time is within user's quiet hours."""
    if not preferences.quiet_hours_enabled:
        return False

    current_hour = current_time.hour
    current_minute = current_time.minute
    current_total_minutes = current_hour * 60 + current_minute

    start_total_minutes = preferences.quiet_hours_start_hour * 60 + preferences.quiet_hours_start_minute
    end_total_minutes = preferences.quiet_hours_end_hour * 60 + preferences.quiet_hours_end_minute

    # Handle overnight quiet hours (e.g., 22:00 to 08:00)
    if start_total_minutes > end_total_minutes:
        return current_total_minutes >= start_total_minutes or current_total_minutes <= end_total_minutes
    else:
        return start_total_minutes <= current_total_minutes <= end_total_minutes

def _get_next_available_time(preferences, current_time: datetime) -> datetime:
    """Get the next available time after quiet hours."""
    if not preferences.quiet_hours_enabled:
        return current_time + timedelta(minutes=5)  # Retry in 5 minutes

    # Calculate next available time (after quiet hours end)
    end_hour = preferences.quiet_hours_end_hour
    end_minute = preferences.quiet_hours_end_minute

    next_time = current_time.replace(hour=end_hour, minute=end_minute, second=0, microsecond=0)

    # If the end time is today but already passed, schedule for tomorrow
    if next_time <= current_time:
        next_time += timedelta(days=1)

    return next_time

@celery_app.task(bind=True, name="cleanup_old_notifications")
def cleanup_old_notifications(self):
    """
    Clean up old delivered notifications to prevent database bloat.
    Runs daily to remove notifications older than 30 days.
    """
    db = get_db_session()

    try:
        from app.db import crud
        from datetime import datetime, timedelta

        # Delete notifications older than 30 days
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)

        deleted_count = db.query(crud.Notification).filter(
            crud.Notification.delivered_at < cutoff_date,
            crud.Notification.is_delivered == True
        ).delete()

        db.commit()

        logger.info(f"Cleaned up {deleted_count} old notifications")
        return {"success": True, "deleted_count": deleted_count}

    except Exception as e:
        logger.error(f"Error cleaning up notifications: {e}")
        return {"success": False, "error": str(e)}

    finally:
        db.close()

if __name__ == "__main__":
    # Start the Celery worker
    celery_app.start()
