# Image-to-Event Extraction Tool PRP

## Tool Overview
**Tool Name**: image_to_event  
**Purpose**: Extract event details (name, date, location) from images containing event information  
**Model Used**: Groq Vision (llama-4-scout-17b-16e-instruct)  
**Output Format**: Structured JSON with event details

## Trigger Conditions

### Primary Triggers
The image-to-event tool should be activated when the user:
- Uploads an image and asks about event details
- Mentions extracting event information from a photo
- Asks to "read" or "analyze" an image for event data
- Requests calendar event creation from an image
- Uses phrases like:
  - "What event is this?"
  - "Extract event details from this image"
  - "Create a calendar event from this photo"
  - "When and where is this event?"
  - "Read the event information"

### Context Patterns
- User provides an image file (JPEG, PNG, etc.)
- Image likely contains text with event information
- User wants structured event data for calendar integration

## Tool Execution Rules

### Input Requirements
1. **Image Data**: Base64 encoded image or image file
2. **User Intent**: Clear request for event extraction
3. **Image Quality**: Image should be readable with visible text

### Processing Steps
1. **Image Analysis**: Use Groq Vision to transcribe all visible text
2. **Information Extraction**: Identify event name, date, and location
3. **Validation**: Ensure all three required fields are found
4. **Format Output**: Return structured JSON response

### System Prompt Template
```
Your task is to analyze this image. First, transcribe all text. Second, from that transcribed text, you MUST extract values for the following JSON fields: 'event_name', 'event_date', and 'event_location'.

If, after analyzing the text, you cannot find a plausible value for all three of those specific fields, you MUST return a JSON object with {"error": "I could not find all the necessary event details in that image. Please ensure the name, date, and location are clearly visible."}.

If successful, the output must be a JSON object with the extracted fields: {"event_name": "...", "event_date": "...", "event_location": "..."}.

Be precise and only extract information that is clearly visible in the image. For dates, use a standard format like "YYYY-MM-DD" or "Month DD, YYYY".
```

## Output Specifications

### Success Response Format
```json
{
  "event_name": "Conference Name or Event Title",
  "event_date": "2025-03-15 or March 15, 2025",
  "event_location": "Venue Name, City, State/Country"
}
```

### Error Response Format
```json
{
  "error": "I could not find all the necessary event details in that image. Please ensure the name, date, and location are clearly visible."
}
```

### Field Requirements
- **event_name**: Clear, descriptive event title
- **event_date**: Standardized date format (ISO 8601 preferred)
- **event_location**: Complete venue/location information

## Error Handling

### Common Error Scenarios
1. **Incomplete Information**: Missing one or more required fields
2. **Unclear Text**: Image quality too poor to read
3. **No Event Information**: Image doesn't contain event details
4. **Ambiguous Dates**: Multiple dates present, unclear which is the event date

### Error Response Guidelines
- Always return the standard error JSON format
- Provide helpful guidance for the user
- Suggest image quality improvements if needed
- Maintain consistent error messaging

## Integration with Calendar Tools

### Workflow Integration
1. **Image Analysis**: Extract event details using this tool
2. **Validation**: Confirm extracted information with user
3. **Calendar Creation**: Use extracted data with create_calendar_event tool
4. **Confirmation**: Provide success feedback to user

### Data Flow
```
User Image → Image-to-Event Tool → Extracted JSON → Calendar Tool → Created Event
```

## User Experience Guidelines

### Success Flow
1. User uploads image with event information
2. Tool extracts event details accurately
3. Present extracted information to user for confirmation
4. Offer to create calendar event with extracted data
5. Provide clear success confirmation

### Error Flow
1. Tool cannot extract complete information
2. Explain what information was missing
3. Suggest improvements (better image quality, different angle)
4. Offer alternative methods (manual entry, different image)

### Response Templates

#### Successful Extraction
"I've successfully extracted the event details from your image:
- **Event**: {event_name}
- **Date**: {event_date}
- **Location**: {event_location}

Would you like me to create a calendar event with these details?"

#### Partial Extraction
"I was able to read some information from the image, but I couldn't find all the required details. I found: [list found items]. Could you provide a clearer image or help me with the missing information?"

#### Complete Failure
"I wasn't able to extract clear event information from this image. The text might be too small, blurry, or the image might not contain event details. Could you try a different image or provide the event details manually?"

## Quality Assurance

### Validation Checks
- All three required fields must be present
- Date format should be standardized
- Location should be specific and complete
- Event name should be descriptive

### Accuracy Measures
- Cross-reference extracted information for consistency
- Validate date formats and logical dates
- Ensure location information is complete
- Verify event name is meaningful

## Performance Considerations

### Optimization Guidelines
- Use temperature 0.0 for consistent extraction
- Limit max_tokens to 500 for focused responses
- Implement proper error handling for API limits
- Cache successful extractions when appropriate

### Rate Limiting
- Respect Groq API rate limits
- Implement exponential backoff for retries
- Provide user feedback during processing
- Handle timeout scenarios gracefully

## Evaluation Criteria

### Success Metrics
- **Accuracy**: Correctly extracted all three fields
- **Completeness**: No missing required information
- **Format Compliance**: Proper JSON structure
- **User Satisfaction**: Clear, actionable results

### Quality Indicators
- Event name is descriptive and complete
- Date is in standard format and logical
- Location includes sufficient detail for calendar entry
- No hallucinated or incorrect information

### Testing Scenarios
1. **Clear Event Flyer**: High-quality image with all details visible
2. **Partial Information**: Image missing one required field
3. **Poor Quality**: Blurry or low-resolution image
4. **Multiple Events**: Image containing multiple event listings
5. **Non-Event Image**: Image without event information
