#!/usr/bin/env python3
"""
Simple therapy migration script that connects directly to the database.
"""

import psycopg2
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Direct database connection (your actual database)
DATABASE_CONFIG = {
    "host": "*************",
    "database": "darvis-postgres",
    "user": "postgres", 
    "password": "qroinoruwob23u410841rqouUBUBUBo808",
    "port": "5432"
}

def create_therapy_tables():
    """Create therapy tables directly."""
    conn = psycopg2.connect(**DATABASE_CONFIG)
    cur = conn.cursor()
    
    try:
        logger.info("🚀 Creating therapy tables...")
        
        # Create pgvector extension
        cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
        
        # Create therapy_knowledge_chunks table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS therapy_knowledge_chunks (
                id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
                content TEXT NOT NULL,
                embedding vector(1024),
                chapter VARCHAR,
                section VARCHAR,
                therapy_technique VARCHAR,
                emotional_themes JSON,
                keywords JSON,
                chunk_index INTEGER NOT NULL,
                total_chunks INTEGER,
                source_document VARCHAR,
                retrieval_count INTEGER DEFAULT 0,
                effectiveness_score FLOAT DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP
            );
        """)
        
        # Create therapy_sessions table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS therapy_sessions (
                id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
                conversation_id VARCHAR NOT NULL,
                user_id VARCHAR NOT NULL,
                session_mode VARCHAR DEFAULT 'chat',
                current_modality VARCHAR DEFAULT 'chat',
                session_focus VARCHAR,
                session_goals JSON,
                initial_emotional_state JSON,
                current_emotional_state JSON,
                emotional_progression JSON,
                retrieved_techniques JSON,
                practice_tasks_assigned JSON,
                key_insights JSON,
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity_at TIMESTAMP,
                ended_at TIMESTAMP,
                session_duration_minutes INTEGER,
                voice_duration_minutes INTEGER DEFAULT 0,
                chat_message_count INTEGER DEFAULT 0,
                modality_switches INTEGER DEFAULT 0
            );
        """)
        
        # Create therapy_progress table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS therapy_progress (
                id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
                user_id VARCHAR NOT NULL,
                session_id VARCHAR,
                progress_type VARCHAR NOT NULL,
                content TEXT NOT NULL,
                mood_score INTEGER,
                anxiety_level INTEGER,
                emotional_tags JSON,
                task_name VARCHAR,
                task_completed BOOLEAN DEFAULT FALSE,
                task_effectiveness_rating INTEGER,
                task_notes TEXT,
                intervention_used VARCHAR,
                intervention_effectiveness FLOAT,
                user_feedback_rating INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                context_tags JSON
            );
        """)
        
        # Create indexes
        logger.info("Creating indexes...")
        
        # Vector similarity index
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_therapy_chunks_embedding_cosine 
            ON therapy_knowledge_chunks USING ivfflat (embedding vector_cosine_ops) 
            WITH (lists = 100);
        """)
        
        # Other indexes
        cur.execute("CREATE INDEX IF NOT EXISTS idx_therapy_technique ON therapy_knowledge_chunks(therapy_technique);")
        cur.execute("CREATE INDEX IF NOT EXISTS idx_therapy_session_conversation ON therapy_sessions(conversation_id);")
        cur.execute("CREATE INDEX IF NOT EXISTS idx_therapy_session_user ON therapy_sessions(user_id);")
        cur.execute("CREATE INDEX IF NOT EXISTS idx_therapy_progress_user_type ON therapy_progress(user_id, progress_type);")
        
        conn.commit()
        logger.info("✅ Therapy tables and indexes created successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating therapy tables: {e}")
        conn.rollback()
        return False
        
    finally:
        cur.close()
        conn.close()

def verify_tables():
    """Verify tables were created."""
    conn = psycopg2.connect(**DATABASE_CONFIG)
    cur = conn.cursor()
    
    try:
        tables = ['therapy_knowledge_chunks', 'therapy_sessions', 'therapy_progress']
        
        for table in tables:
            cur.execute(f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table}');")
            exists = cur.fetchone()[0]
            
            if exists:
                logger.info(f"✅ Table {table} exists")
            else:
                logger.error(f"❌ Table {table} missing")
                return False
        
        return True
        
    finally:
        cur.close()
        conn.close()

if __name__ == "__main__":
    logger.info("🚀 Starting simple therapy migration...")
    
    if create_therapy_tables():
        if verify_tables():
            logger.info("🎉 Migration completed successfully!")
        else:
            logger.error("💥 Verification failed!")
    else:
        logger.error("💥 Migration failed!")