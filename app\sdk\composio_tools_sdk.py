import os
from typing import Dict, Any, Optional
import logging
from datetime import datetime, timezone
import json

# Configure logging
logger = logging.getLogger(__name__)

class ComposioToolsSDK:
    """
    SDK for interacting with external tools via Composio.

    This service provides a unified interface for executing actions
    on external services like Google Calendar, Gmail, etc.
    """

    def __init__(self):
        """Initialize the Composio tools SDK."""
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """Initialize the Composio client."""
        try:
            from composio import Composio

            api_key = os.getenv("COMPOSIO_API_KEY")
            if not api_key:
                logger.warning("COMPOSIO_API_KEY not found. Tool execution will be mocked.")
                self.client = None
                return

            self.client = Composio(api_key=api_key)
            logger.info("Composio client initialized successfully")

        except ImportError:
            logger.warning("composio package not installed. Tool execution will be mocked.")
            self.client = None
        except Exception as e:
            logger.error(f"Failed to initialize Composio client: {e}")
            self.client = None

    def create_calendar_event(
        self,
        user_id: str,
        summary: str,
        start_time: str,
        end_time: str,
        description: Optional[str] = None,
        location: Optional[str] = None,
        connection_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a Google Calendar event using Composio.

        Args:
            user_id: User identifier for the calendar
            summary: Event title/summary
            start_time: Event start time in ISO 8601 format
            end_time: Event end time in ISO 8601 format
            description: Optional event description
            location: Optional event location
            connection_id: Optional specific connection ID to use

        Returns:
            Dictionary with success status and event details or error information
        """
        if not self.client:
            # Return mock response for development
            return self._mock_calendar_event_response(summary, start_time, end_time)

        try:
            # Prepare event data for Google Calendar
            event_data = {
                "summary": summary,
                "start": {
                    "dateTime": start_time,
                    "timeZone": "UTC"  # Default to UTC, could be made configurable
                },
                "end": {
                    "dateTime": end_time,
                    "timeZone": "UTC"
                }
            }

            # Add optional fields if provided
            if description:
                event_data["description"] = description
            if location:
                event_data["location"] = location

            # Execute the Google Calendar create event action
            result = self.client.tools.execute(
                "GOOGLECALENDAR_CREATE_EVENT",
                user_id=user_id,
                arguments=event_data
            )

            # Process the result
            if result.successful:
                logger.info(f"Successfully created calendar event: {summary}")
                return {
                    "success": True,
                    "event_id": result.data.get("id"),
                    "event_link": result.data.get("htmlLink"),
                    "summary": summary,
                    "start_time": start_time,
                    "end_time": end_time,
                    "description": description,
                    "location": location,
                    "message": f"Event '{summary}' created successfully"
                }
            else:
                logger.error(f"Failed to create calendar event: {result.error}")
                return {
                    "success": False,
                    "error": result.error or "Unknown error occurred",
                    "message": f"Failed to create event '{summary}'"
                }

        except Exception as e:
            logger.error(f"Error creating calendar event: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Error creating event '{summary}': {str(e)}"
            }

    def _mock_calendar_event_response(
        self,
        summary: str,
        start_time: str,
        end_time: str
    ) -> Dict[str, Any]:
        """
        Generate a mock response for calendar event creation.

        Args:
            summary: Event title
            start_time: Event start time
            end_time: Event end time

        Returns:
            Mock success response
        """
        mock_event_id = f"mock_event_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        logger.info(f"Mock: Created calendar event '{summary}' from {start_time} to {end_time}")

        return {
            "success": True,
            "event_id": mock_event_id,
            "event_link": f"https://calendar.google.com/event?eid={mock_event_id}",
            "summary": summary,
            "start_time": start_time,
            "end_time": end_time,
            "message": f"Mock: Event '{summary}' created successfully",
            "mock": True
        }

    def list_available_tools(self, user_id: str = "default") -> Dict[str, Any]:
        """
        List available tools for the user.

        Args:
            user_id: User identifier

        Returns:
            Dictionary with available tools information
        """
        if not self.client:
            return {
                "success": False,
                "tools": [],
                "message": "Composio client not available. Please configure COMPOSIO_API_KEY."
            }

        try:
            # Get available tools from Composio
            tools = self.client.tools.get(user_id, {"toolkits": ["googlecalendar", "gmail"]})

            return {
                "success": True,
                "tools": [{"name": tool.name, "description": tool.description} for tool in tools],
                "count": len(tools),
                "message": f"Found {len(tools)} available tools"
            }

        except Exception as e:
            logger.error(f"Error listing tools: {e}")
            return {
                "success": False,
                "tools": [],
                "error": str(e),
                "message": f"Error listing tools: {str(e)}"
            }

# Global Composio tools instance
composio_tools = ComposioToolsSDK()