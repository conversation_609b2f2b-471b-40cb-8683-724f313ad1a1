import os
import re
from typing import Dict, Optional, List
from pathlib import Path
import logging

# Configure logging
logger = logging.getLogger(__name__)

class PRPManager:
    """
    Prompt Rule Proposal (PRP) Manager for loading and parsing PRP files.

    PRPs are structured markdown files that define AI behavior rules, triggers,
    and evaluation criteria for different conversation contexts.
    """

    def __init__(self, prompts_dir: str = "darvis-ai-core/prompts"):
        """
        Initialize the PRP Manager.

        Args:
            prompts_dir: Base directory containing PRP files
        """
        self.prompts_dir = Path(prompts_dir)
        self._cache: Dict[str, Dict] = {}

    def load_prp(self, prp_path: str) -> Optional[Dict]:
        """
        Load and parse a PRP file from the prompts directory.

        Args:
            prp_path: Relative path to the PRP file (e.g., "_system/_initial_persona.md")

        Returns:
            Dictionary containing parsed PRP sections or None if file not found
        """
        # Check cache first
        if prp_path in self._cache:
            return self._cache[prp_path]

        full_path = self.prompts_dir / prp_path

        if not full_path.exists():
            logger.error(f"PRP file not found: {full_path}")
            return None

        try:
            with open(full_path, 'r', encoding='utf-8') as file:
                content = file.read()

            parsed_prp = self._parse_prp_content(content)

            # Cache the result
            self._cache[prp_path] = parsed_prp

            logger.info(f"Successfully loaded PRP: {prp_path}")
            return parsed_prp

        except Exception as e:
            logger.error(f"Error loading PRP file {prp_path}: {e}")
            return None

    def _parse_prp_content(self, content: str) -> Dict:
        """
        Parse PRP markdown content into structured sections.

        Args:
            content: Raw markdown content of the PRP file

        Returns:
            Dictionary with parsed sections (title, trigger, rules, evaluation)
        """
        prp_data = {
            "title": "",
            "trigger": "",
            "rules": [],
            "evaluation": ""
        }

        # Extract title from first line (# PRP-XXX: Title)
        title_match = re.match(r'^#\s*(.+)$', content.split('\n')[0])
        if title_match:
            prp_data["title"] = title_match.group(1).strip()

        # Split content into sections
        sections = re.split(r'^##\s+(.+)$', content, flags=re.MULTILINE)

        current_section = None
        for i, section in enumerate(sections):
            if i == 0:
                continue  # Skip content before first section

            if i % 2 == 1:  # Odd indices are section headers
                current_section = section.strip().lower()
            else:  # Even indices are section content
                section_content = section.strip()

                if current_section == "trigger":
                    prp_data["trigger"] = section_content
                elif current_section == "rules":
                    # Parse numbered rules
                    rules = re.findall(r'^\d+\.\s*(.+)$', section_content, re.MULTILINE)
                    prp_data["rules"] = rules
                elif current_section == "evaluation":
                    prp_data["evaluation"] = section_content

        return prp_data

    def get_system_prp(self, prp_name: str = "_initial_persona.md") -> Optional[Dict]:
        """
        Load a system PRP from the _system directory.

        Args:
            prp_name: Name of the PRP file in _system directory

        Returns:
            Parsed PRP dictionary or None if not found
        """
        return self.load_prp(f"_system/{prp_name}")

    def get_mode_prp(self, mode_name: str) -> Optional[Dict]:
        """
        Load a mode-specific PRP from the modes directory.

        Args:
            mode_name: Name of the mode PRP file

        Returns:
            Parsed PRP dictionary or None if not found
        """
        return self.load_prp(f"modes/{mode_name}")

    def get_tool_prp(self, tool_name: str) -> Optional[Dict]:
        """
        Load a tool-specific PRP from the tools directory.

        Args:
            tool_name: Name of the tool PRP file

        Returns:
            Parsed PRP dictionary or None if not found
        """
        return self.load_prp(f"tools/{tool_name}")

    def clear_cache(self):
        """Clear the PRP cache."""
        self._cache.clear()
        logger.info("PRP cache cleared")

# Global PRP manager instance
prp_manager = PRPManager()