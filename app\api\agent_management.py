"""
Agent Management API for LiveKit Voice Sessions.

This module provides endpoints for managing voice AI agent sessions,
including room creation, token generation, and agent lifecycle management.
"""

import os
import json
import uuid
import asyncio
import subprocess
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

# LiveKit API for room and token management - FIXED IMPORTS
from livekit import api as lkapi

# Darvis components
from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
import logging

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/agent", tags=["Agent Management"])

# Pydantic models for request/response
class VoiceSessionRequest(BaseModel):
    mode: str = Field(
        default="general",
        description="Conversation mode: 'general', 'therapy', 'productivity'"
    )
    conversation_id: Optional[str] = Field(
        None,
        description="Optional conversation ID for context continuity"
    )

class VoiceSessionResponse(BaseModel):
    room_name: str = Field(description="LiveKit room name")
    user_token: str = Field(description="User access token for the room")
    room_url: str = Field(description="LiveKit server URL")
    session_id: str = Field(description="Unique session identifier")
    expires_at: datetime = Field(description="Token expiration time")

class LiveKitManager:
    """
    Manager for LiveKit room and token operations.
    
    Handles room creation, token generation, and agent deployment
    using the LiveKit Cloud API.
    """
    
    def __init__(self):
        """Initialize LiveKit manager with API credentials."""
        self.api_key = os.getenv("LIVEKIT_API_KEY")
        self.api_secret = os.getenv("LIVEKIT_API_SECRET")
        self.livekit_url = os.getenv("LIVEKIT_URL", "wss://darvis.livekit.cloud")
        
        if not all([self.api_key, self.api_secret]):
            raise ValueError("LIVEKIT_API_KEY and LIVEKIT_API_SECRET environment variables are required")
        
        # Note: No longer storing room_service as instance variable
        # We'll create LiveKitAPI clients as needed in methods
        
    async def create_room(self, room_name: str, metadata: Dict[str, Any]) -> str:
        """
        Create a new LiveKit room.
        
        Args:
            room_name: Unique room identifier
            metadata: Room metadata including user context
            
        Returns:
            Created room name
        """
        try:
            # Create LiveKitAPI client for this operation - FIXED USAGE
            async with lkapi.LiveKitAPI(
                url=self.livekit_url, 
                api_key=self.api_key, 
                api_secret=self.api_secret
            ) as client:
                room_options = lkapi.CreateRoomRequest(
                    name=room_name,
                    metadata=json.dumps(metadata),
                    empty_timeout=300,  # 5 minutes timeout for empty room
                    max_participants=2,  # User + Agent
                )
                
                room = await client.room.create_room(room_options)
                logger.info(f"Created LiveKit room: {room.name}")
                return room.name
            
        except Exception as e:
            logger.error(f"Error creating LiveKit room: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create voice session room: {str(e)}"
            )
    
    def generate_user_token(self, room_name: str, user_id: str, expires_in_hours: int = 2) -> str:
        """
        Generate access token for user to join the room.
        
        Args:
            room_name: Room to join
            user_id: User identifier
            expires_in_hours: Token validity period
            
        Returns:
            JWT access token
        """
        try:
            # Token grants for user participant - FIXED ACCESS
            token = lkapi.AccessToken(self.api_key, self.api_secret)
            token.with_identity(user_id)
            token.with_name(f"User-{user_id}")
            token.with_grants(lkapi.VideoGrants(
                room_join=True,
                room=room_name,
                can_publish=True,
                can_subscribe=True,
                can_publish_data=True,
            ))
            
            # Set expiration (your original code was correct)
            expires_at = datetime.utcnow() + timedelta(hours=expires_in_hours)
            token.with_ttl(expires_at)
            
            return token.to_jwt()
            
        except Exception as e:
            logger.error(f"Error generating user token: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate access token"
            )
    
    def generate_agent_token(self, room_name: str, agent_id: str) -> str:
        """
        Generate access token for agent to join the room.
        
        Args:
            room_name: Room to join
            agent_id: Agent identifier
            
        Returns:
            JWT access token for agent
        """
        try:
            # FIXED ACCESS
            token = lkapi.AccessToken(self.api_key, self.api_secret)
            token.with_identity(agent_id)
            token.with_name(f"DarvisAgent-{agent_id}")
            token.with_grants(lkapi.VideoGrants(
                room_join=True,
                room=room_name,
                can_publish=True,
                can_subscribe=True,
                can_publish_data=True,
                # Agent-specific permissions
                room_admin=True,  # Allow agent to manage room
            ))
            
            # Longer expiration for agent (your original code was correct)
            expires_at = datetime.utcnow() + timedelta(hours=6)
            token.with_ttl(expires_at)
            
            return token.to_jwt()
            
        except Exception as e:
            logger.error(f"Error generating agent token: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate agent token"
            )

class AgentProcessManager:
    """
    Manager for LiveKit agent process lifecycle.
    
    Handles launching, monitoring, and terminating agent processes
    for voice sessions.
    """
    
    def __init__(self):
        """Initialize agent process manager."""
        self.active_processes: Dict[str, subprocess.Popen] = {}
    
    async def launch_agent(
        self,
        room_name: str,
        agent_token: str,
        user_id: str,
        mode: str,
        conversation_id: Optional[str] = None
    ) -> str:
        """
        Launch a Darvis LiveKit agent process.
        
        Args:
            room_name: LiveKit room name
            agent_token: Agent access token
            user_id: User identifier
            mode: Conversation mode
            conversation_id: Optional conversation ID
            
        Returns:
            Process identifier
        """
        try:
            # Generate unique process ID
            process_id = f"agent-{uuid.uuid4().hex[:8]}"
            
            # Prepare environment variables for agent
            env = os.environ.copy()
            env.update({
                "LIVEKIT_URL": os.getenv("LIVEKIT_URL", "wss://darvis.livekit.cloud"),
                "LIVEKIT_API_KEY": os.getenv("LIVEKIT_API_KEY"),
                "LIVEKIT_API_SECRET": os.getenv("LIVEKIT_API_SECRET"),
                "AGENT_TOKEN": agent_token,
                "ROOM_NAME": room_name,
                "USER_ID": user_id,
                "AGENT_MODE": mode,
                "CONVERSATION_ID": conversation_id or "",
                "DEEPINFRA_API_KEY": os.getenv("DEEPINFRA_API_KEY"),
                "GROQ_API_KEY": os.getenv("GROQ_API_KEY"),
            })
            
            # Launch agent process
            cmd = [
                "python",
                "darvis-ai-core/livekit_agent.py",
                "start",
                "--room", room_name,
                "--token", agent_token
            ]
            
            process = subprocess.Popen(
                cmd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            # Store process reference
            self.active_processes[process_id] = process
            
            logger.info(f"Launched agent process {process_id} for room {room_name}")
            return process_id
            
        except Exception as e:
            logger.error(f"Error launching agent process: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to launch voice agent"
            )
    
    def terminate_agent(self, process_id: str) -> bool:
        """
        Terminate an agent process.
        
        Args:
            process_id: Process identifier
            
        Returns:
            True if terminated successfully
        """
        try:
            if process_id in self.active_processes:
                process = self.active_processes[process_id]
                process.terminate()
                
                # Wait for graceful termination
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()  # Force kill if not terminated
                
                del self.active_processes[process_id]
                logger.info(f"Terminated agent process {process_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error terminating agent process {process_id}: {e}")
            return False

# LAZY INITIALIZATION - This prevents startup crashes
livekit_manager = None  # Don't create immediately
agent_manager = AgentProcessManager()  # This one is safe to create

def get_livekit_manager() -> LiveKitManager:
    """
    Get LiveKit manager instance, creating it if needed.
    This prevents startup crashes if environment variables are missing.
    """
    global livekit_manager
    if livekit_manager is None:
        livekit_manager = LiveKitManager()
    return livekit_manager

@router.post("/start_voice_session", response_model=VoiceSessionResponse)
async def start_voice_session(
    request: VoiceSessionRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Start a new voice AI session with LiveKit.
    
    This endpoint:
    1. Creates a new LiveKit room
    2. Generates secure access tokens for user and agent
    3. Launches the Darvis LiveKit agent
    4. Returns connection details for the Flutter app
    
    Args:
        request: Voice session configuration
        background_tasks: FastAPI background tasks
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Voice session connection details
    """
    try:
        user_id = current_user["uid"]
        session_id = str(uuid.uuid4())
        
        # Generate unique room name
        room_name = f"darvis-voice-{user_id}-{session_id[:8]}"
        
        # Prepare room metadata
        room_metadata = {
            "user_id": user_id,
            "mode": request.mode,
            "conversation_id": request.conversation_id,
            "session_id": session_id,
            "created_at": datetime.utcnow().isoformat()
        }
        
        # Get LiveKit manager (lazy initialization prevents startup crashes)
        manager = get_livekit_manager()
        
        # Create LiveKit room
        await manager.create_room(room_name, room_metadata)
        
        # Generate access tokens
        user_token = manager.generate_user_token(room_name, user_id)
        agent_token = manager.generate_agent_token(room_name, f"agent-{session_id}")
        
        # Launch agent in background
        background_tasks.add_task(
            launch_agent_background,
            room_name,
            agent_token,
            user_id,
            request.mode,
            request.conversation_id
        )
        
        # Calculate token expiration
        expires_at = datetime.utcnow() + timedelta(hours=2)
        
        logger.info(f"Started voice session {session_id} for user {user_id} in mode {request.mode}")
        
        return VoiceSessionResponse(
            room_name=room_name,
            user_token=user_token,
            room_url=manager.livekit_url,
            session_id=session_id,
            expires_at=expires_at
        )
        
    except Exception as e:
        logger.error(f"Error starting voice session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start voice session"
        )

async def launch_agent_background(
    room_name: str,
    agent_token: str,
    user_id: str,
    mode: str,
    conversation_id: Optional[str]
):
    """
    Background task to launch the LiveKit agent.
    
    Args:
        room_name: LiveKit room name
        agent_token: Agent access token
        user_id: User identifier
        mode: Conversation mode
        conversation_id: Optional conversation ID
    """
    try:
        # Small delay to ensure room is ready
        await asyncio.sleep(2)
        
        # Launch agent process
        process_id = await agent_manager.launch_agent(
            room_name=room_name,
            agent_token=agent_token,
            user_id=user_id,
            mode=mode,
            conversation_id=conversation_id
        )
        
        logger.info(f"Agent {process_id} launched for room {room_name}")
        
    except Exception as e:
        logger.error(f"Error in background agent launch: {e}")

@router.delete("/session/{session_id}")
async def terminate_voice_session(
    session_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Terminate a voice session and cleanup resources.
    
    Args:
        session_id: Session identifier
        current_user: Authenticated user
        
    Returns:
        Success confirmation
    """
    try:
        # Find and terminate agent process
        # Note: In production, you'd want to track session->process mapping
        # For now, we'll implement basic cleanup
        
        logger.info(f"Terminating voice session {session_id} for user {current_user['uid']}")
        
        return {"message": "Voice session terminated successfully"}
        
    except Exception as e:
        logger.error(f"Error terminating voice session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to terminate voice session"
        )