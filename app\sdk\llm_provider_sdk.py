import os
from typing import List, Dict, Any, Optional
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.language_models import BaseChatModel
import logging

# Configure logging
logger = logging.getLogger(__name__)

class LLMProviderSDK:
    """
    SDK Adapter for LLM providers following the adapter pattern.
    Currently supports Groq and can be extended for other providers.
    """

    def __init__(self, provider: str = "groq", model: str = "llama3-8b-8192"):
        """
        Initialize the LLM provider SDK.

        Args:
            provider: LLM provider name ("groq", "gemini", etc.)
            model: Model name to use
        """
        self.provider = provider.lower()
        self.model = model
        self.client: Optional[BaseChatModel] = None
        self._initialize_client()

    def _initialize_client(self):
        """Initialize the appropriate LLM client based on provider."""
        try:
            if self.provider == "groq":
                self._initialize_groq()
            elif self.provider == "gemini":
                self._initialize_gemini()
            else:
                raise ValueError(f"Unsupported provider: {self.provider}")

            logger.info(f"LLM provider {self.provider} initialized successfully with model {self.model}")

        except Exception as e:
            logger.error(f"Failed to initialize LLM provider {self.provider}: {e}")
            # For development, we'll create a mock client
            self.client = None

    def _initialize_groq(self):
        """Initialize Groq client."""
        try:
            from langchain_groq import ChatGroq

            api_key = os.getenv("GROQ_API_KEY")
            if not api_key:
                raise ValueError("GROQ_API_KEY environment variable not set")

            self.client = ChatGroq(
                groq_api_key=api_key,
                model_name=self.model,
                temperature=0.7,
                max_tokens=1024
            )
        except ImportError:
            logger.error("langchain_groq not installed. Install with: pip install langchain-groq")
            raise

    def _initialize_gemini(self):
        """Initialize Gemini client."""
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI

            api_key = os.getenv("GOOGLE_API_KEY")
            if not api_key:
                raise ValueError("GOOGLE_API_KEY environment variable not set")

            self.client = ChatGoogleGenerativeAI(
                google_api_key=api_key,
                model=self.model or "gemini-pro",
                temperature=0.7,
                max_output_tokens=1024
            )
        except ImportError:
            logger.error("langchain_google_genai not installed. Install with: pip install langchain-google-genai")
            raise

    def invoke(self, messages: List[BaseMessage], model_name: str = None) -> AIMessage:
        """
        Invoke the LLM with a list of messages.

        Args:
            messages: List of LangChain messages
            model_name: Optional model name to override the default

        Returns:
            AI response message
        """
        if not self.client:
            # Return mock response for development
            return AIMessage(content="I'm a mock AI response. Please configure your LLM provider.")

        # Use provided model name or fall back to instance default
        model_to_use = model_name or self.model

        try:
            # For LangChain clients, we need to create a new client instance with the specific model
            if self.provider == "groq":
                response = self._invoke_groq(messages, model_to_use)
            elif self.provider == "gemini":
                response = self._invoke_gemini(messages, model_to_use)
            else:
                response = self.client.invoke(messages)

            logger.debug(f"LLM response received: {len(response.content)} characters using model {model_to_use}")
            return response

        except Exception as e:
            logger.error(f"Error invoking LLM with model {model_to_use}: {e}")
            return AIMessage(content="I apologize, but I'm experiencing technical difficulties. Please try again later.")

    def _invoke_groq(self, messages: List[BaseMessage], model_name: str) -> AIMessage:
        """
        Invoke Groq LLM with specific model.

        Args:
            messages: List of LangChain messages
            model_name: Groq model name to use

        Returns:
            AI response message
        """
        try:
            from langchain_groq import ChatGroq

            # Create a new client instance with the specific model
            groq_client = ChatGroq(
                groq_api_key=self.client.groq_api_key,
                model_name=model_name,
                temperature=0.7,
                max_tokens=1024
            )

            return groq_client.invoke(messages)

        except Exception as e:
            logger.error(f"Error invoking Groq with model {model_name}: {e}")
            raise

    def _invoke_gemini(self, messages: List[BaseMessage], model_name: str) -> AIMessage:
        """
        Invoke Gemini LLM with specific model.

        Args:
            messages: List of LangChain messages
            model_name: Gemini model name to use

        Returns:
            AI response message
        """
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI

            # Create a new client instance with the specific model
            gemini_client = ChatGoogleGenerativeAI(
                google_api_key=self.client.google_api_key,
                model=model_name,
                temperature=0.7,
                max_output_tokens=1024
            )

            return gemini_client.invoke(messages)

        except Exception as e:
            logger.error(f"Error invoking Gemini with model {model_name}: {e}")
            raise

    def create_messages(self, system_prompt: str, user_message: str, conversation_history: List[Dict] = None) -> List[BaseMessage]:
        """
        Create a list of LangChain messages from components.

        Args:
            system_prompt: System instruction
            user_message: User's message
            conversation_history: Optional conversation history

        Returns:
            List of formatted messages
        """
        messages = []

        # Add system message
        if system_prompt:
            messages.append(SystemMessage(content=system_prompt))

        # Add conversation history
        if conversation_history:
            for msg in conversation_history:
                if msg.get("role") == "user":
                    messages.append(HumanMessage(content=msg.get("content", "")))
                elif msg.get("role") == "assistant":
                    messages.append(AIMessage(content=msg.get("content", "")))

        # Add current user message
        messages.append(HumanMessage(content=user_message))

        return messages

    def stream(self, messages: List[BaseMessage], model_name: str = None):
        """
        Stream LLM response with a list of messages.

        Args:
            messages: List of LangChain messages
            model_name: Optional model name to override the default

        Yields:
            Streaming response chunks
        """
        if not self.client:
            # Return mock streaming response for development
            yield {"choices": [{"delta": {"content": "Mock streaming response. Please configure your LLM provider."}}]}
            return

        # Use provided model name or fall back to instance default
        model_to_use = model_name or self.model

        try:
            if self.provider == "groq":
                yield from self._stream_groq(messages, model_to_use)
            elif self.provider == "gemini":
                yield from self._stream_gemini(messages, model_to_use)
            else:
                # Fallback for other providers
                response = self.client.invoke(messages)
                yield {"choices": [{"delta": {"content": response.content}}]}

        except Exception as e:
            logger.error(f"Error streaming LLM with model {model_to_use}: {e}")
            yield {"choices": [{"delta": {"content": "I apologize, but I'm experiencing technical difficulties. Please try again later."}}]}

    def _stream_groq(self, messages: List[BaseMessage], model_name: str):
        """
        Stream Groq LLM response with specific model.

        Args:
            messages: List of LangChain messages
            model_name: Groq model name to use

        Yields:
            Streaming response chunks
        """
        try:
            from langchain_groq import ChatGroq

            # Create a new client instance with the specific model
            groq_client = ChatGroq(
                groq_api_key=self.client.groq_api_key,
                model_name=model_name,
                temperature=0.7,
                max_tokens=1024,
                streaming=True
            )

            # Stream the response
            for chunk in groq_client.stream(messages):
                if hasattr(chunk, 'content') and chunk.content:
                    yield {"choices": [{"delta": {"content": chunk.content}}]}

        except Exception as e:
            logger.error(f"Error streaming Groq with model {model_name}: {e}")
            raise

    def _stream_gemini(self, messages: List[BaseMessage], model_name: str):
        """
        Stream Gemini LLM response with specific model.

        Args:
            messages: List of LangChain messages
            model_name: Gemini model name to use

        Yields:
            Streaming response chunks
        """
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI

            # Create a new client instance with the specific model
            gemini_client = ChatGoogleGenerativeAI(
                google_api_key=self.client.google_api_key,
                model=model_name,
                temperature=0.7,
                max_output_tokens=1024
            )

            # Stream the response
            for chunk in gemini_client.stream(messages):
                if hasattr(chunk, 'content') and chunk.content:
                    yield {"choices": [{"delta": {"content": chunk.content}}]}

        except Exception as e:
            logger.error(f"Error streaming Gemini with model {model_name}: {e}")
            raise

# Global LLM provider instance
llm_provider = LLMProviderSDK()