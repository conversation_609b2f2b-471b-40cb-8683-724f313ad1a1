import psycopg2
import json

conn = psycopg2.connect(
    host="*************",
    database="darvis-postgres", 
    user="postgres",
    password="qroinoruwob23u410841rqouUBUBUBo808",
    port="5432"
)

cur = conn.cursor()

print("🔍 EMBEDDING VERIFICATION")
print("=" * 40)

# Check total count
cur.execute("SELECT COUNT(*) FROM therapy_knowledge_chunks;")
total_count = cur.fetchone()[0]
print(f"📊 Total chunks: {total_count}")

# Check embeddings exist
cur.execute("SELECT COUNT(*) FROM therapy_knowledge_chunks WHERE embedding IS NOT NULL;")
embedded_count = cur.fetchone()[0]
print(f"🧠 Chunks with embeddings: {embedded_count}")

# Check sample data
cur.execute("""
    SELECT content, chapter, therapy_technique, chunk_index 
    FROM therapy_knowledge_chunks 
    ORDER BY chunk_index 
    LIMIT 3;
""")

print(f"\n📋 Sample chunks:")
for content, chapter, technique, idx in cur.fetchall():
    print(f"  Chunk {idx}: {technique} - {chapter}")
    print(f"    Content: {content[:100]}...")
    print()

# Test vector similarity (basic check)
cur.execute("""
    SELECT content, chunk_index
    FROM therapy_knowledge_chunks 
    WHERE embedding IS NOT NULL
    LIMIT 1;
""")

if cur.fetchone():
    print("✅ Embeddings are properly stored!")
else:
    print("❌ No embeddings found!")

conn.close()