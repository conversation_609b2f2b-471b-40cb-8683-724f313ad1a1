from typing import Annotated, Dict, Any, List, Optional
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
import logging
import sys
import os
import json
import re

# Add the parent directory to the path to import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.sdk.llm_provider_sdk import llm_provider
from app.sdk.embedding_sdk import embedding_service
from app.db.database import get_db, get_redis
from app.db import crud
from datetime import datetime, timedelta
from prp_manager import prp_manager

# Configure logging
logger = logging.getLogger(__name__)

class ConversationState(TypedDict):
    """
    State schema for the conversation graph.

    This defines the structure of data that flows through the LangGraph nodes.
    """
    messages: Annotated[List[BaseMessage], add_messages]
    user_id: str
    conversation_id: str
    conversation_context: Dict[str, Any]
    relevant_memories: List[Dict[str, Any]]
    tool_calls: Optional[List[Dict[str, Any]]]  # Store detected tool calls
    tool_results: Optional[List[Dict[str, Any]]]  # Store tool execution results
    command_executed: Optional[Dict[str, Any]]  # Store executed user command if any
    is_search_enabled: bool  # Whether search functionality is enabled
    current_mode: str  # Current conversation mode (general, learning, therapy, etc.)

class DarvisOrchestrator:
    """
    Main LangGraph orchestrator for the Darvis AI agent.

    This class manages the conversation flow, PRP loading, and LLM interactions
    using LangGraph's state management and node execution framework.
    """

    def __init__(self):
        """Initialize the Darvis orchestrator."""
        self.graph = None
        self._build_graph()

    def _build_graph(self):
        """Build the LangGraph conversation flow with tool support."""
        # Create the state graph
        builder = StateGraph(ConversationState)

        # Add nodes
        builder.add_node("command_check", self._command_check_node)
        builder.add_node("therapy_mode_check", self._therapy_mode_check_node)
        builder.add_node("therapy_context_retrieval", self._therapy_context_retrieval_node)
        builder.add_node("memory_retrieval", self._memory_retrieval_node)
        builder.add_node("agent", self._agent_node)
        builder.add_node("tool_execution", self._tool_execution_node)
        builder.add_node("therapy_session_management", self._therapy_session_management_node)
        builder.add_node("memory_storage", self._memory_storage_node)

        # Define edges - command check is now the first step
        builder.add_edge(START, "command_check")

        # Conditional routing after command check
        builder.add_conditional_edges(
            "command_check",
            self._should_continue_after_command,
            {
                "continue": "therapy_mode_check",
                "end": "memory_storage"
            }
        )

        # Therapy mode routing
        builder.add_conditional_edges(
            "therapy_mode_check",
            self._should_use_therapy_mode,
            {
                "therapy": "therapy_context_retrieval",
                "normal": "memory_retrieval"
            }
        )

        # Therapy context retrieval flows to memory retrieval
        builder.add_edge("therapy_context_retrieval", "memory_retrieval")
        builder.add_edge("memory_retrieval", "agent")

        # Conditional routing after agent
        builder.add_conditional_edges(
            "agent",
            self._should_use_tools_or_therapy,
            {
                "tools": "tool_execution",
                "therapy_session": "therapy_session_management",
                "end": "memory_storage"
            }
        )

        # After tool execution, go back to agent for response formulation
        builder.add_edge("tool_execution", "agent")

        # Therapy session management flows to memory storage
        builder.add_edge("therapy_session_management", "memory_storage")
        builder.add_edge("memory_storage", END)

        # Compile the graph
        self.graph = builder.compile()

        logger.info("Darvis orchestrator graph with command checking and tool support built successfully")

    def _command_check_node(self, state: ConversationState) -> ConversationState:
        """
        Check if the user's message matches any of their custom commands.

        This is the FIRST step in the conversation flow. If a command is matched,
        it executes the action immediately and skips normal conversation processing.

        Args:
            state: Current conversation state

        Returns:
            Updated state with command execution results if applicable
        """
        try:
            user_message = state["messages"][-1].content.strip().lower()
            user_id = state["user_id"]

            logger.debug(f"Checking for user commands for message: '{user_message}'")

            # Get database session
            db = next(get_db())

            # Check if the message matches any user command
            command = crud.get_user_command_by_trigger(db, user_id, user_message)

            if command:
                logger.info(f"User command matched: '{command.trigger_phrase}' -> '{command.action_to_perform}'")

                # Execute the command action
                command_result = self._execute_command_action(command.action_to_perform, state)

                # Store the command execution in state
                state["command_executed"] = {
                    "command_id": command.id,
                    "trigger_phrase": command.trigger_phrase,
                    "action": command.action_to_perform,
                    "result": command_result
                }

                # Add AI response message about the command execution
                ai_response = AIMessage(content=command_result.get("response", "Command executed successfully."))
                state["messages"].append(ai_response)

            else:
                logger.debug("No user command matched, proceeding with normal conversation flow")
                state["command_executed"] = None

            db.close()
            return state

        except Exception as e:
            logger.error(f"Error in command check node: {e}")
            state["command_executed"] = None
            return state

    def _should_continue_after_command(self, state: ConversationState) -> str:
        """
        Conditional routing function after command check.

        Args:
            state: Current conversation state

        Returns:
            "end" if a command was executed, "continue" for normal flow
        """
        command_executed = state.get("command_executed")

        if command_executed:
            logger.info(f"Command executed: {command_executed['action']}, skipping normal conversation flow")
            return "end"
        else:
            logger.debug("No command executed, continuing with normal conversation flow")
            return "continue"

    def _execute_command_action(self, action: str, state: ConversationState) -> Dict[str, Any]:
        """
        Execute a user-defined command action.

        Args:
            action: The action to perform (e.g., "enter_therapy_mode")
            state: Current conversation state

        Returns:
            Dictionary with execution results
        """
        try:
            user_id = state["user_id"]
            conversation_id = state["conversation_id"]

            # Define available command actions
            if action == "enter_therapy_mode":
                # Set conversation context for therapy mode
                state["conversation_context"]["mode"] = "therapy"
                state["conversation_context"]["therapy_session_started"] = datetime.now().isoformat()

                return {
                    "response": "I've switched to therapy mode. I'm here to listen and support you. How are you feeling today?",
                    "mode_changed": "therapy",
                    "success": True
                }

            elif action == "start_productivity_session":
                # Set conversation context for productivity mode
                state["conversation_context"]["mode"] = "productivity"
                state["conversation_context"]["productivity_session_started"] = datetime.now().isoformat()

                return {
                    "response": "Productivity mode activated! Let's focus on getting things done. What would you like to work on today?",
                    "mode_changed": "productivity",
                    "success": True
                }

            elif action == "enter_general_mode":
                # Set conversation context for general mode
                state["conversation_context"]["mode"] = "general"

                return {
                    "response": "I'm now in general conversation mode. How can I help you today?",
                    "mode_changed": "general",
                    "success": True
                }

            elif action == "show_my_tasks":
                # Get user's tasks
                db = next(get_db())
                tasks = crud.get_user_tasks(db, user_id, limit=10)
                db.close()

                if tasks:
                    task_list = "\n".join([f"• {task.content}" for task in tasks[:5]])
                    response = f"Here are your recent tasks:\n{task_list}"
                    if len(tasks) > 5:
                        response += f"\n... and {len(tasks) - 5} more tasks."
                else:
                    response = "You don't have any tasks yet. Would you like to create some?"

                return {
                    "response": response,
                    "tasks_shown": len(tasks),
                    "success": True
                }

            elif action == "clear_conversation":
                # Clear conversation context
                state["conversation_context"] = {"mode": "general"}

                return {
                    "response": "Conversation cleared. We're starting fresh! How can I help you?",
                    "conversation_cleared": True,
                    "success": True
                }

            else:
                # Unknown action
                logger.warning(f"Unknown command action: {action}")
                return {
                    "response": f"I recognize your command, but I don't know how to perform the action '{action}' yet. Please check your command configuration.",
                    "error": f"Unknown action: {action}",
                    "success": False
                }

        except Exception as e:
            logger.error(f"Error executing command action '{action}': {e}")
            return {
                "response": "I encountered an error while executing your command. Please try again.",
                "error": str(e),
                "success": False
            }

    def _should_use_tools(self, state: ConversationState) -> str:
        """
        Conditional routing function to determine if tools should be used.

        Args:
            state: Current conversation state

        Returns:
            "tools" if tool calls are detected, "end" otherwise
        """
        # Check if there are tool calls in the state
        tool_calls = state.get("tool_calls", [])

        if tool_calls and len(tool_calls) > 0:
            logger.info(f"Tool calls detected: {len(tool_calls)} tools to execute")
            return "tools"
        else:
            logger.debug("No tool calls detected, proceeding to memory storage")
            return "end"

    def _memory_retrieval_node(self, state: ConversationState) -> Dict[str, Any]:
        """
        Enhanced 3-layer memory retrieval node with Redis caching and semantic search.

        Layer 1 (Active Memory): Redis cache for last 5-10 messages of current conversation
        Layer 2 (Semantic Cache): Redis vector search for memories from last 7 days
        Layer 3 (Long-term Memory): PostgreSQL semantic search for historical memories

        Args:
            state: Current conversation state

        Returns:
            Updated state with relevant memories from all layers
        """
        try:
            user_id = state.get("user_id")
            conversation_id = state.get("conversation_id")
            messages = state.get("messages", [])

            if not user_id:
                logger.warning("No user_id in state for memory retrieval")
                return {"relevant_memories": []}

            # Get the latest user message for semantic search
            user_message = None
            for msg in reversed(messages):
                if isinstance(msg, HumanMessage):
                    user_message = msg.content
                    break

            if not user_message:
                logger.debug("No user message found for memory retrieval")
                return {"relevant_memories": []}

            relevant_memories = []

            # LAYER 1: Active Memory - Check Redis for recent conversation messages
            layer1_memories = self._get_active_memory(user_id, conversation_id)
            if layer1_memories:
                relevant_memories.extend(layer1_memories)
                logger.info(f"Layer 1 (Active): Retrieved {len(layer1_memories)} recent messages")

            # LAYER 2: Semantic Cache - Redis vector search for last 7 days
            layer2_memories = self._get_semantic_cache(user_id, user_message)
            if layer2_memories:
                relevant_memories.extend(layer2_memories)
                logger.info(f"Layer 2 (Semantic Cache): Retrieved {len(layer2_memories)} cached memories")

            # LAYER 3: Long-term Memory - PostgreSQL semantic search (fallback)
            if len(relevant_memories) < 3:  # Only hit DB if we don't have enough context
                layer3_memories = self._get_longterm_memory(user_id, user_message, conversation_id)
                relevant_memories.extend(layer3_memories)
                logger.info(f"Layer 3 (Long-term): Retrieved {len(layer3_memories)} historical memories")

            # Remove duplicates and limit total memories
            unique_memories = []
            seen_content = set()
            for memory in relevant_memories:
                content_key = f"{memory.get('content', '')}_{memory.get('conversation_id', '')}"
                if content_key not in seen_content:
                    seen_content.add(content_key)
                    unique_memories.append(memory)
                    if len(unique_memories) >= 8:  # Limit total memories
                        break

            logger.info(f"Total unique memories retrieved: {len(unique_memories)} across all layers")
            return {"relevant_memories": unique_memories}

        except Exception as e:
            logger.error(f"Error in enhanced memory retrieval node: {e}")
            return {"relevant_memories": []}

    def _get_active_memory(self, user_id: str, conversation_id: Optional[str]) -> List[Dict[str, Any]]:
        """
        Layer 1: Get recent messages from Redis cache for active conversation.

        Args:
            user_id: User identifier
            conversation_id: Current conversation ID

        Returns:
            List of recent message memories
        """
        if not conversation_id:
            return []

        try:
            redis_client = get_redis()
            if not redis_client:
                logger.warning("Redis client not available for active memory")
                return []

            # Get recent messages from Redis
            cache_key = f"conversation:{user_id}:{conversation_id}:messages"
            cached_messages = redis_client.lrange(cache_key, -10, -1)  # Last 10 messages

            memories = []
            for msg_json in cached_messages:
                try:
                    msg_data = json.loads(msg_json)
                    memories.append({
                        "content": msg_data.get("content", ""),
                        "role": msg_data.get("role", ""),
                        "conversation_id": conversation_id,
                        "created_at": msg_data.get("created_at"),
                        "source": "active_memory"
                    })
                except json.JSONDecodeError:
                    continue

            return memories

        except Exception as e:
            logger.error(f"Error retrieving active memory: {e}")
            return []

    def _get_semantic_cache(self, user_id: str, query: str) -> List[Dict[str, Any]]:
        """
        Layer 2: Get semantically similar memories from Redis vector cache (last 7 days).

        Args:
            user_id: User identifier
            query: User's current message for similarity search

        Returns:
            List of semantically similar memories from cache
        """
        try:
            redis_client = get_redis()
            if not redis_client:
                logger.warning("Redis client not available for semantic cache")
                return []

            # Generate embedding for query
            query_embedding = embedding_service.generate_embedding(query)

            # Search in Redis vector index (if available)
            # Note: This requires Redis Stack with RediSearch module
            cache_key = f"semantic_cache:{user_id}"
            seven_days_ago = (datetime.now() - timedelta(days=7)).timestamp()

            # Try to get cached similar memories
            # For now, we'll use a simple key-based approach
            # In production, this would use Redis vector search capabilities
            cached_memories = redis_client.get(f"{cache_key}:{hash(query) % 1000}")

            if cached_memories:
                try:
                    memories_data = json.loads(cached_memories)
                    return memories_data.get("memories", [])
                except json.JSONDecodeError:
                    pass

            return []

        except Exception as e:
            logger.error(f"Error retrieving semantic cache: {e}")
            return []

    def _get_longterm_memory(self, user_id: str, query: str, conversation_id: Optional[str]) -> List[Dict[str, Any]]:
        """
        Layer 3: Get memories from PostgreSQL semantic search (fallback).

        Args:
            user_id: User identifier
            query: User's current message for similarity search
            conversation_id: Current conversation ID to exclude

        Returns:
            List of historically relevant memories
        """
        try:
            # Generate embedding for the user's query
            query_embedding = embedding_service.generate_embedding(query)

            # Get database session
            db = next(get_db())

            try:
                # Find similar messages from past conversations
                similar_messages = crud.find_similar_messages(
                    db=db,
                    query_vector=query_embedding,
                    user_id=user_id,
                    limit=5
                )

                # Format relevant memories
                memories = []
                for msg in similar_messages:
                    # Skip messages from current conversation to avoid redundancy
                    if conversation_id and msg.conversation_id == conversation_id:
                        continue

                    memory = {
                        "content": msg.content,
                        "role": msg.role,
                        "conversation_id": msg.conversation_id,
                        "created_at": msg.created_at.isoformat() if msg.created_at else None,
                        "source": "longterm_memory"
                    }
                    memories.append(memory)

                return memories

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error retrieving long-term memory: {e}")
            return []

    def _agent_node(self, state: ConversationState) -> Dict[str, Any]:
        """
        Main agent node that processes user input and generates responses.

        Args:
            state: Current conversation state

        Returns:
            Updated state with AI response
        """
        try:
            # Load the initial persona PRP
            persona_prp = prp_manager.get_system_prp("_initial_persona.md")

            if not persona_prp:
                logger.error("Failed to load initial persona PRP")
                system_prompt = "You are Darvis, a helpful AI assistant."
            else:
                # Build system prompt from PRP rules with memory context
                rules_text = "\n".join([f"- {rule}" for rule in persona_prp.get("rules", [])])

                # Add relevant memories to context if available
                memory_context = ""
                if relevant_memories:
                    memory_context = "\n\nRelevant memories from past conversations:\n"
                    for i, memory in enumerate(relevant_memories[:3], 1):  # Limit to top 3 memories
                        memory_context += f"{i}. {memory['content'][:200]}...\n"
                    memory_context += "\nUse these memories to provide more personalized and contextual responses.\n"

                # Load mode-specific PRP
                current_mode = state.get("current_mode", "general")
                mode_instructions = ""

                try:
                    if current_mode == "learning":
                        mode_prp = prp_manager.get_mode_prp("learning_mode.md")
                        if mode_prp:
                            mode_instructions = "\n".join(mode_prp.get("rules", []))
                    else:
                        # Default to general chat mode
                        mode_prp = prp_manager.get_mode_prp("general_chat.md")
                        if mode_prp:
                            mode_instructions = "\n".join(mode_prp.get("rules", []))

                            # Add search state information for general chat
                            is_search_enabled = state.get("is_search_enabled", False)
                            mode_instructions += f"\n\nIMPORTANT: is_search_enabled is currently {is_search_enabled}. Follow the search integration rules accordingly."

                except Exception as e:
                    logger.error(f"Error loading mode PRP for {current_mode}: {e}")
                    mode_instructions = "Respond helpfully and naturally to the user's message."

                # Check if therapy mode is active and add therapy context
                therapy_context = ""
                if state.get("therapy_mode_active", False):
                    therapy_context = self._build_therapy_system_prompt(state)

                system_prompt = f"""You are Darvis, an AI assistant. Follow these rules:

{rules_text}

Current Mode: {current_mode}
Mode-Specific Instructions:
{mode_instructions}

{therapy_context}

Additional context: {persona_prp.get('trigger', '')}{memory_context}"""

            # Get the latest user message and conversation context
            messages = state.get("messages", [])
            relevant_memories = state.get("relevant_memories", [])
            conversation_id = state.get("conversation_id")

            if not messages:
                logger.warning("No messages in state")
                return {"messages": [AIMessage(content="Hello! How can I help you today?")]}

            # Find the last human message
            user_message = None
            for msg in reversed(messages):
                if isinstance(msg, HumanMessage):
                    user_message = msg.content
                    break

            if not user_message:
                logger.warning("No user message found in conversation")
                return {"messages": [AIMessage(content="I didn't receive your message. Could you please try again?")]}

            # Load recent conversation history from database if conversation_id exists
            conversation_history = []
            if conversation_id:
                try:
                    db = next(get_db())
                    try:
                        recent_messages = crud.get_conversation_history(db, conversation_id, limit=10)
                        for msg in recent_messages[:-1]:  # Exclude the current message
                            conversation_history.append({
                                "role": "user" if msg.role == "human" else "assistant",
                                "content": msg.content
                            })
                    finally:
                        db.close()
                except Exception as e:
                    logger.error(f"Error loading conversation history: {e}")
                    # Fallback to in-memory messages
                    for msg in messages[:-1]:
                        if isinstance(msg, HumanMessage):
                            conversation_history.append({"role": "user", "content": msg.content})
                        elif isinstance(msg, AIMessage):
                            conversation_history.append({"role": "assistant", "content": msg.content})
            else:
                # Use in-memory conversation history
                for msg in messages[:-1]:
                    if isinstance(msg, HumanMessage):
                        conversation_history.append({"role": "user", "content": msg.content})
                    elif isinstance(msg, AIMessage):
                        conversation_history.append({"role": "assistant", "content": msg.content})

            # Create messages for LLM
            llm_messages = llm_provider.create_messages(
                system_prompt=system_prompt,
                user_message=user_message,
                conversation_history=conversation_history
            )

            # Check if we're processing tool results
            tool_results = state.get("tool_results", [])
            if tool_results:
                # We're coming back from tool execution, formulate response based on results
                return self._formulate_tool_response(state, tool_results)

            # Check if this looks like a tool-using request
            tool_prp = self._detect_tool_intent(user_message)

            # Add tool instructions if tool intent detected
            if tool_prp:
                tool_instructions = f"\n\nTOOL USAGE INSTRUCTIONS:\n{tool_prp}\n"
                tool_instructions += """
When you need to use a tool, format your response like this:
TOOL_CALL: tool_name
PARAMETERS: {"param1": "value1", "param2": "value2"}
REASONING: Brief explanation of why you're using this tool

Available tools:
- create_calendar_event: Create events in Google Calendar
- image_to_event: Extract event details from images (requires image_data parameter)
- brave_search: Search the web for current information (requires query parameter)
"""
                system_prompt += tool_instructions

            # Get AI response
            ai_response = llm_provider.invoke(llm_messages)

            # Detect tool calls in the response
            tool_calls = self._extract_tool_calls(ai_response.content)

            logger.info(f"Generated AI response for user {state.get('user_id', 'unknown')}")

            result = {"messages": [ai_response]}
            if tool_calls:
                result["tool_calls"] = tool_calls
                logger.info(f"Detected {len(tool_calls)} tool calls in AI response")

            return result

        except Exception as e:
            logger.error(f"Error in agent node: {e}")
            error_response = AIMessage(
                content="I apologize, but I encountered an error while processing your request. Please try again."
            )
            return {"messages": [error_response]}

    def _detect_tool_intent(self, user_message: str) -> Optional[str]:
        """
        Detect if the user message indicates intent to use tools.

        Args:
            user_message: User's input message

        Returns:
            Tool PRP content if tool intent detected, None otherwise
        """
        user_lower = user_message.lower()

        # Keywords that suggest image-to-event intent
        image_event_keywords = [
            "image", "photo", "picture", "extract event", "read event",
            "event details", "what event", "analyze image", "from this image"
        ]

        # Keywords that suggest search intent (when search is enabled)
        search_keywords = [
            "latest", "recent", "current", "news", "what's happening", "search for",
            "look up", "find information", "up to date", "today", "this week"
        ]

        # Keywords that suggest calendar/scheduling intent
        calendar_keywords = [
            "schedule", "meeting", "appointment", "calendar", "event",
            "book", "plan", "remind", "tomorrow", "next week", "at", "pm", "am"
        ]

        # Check for image-to-event intent first
        if any(keyword in user_lower for keyword in image_event_keywords):
            try:
                # Load the image-to-event tool PRP
                tool_prp = prp_manager.get_tool_prp("image_to_event.md")
                if tool_prp:
                    return "\n".join(tool_prp.get("rules", []))
            except Exception as e:
                logger.error(f"Error loading image-to-event tool PRP: {e}")

        # Check for search intent (only if search is enabled in state)
        elif any(keyword in user_lower for keyword in search_keywords):
            # Note: Search intent will be handled in the agent node based on is_search_enabled state
            # This is just for detection, actual search logic is in general_chat.md PRP
            return "SEARCH_INTENT_DETECTED"

        # Check for calendar intent
        elif any(keyword in user_lower for keyword in calendar_keywords):
            try:
                # Load the calendar tool PRP
                tool_prp = prp_manager.get_tool_prp("create_calendar_event.md")
                if tool_prp:
                    return "\n".join(tool_prp.get("rules", []))
            except Exception as e:
                logger.error(f"Error loading calendar tool PRP: {e}")

        return None

    def _extract_tool_calls(self, ai_response: str) -> List[Dict[str, Any]]:
        """
        Extract tool calls from AI response.

        Args:
            ai_response: AI's response text

        Returns:
            List of detected tool calls
        """
        tool_calls = []

        # Look for TOOL_CALL pattern
        tool_pattern = r'TOOL_CALL:\s*(\w+)\s*\nPARAMETERS:\s*(\{[^}]+\})\s*\nREASONING:\s*([^\n]+)'
        matches = re.findall(tool_pattern, ai_response, re.MULTILINE | re.IGNORECASE)

        for match in matches:
            tool_name, parameters_str, reasoning = match
            try:
                parameters = json.loads(parameters_str)
                tool_calls.append({
                    "tool_name": tool_name.lower(),
                    "parameters": parameters,
                    "reasoning": reasoning.strip()
                })
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse tool parameters: {e}")
                continue

        return tool_calls

    def _formulate_tool_response(self, state: ConversationState, tool_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Formulate a response based on tool execution results.

        Args:
            state: Current conversation state
            tool_results: Results from tool execution

        Returns:
            Updated state with formulated response
        """
        # Create a summary of tool results
        results_summary = []
        for result in tool_results:
            if result.get("success"):
                results_summary.append(f"✅ {result.get('message', 'Tool executed successfully')}")
            else:
                results_summary.append(f"❌ {result.get('message', 'Tool execution failed')}")

        response_content = "\n".join(results_summary)

        # Clear tool results from state
        return {
            "messages": [AIMessage(content=response_content)],
            "tool_results": []  # Clear tool results
        }

    def _tool_execution_node(self, state: ConversationState) -> Dict[str, Any]:
        """
        Tool execution node that executes detected tool calls.

        Args:
            state: Current conversation state

        Returns:
            Updated state with tool execution results
        """
        try:
            tool_calls = state.get("tool_calls", [])
            if not tool_calls:
                logger.warning("Tool execution node called but no tool calls found")
                return {"tool_results": []}

            # Import the tools SDKs
            from app.sdk.composio_tools_sdk import composio_tools
            from app.sdk.groq_vision_sdk import GroqVisionSDK
            from app.sdk.brave_search_sdk import brave_search

            tool_results = []
            user_id = state.get("user_id", "default")
            conversation_id = state.get("conversation_id")

            for tool_call in tool_calls:
                tool_name = tool_call.get("tool_name")
                parameters = tool_call.get("parameters", {})
                reasoning = tool_call.get("reasoning", "")

                logger.info(f"Executing tool: {tool_name} with parameters: {parameters}")

                # Execute the appropriate tool
                if tool_name == "create_calendar_event":
                    result = composio_tools.create_calendar_event(
                        user_id=user_id,
                        summary=parameters.get("summary", ""),
                        start_time=parameters.get("start_time", ""),
                        end_time=parameters.get("end_time", ""),
                        description=parameters.get("description"),
                        location=parameters.get("location")
                    )
                elif tool_name == "image_to_event":
                    # Initialize Groq Vision SDK
                    vision_sdk = GroqVisionSDK()

                    # Extract event details from image
                    image_data = parameters.get("image_data")
                    if image_data:
                        result = vision_sdk.extract_event_from_image(image_data)
                        if result.get("success"):
                            # Format success response
                            event_details = result.get("event_details", {})
                            result["message"] = f"Successfully extracted event details: {event_details.get('event_name', 'Unknown')} on {event_details.get('event_date', 'Unknown date')} at {event_details.get('event_location', 'Unknown location')}"
                        else:
                            # Format error response
                            result["message"] = result.get("error", "Failed to extract event details from image")
                    else:
                        result = {
                            "success": False,
                            "error": "No image data provided",
                            "message": "Image data is required for event extraction"
                        }
                elif tool_name == "brave_search":
                    # Perform web search
                    query = parameters.get("query")
                    count = parameters.get("count", 5)

                    if query:
                        search_result = brave_search.search(query=query, count=count)
                        if search_result.get("success"):
                            # Format search results for AI consumption
                            formatted_results = brave_search.format_search_results_for_ai(search_result)
                            result = {
                                "success": True,
                                "search_results": formatted_results,
                                "raw_results": search_result.get("results", []),
                                "message": f"Found {len(search_result.get('results', []))} search results for '{query}'"
                            }
                        else:
                            result = {
                                "success": False,
                                "error": search_result.get("error", "Search failed"),
                                "message": f"Search failed for query '{query}'"
                            }
                    else:
                        result = {
                            "success": False,
                            "error": "No search query provided",
                            "message": "Search query is required"
                        }
                else:
                    result = {
                        "success": False,
                        "error": f"Unknown tool: {tool_name}",
                        "message": f"Tool '{tool_name}' is not available"
                    }

                tool_results.append(result)

                # Log the tool execution to database
                self._log_tool_execution(
                    conversation_id=conversation_id,
                    tool_name=tool_name,
                    input_params=parameters,
                    output_summary=result.get("message", ""),
                    success=result.get("success", False)
                )

            logger.info(f"Executed {len(tool_results)} tools")
            return {
                "tool_results": tool_results,
                "tool_calls": []  # Clear tool calls after execution
            }

        except Exception as e:
            logger.error(f"Error in tool execution node: {e}")
            return {
                "tool_results": [{
                    "success": False,
                    "error": str(e),
                    "message": f"Tool execution failed: {str(e)}"
                }],
                "tool_calls": []
            }

    def _log_tool_execution(
        self,
        conversation_id: Optional[str],
        tool_name: str,
        input_params: Dict[str, Any],
        output_summary: str,
        success: bool
    ):
        """
        Log tool execution to the database.

        Args:
            conversation_id: ID of the conversation
            tool_name: Name of the executed tool
            input_params: Input parameters for the tool
            output_summary: Summary of the tool output
            success: Whether the tool execution was successful
        """
        if not conversation_id:
            logger.warning("No conversation_id provided for tool logging")
            return

        try:
            from app.db.database import get_db
            from app.db import crud

            db = next(get_db())
            try:
                crud.create_action_log(
                    db=db,
                    conversation_id=conversation_id,
                    tool_name=tool_name,
                    input_params=input_params,
                    output_summary=output_summary,
                    success=success
                )
                logger.info(f"Logged tool execution: {tool_name}")
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error logging tool execution: {e}")

    def _memory_storage_node(self, state: ConversationState) -> Dict[str, Any]:
        """
        Memory storage node that saves the conversation to the database.

        Args:
            state: Current conversation state

        Returns:
            Updated state
        """
        try:
            user_id = state.get("user_id")
            conversation_id = state.get("conversation_id")
            messages = state.get("messages", [])

            if not user_id or not messages:
                logger.warning("Missing user_id or messages for memory storage")
                return {}

            # Get database session
            db = next(get_db())

            try:
                # Create conversation if it doesn't exist
                if not conversation_id:
                    conversation = crud.create_conversation(db, user_id)
                    conversation_id = conversation.id
                    logger.info(f"Created new conversation {conversation_id} for user {user_id}")

                # Find the latest user and AI messages to save
                user_message = None
                ai_message = None

                for msg in reversed(messages):
                    if isinstance(msg, AIMessage) and not ai_message:
                        ai_message = msg
                    elif isinstance(msg, HumanMessage) and not user_message:
                        user_message = msg

                    if user_message and ai_message:
                        break

                # Save user message with embedding
                if user_message:
                    user_embedding = embedding_service.generate_embedding(user_message.content)
                    crud.save_message(
                        db=db,
                        conversation_id=conversation_id,
                        role="human",
                        content=user_message.content,
                        vector=user_embedding
                    )
                    logger.debug(f"Saved user message to conversation {conversation_id}")

                # Save AI message with embedding
                if ai_message:
                    ai_embedding = embedding_service.generate_embedding(ai_message.content)
                    crud.save_message(
                        db=db,
                        conversation_id=conversation_id,
                        role="ai",
                        content=ai_message.content,
                        vector=ai_embedding
                    )
                    logger.debug(f"Saved AI message to conversation {conversation_id}")

                # Cache messages in Redis for Layer 1 (Active Memory)
                self._cache_messages_in_redis(user_id, conversation_id, user_message, ai_message)

                return {"conversation_id": conversation_id}

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error in memory storage node: {e}")
            return {}

    def _cache_messages_in_redis(self, user_id: str, conversation_id: str, user_message, ai_message):
        """
        Cache recent messages in Redis for Layer 1 active memory.

        Args:
            user_id: User identifier
            conversation_id: Conversation identifier
            user_message: User's message to cache
            ai_message: AI's response to cache
        """
        try:
            redis_client = get_redis()
            if not redis_client:
                logger.warning("Redis client not available for message caching")
                return

            cache_key = f"conversation:{user_id}:{conversation_id}:messages"
            current_time = datetime.now().isoformat()

            # Cache user message
            if user_message:
                user_msg_data = {
                    "content": user_message.content,
                    "role": "human",
                    "created_at": current_time
                }
                redis_client.lpush(cache_key, json.dumps(user_msg_data))

            # Cache AI message
            if ai_message:
                ai_msg_data = {
                    "content": ai_message.content,
                    "role": "ai",
                    "created_at": current_time
                }
                redis_client.lpush(cache_key, json.dumps(ai_msg_data))

            # Keep only last 20 messages in cache
            redis_client.ltrim(cache_key, 0, 19)

            # Set expiration for 24 hours
            redis_client.expire(cache_key, 86400)

            logger.debug(f"Cached messages in Redis for conversation {conversation_id}")

        except Exception as e:
            logger.error(f"Error caching messages in Redis: {e}")

    def process_message(
        self,
        user_message: str,
        user_id: str,
        conversation_id: str = None,
        conversation_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Process a user message through the conversation graph with memory.

        Args:
            user_message: User's input message
            user_id: Unique identifier for the user
            conversation_id: Optional conversation ID for memory continuity
            conversation_context: Optional conversation context

        Returns:
            Dictionary with AI response and conversation metadata
        """
        if not self.graph:
            logger.error("Graph not initialized")
            return {
                "response": "I'm sorry, but I'm not ready to chat yet. Please try again later.",
                "conversation_id": None,
                "error": "Graph not initialized"
            }

        try:
            # Create initial state
            initial_state = ConversationState(
                messages=[HumanMessage(content=user_message)],
                user_id=user_id,
                conversation_id=conversation_id,
                conversation_context=conversation_context or {},
                relevant_memories=[],
                is_search_enabled=False,  # Default to False, can be enabled per user/conversation
                current_mode="general"  # Default mode
            )

            # Run the graph
            result = self.graph.invoke(initial_state)

            # Extract the AI response and conversation metadata
            messages = result.get("messages", [])
            ai_messages = [msg for msg in messages if isinstance(msg, AIMessage)]
            final_conversation_id = result.get("conversation_id", conversation_id)

            if ai_messages:
                return {
                    "response": ai_messages[-1].content,
                    "conversation_id": final_conversation_id,
                    "relevant_memories_count": len(result.get("relevant_memories", [])),
                    "success": True
                }
            else:
                logger.warning("No AI response generated")
                return {
                    "response": "I'm sorry, I couldn't generate a response. Please try again.",
                    "conversation_id": final_conversation_id,
                    "error": "No AI response generated"
                }

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                "response": "I apologize, but I encountered an error. Please try again later.",
                "conversation_id": conversation_id,
                "error": str(e)
            }

    def stream_message(
        self,
        user_message: str,
        user_id: str,
        conversation_id: str = None,
        model_name: str = None,
        conversation_context: Dict[str, Any] = None
    ):
        """
        Stream a user message through the conversation graph with memory.

        Args:
            user_message: User's input message
            user_id: Unique identifier for the user
            conversation_id: Optional conversation ID for memory continuity
            model_name: Optional model name to override default
            conversation_context: Optional conversation context

        Yields:
            Streaming response chunks with metadata
        """
        if not self.graph:
            logger.error("Graph not initialized")
            yield {
                "type": "error",
                "content": "I'm sorry, but I'm not ready to chat yet. Please try again later.",
                "conversation_id": None,
                "error": "Graph not initialized"
            }
            return

        try:
            # First, run memory retrieval to get context
            initial_state = ConversationState(
                messages=[HumanMessage(content=user_message)],
                user_id=user_id,
                conversation_id=conversation_id,
                conversation_context=conversation_context or {},
                relevant_memories=[],
                is_search_enabled=False,  # Default to False
                current_mode="general"  # Default mode
            )

            # Run memory retrieval node
            memory_result = self._memory_retrieval_node(initial_state)
            relevant_memories = memory_result.get("relevant_memories", [])

            # Prepare context for streaming
            persona_prp = prp_manager.get_system_prp("_initial_persona.md")

            if not persona_prp:
                logger.error("Failed to load initial persona PRP")
                system_prompt = "You are Darvis, a helpful AI assistant."
            else:
                # Build system prompt from PRP rules with memory context
                rules_text = "\n".join([f"- {rule}" for rule in persona_prp.get("rules", [])])

                # Add relevant memories to context if available
                memory_context = ""
                if relevant_memories:
                    memory_context = "\n\nRelevant memories from past conversations:\n"
                    for i, memory in enumerate(relevant_memories[:3], 1):  # Limit to top 3 memories
                        memory_context += f"{i}. {memory['content'][:200]}...\n"
                    memory_context += "\nUse these memories to provide more personalized and contextual responses.\n"

                # Load mode-specific PRP for streaming
                current_mode = "general"  # Default mode for streaming
                mode_instructions = ""

                try:
                    mode_prp = prp_manager.get_mode_prp("general_chat.md")
                    if mode_prp:
                        mode_instructions = "\n".join(mode_prp.get("rules", []))
                        # Add search state information (default disabled for streaming)
                        mode_instructions += f"\n\nIMPORTANT: is_search_enabled is currently False. Follow the search integration rules accordingly."
                except Exception as e:
                    logger.error(f"Error loading mode PRP for streaming: {e}")
                    mode_instructions = "Respond helpfully and naturally to the user's message."

                system_prompt = f"""You are Darvis, an AI assistant. Follow these rules:

{rules_text}

Current Mode: {current_mode}
Mode-Specific Instructions:
{mode_instructions}

Additional context: {persona_prp.get('trigger', '')}{memory_context}"""

            # Load conversation history
            conversation_history = []
            if conversation_id:
                try:
                    db = next(get_db())
                    try:
                        recent_messages = crud.get_conversation_history(db, conversation_id, limit=10)
                        for msg in recent_messages[:-1]:  # Exclude the current message
                            conversation_history.append({
                                "role": "user" if msg.role == "human" else "assistant",
                                "content": msg.content
                            })
                    finally:
                        db.close()
                except Exception as e:
                    logger.error(f"Error loading conversation history: {e}")

            # Create messages for LLM
            llm_messages = llm_provider.create_messages(
                system_prompt=system_prompt,
                user_message=user_message,
                conversation_history=conversation_history
            )

            # Yield initial metadata
            yield {
                "type": "metadata",
                "conversation_id": conversation_id,
                "relevant_memories_count": len(relevant_memories),
                "model": model_name or llm_provider.model
            }

            # Stream the AI response
            full_response = ""
            for chunk in llm_provider.stream(llm_messages, model_name):
                if chunk.get("choices") and chunk["choices"][0].get("delta", {}).get("content"):
                    content = chunk["choices"][0]["delta"]["content"]
                    full_response += content
                    yield {
                        "type": "content",
                        "content": content
                    }

            # After streaming is complete, save to memory
            try:
                # Save the conversation to memory
                db = next(get_db())
                try:
                    # Create conversation if it doesn't exist
                    if not conversation_id:
                        conversation = crud.create_conversation(db, user_id)
                        conversation_id = conversation.id
                        logger.info(f"Created new conversation {conversation_id} for user {user_id}")

                    # Save user message with embedding
                    user_embedding = embedding_service.generate_embedding(user_message)
                    crud.save_message(
                        db=db,
                        conversation_id=conversation_id,
                        role="human",
                        content=user_message,
                        vector=user_embedding
                    )

                    # Save AI message with embedding
                    if full_response:
                        ai_embedding = embedding_service.generate_embedding(full_response)
                        crud.save_message(
                            db=db,
                            conversation_id=conversation_id,
                            role="ai",
                            content=full_response,
                            vector=ai_embedding
                        )

                    # Yield final metadata
                    yield {
                        "type": "complete",
                        "conversation_id": conversation_id,
                        "success": True
                    }

                finally:
                    db.close()

            except Exception as e:
                logger.error(f"Error saving streaming conversation to memory: {e}")
                yield {
                    "type": "complete",
                    "conversation_id": conversation_id,
                    "success": False,
                    "error": str(e)
                }

        except Exception as e:
            logger.error(f"Error in streaming message: {e}")
            yield {
                "type": "error",
                "content": "I apologize, but I encountered an error. Please try again later.",
                "conversation_id": conversation_id,
                "error": str(e)
            }

    def _therapy_mode_check_node(self, state: ConversationState) -> ConversationState:
        """
        Check if the conversation should use therapy mode based on context or explicit request.

        This node determines whether to activate therapy-specific processing based on:
        - Explicit therapy mode request
        - Conversation context indicating therapeutic need
        - User's therapy session history
        """
        try:
            # Check for explicit therapy mode request
            user_message = state.get("user_message", "").lower()
            therapy_keywords = ["therapy", "therapeutic", "counseling", "mental health", "emotional support"]

            # Check conversation context for therapy mode
            conversation_context = state.get("conversation_context", {})
            is_therapy_mode = conversation_context.get("therapy_mode", False)

            # Check if user explicitly requested therapy mode
            explicit_therapy_request = any(keyword in user_message for keyword in therapy_keywords)

            # Check if this is a continuation of an existing therapy session
            conversation_id = state.get("conversation_id")
            user_id = state.get("user_id")

            existing_therapy_session = False
            if conversation_id and user_id:
                try:
                    from app.db.database import SessionLocal
                    from app.db import crud

                    db = SessionLocal()
                    therapy_session = crud.get_therapy_session_by_conversation(db, conversation_id)
                    existing_therapy_session = therapy_session is not None
                    db.close()

                except Exception as e:
                    logger.warning(f"Error checking existing therapy session: {e}")

            # Determine if therapy mode should be activated
            should_use_therapy = (
                is_therapy_mode or
                explicit_therapy_request or
                existing_therapy_session
            )

            # Update state with therapy mode decision
            state["therapy_mode_active"] = should_use_therapy
            state["therapy_mode_reason"] = (
                "explicit_request" if explicit_therapy_request else
                "existing_session" if existing_therapy_session else
                "context_mode" if is_therapy_mode else
                "none"
            )

            logger.info(f"Therapy mode check: {should_use_therapy} (reason: {state['therapy_mode_reason']})")
            return state

        except Exception as e:
            logger.error(f"Error in therapy mode check: {e}")
            state["therapy_mode_active"] = False
            state["therapy_mode_reason"] = "error"
            return state

    def _should_use_therapy_mode(self, state: ConversationState) -> str:
        """Determine routing after therapy mode check."""
        return "therapy" if state.get("therapy_mode_active", False) else "normal"

    def _therapy_context_retrieval_node(self, state: ConversationState) -> ConversationState:
        """
        Retrieve therapy-specific context and knowledge for enhanced therapeutic responses.

        This node enriches the conversation state with relevant therapy content,
        user's therapy history, and personalized therapeutic interventions.
        """
        try:
            user_message = state.get("user_message", "")
            user_id = state.get("user_id")
            conversation_id = state.get("conversation_id")

            if not user_id:
                logger.warning("No user_id provided for therapy context retrieval")
                return state

            from app.db.database import SessionLocal
            from app.db import crud
            from app.sdk.embedding_sdk import EmbeddingSDK
            from app.sdk.therapy_retrieval_sdk import create_therapy_retrieval_sdk

            db = SessionLocal()

            try:
                # Initialize therapy retrieval system
                embedding_sdk = EmbeddingSDK()
                therapy_retrieval = create_therapy_retrieval_sdk(db, embedding_sdk)

                # Get or create therapy session
                therapy_session = crud.get_therapy_session_by_conversation(db, conversation_id)
                if not therapy_session:
                    # Create new therapy session
                    therapy_session = crud.create_therapy_session(
                        db=db,
                        conversation_id=conversation_id,
                        user_id=user_id,
                        session_mode="chat"  # Will be updated if voice is used
                    )

                # Detect emotional context from user message
                emotional_context = self._detect_emotional_context(user_message)

                # Update therapy session with current emotional state
                if emotional_context:
                    crud.update_therapy_session_emotional_state(
                        db=db,
                        session_id=therapy_session.id,
                        emotional_state=emotional_context
                    )

                # Retrieve relevant therapy content
                session_context = {
                    "emotional_state": emotional_context,
                    "focus": therapy_session.session_focus,
                    "session_id": therapy_session.id
                }

                therapy_content = therapy_retrieval.retrieve_therapy_content(
                    query=user_message,
                    user_id=user_id,
                    session_context=session_context,
                    top_k=3,
                    emotional_themes=emotional_context.get("descriptors", []) if emotional_context else None
                )

                # Get personalized interventions
                personalized_interventions = therapy_retrieval.retrieve_personalized_interventions(
                    user_id=user_id,
                    emotional_state=emotional_context or {},
                    session_focus=therapy_session.session_focus,
                    limit=2
                )

                # Enrich state with therapy context
                state["therapy_session"] = {
                    "id": therapy_session.id,
                    "focus": therapy_session.session_focus,
                    "emotional_state": emotional_context,
                    "session_goals": therapy_session.session_goals
                }

                state["therapy_content"] = therapy_content
                state["personalized_interventions"] = personalized_interventions
                state["therapy_context_retrieved"] = True

                logger.info(f"Retrieved {len(therapy_content)} therapy content pieces and {len(personalized_interventions)} interventions for user {user_id}")

            finally:
                db.close()

            return state

        except Exception as e:
            logger.error(f"Error in therapy context retrieval: {e}")
            state["therapy_context_retrieved"] = False
            return state

    def _detect_emotional_context(self, user_message: str) -> Optional[Dict[str, Any]]:
        """
        Detect emotional context from user message using keyword analysis.

        In a production system, this could be enhanced with sentiment analysis
        or emotion detection models.
        """
        try:
            # Simple keyword-based emotion detection
            emotion_keywords = {
                "anxiety": ["anxious", "worried", "nervous", "stressed", "panic", "fear"],
                "depression": ["sad", "depressed", "down", "hopeless", "empty", "worthless"],
                "anger": ["angry", "mad", "frustrated", "irritated", "furious", "rage"],
                "joy": ["happy", "joyful", "excited", "elated", "cheerful", "content"],
                "fear": ["scared", "afraid", "terrified", "frightened", "worried"],
                "grief": ["loss", "grief", "mourning", "bereaved", "sorrow"]
            }

            message_lower = user_message.lower()
            detected_emotions = []

            for emotion, keywords in emotion_keywords.items():
                if any(keyword in message_lower for keyword in keywords):
                    detected_emotions.append(emotion)

            if detected_emotions:
                return {
                    "descriptors": detected_emotions,
                    "intensity": "moderate",  # Could be enhanced with intensity detection
                    "detected_at": datetime.utcnow().isoformat()
                }

            return None

        except Exception as e:
            logger.error(f"Error detecting emotional context: {e}")
            return None

    def _therapy_session_management_node(self, state: ConversationState) -> ConversationState:
        """
        Manage therapy session state, insights, and progress tracking.

        This node handles session conclusion, insight extraction, and progress recording
        for therapy mode conversations.
        """
        try:
            therapy_session = state.get("therapy_session", {})
            user_id = state.get("user_id")

            if not therapy_session or not user_id:
                return state

            from app.db.database import SessionLocal
            from app.db import crud

            db = SessionLocal()

            try:
                session_id = therapy_session.get("id")
                ai_response = state.get("messages", [])[-1] if state.get("messages") else None

                # Extract insights from the conversation
                insights = self._extract_therapy_insights(state)

                # Check if session should be concluded
                should_conclude = self._should_conclude_therapy_session(state)

                if should_conclude and insights:
                    # End therapy session with insights
                    practice_tasks = self._extract_practice_tasks(ai_response.content if ai_response else "")

                    crud.end_therapy_session(
                        db=db,
                        session_id=session_id,
                        key_insights=insights,
                        practice_tasks=practice_tasks
                    )

                    # Create progress entry for session completion
                    crud.create_therapy_progress_entry(
                        db=db,
                        user_id=user_id,
                        progress_type="session_completion",
                        content=f"Completed therapy session with {len(insights)} key insights",
                        session_id=session_id,
                        emotional_tags=therapy_session.get("emotional_state", {}).get("descriptors", [])
                    )

                    state["therapy_session_concluded"] = True
                    logger.info(f"Concluded therapy session {session_id} with {len(insights)} insights")

                # Update session activity
                if session_id:
                    # This would update last_activity_at timestamp
                    pass

            finally:
                db.close()

            return state

        except Exception as e:
            logger.error(f"Error in therapy session management: {e}")
            return state

    def _extract_therapy_insights(self, state: ConversationState) -> List[str]:
        """Extract key therapeutic insights from the conversation."""
        try:
            messages = state.get("messages", [])
            therapy_content = state.get("therapy_content", [])

            insights = []

            # Look for insight patterns in AI responses
            for message in messages:
                if hasattr(message, 'content') and message.content:
                    content = message.content.lower()

                    # Simple pattern matching for insights
                    insight_patterns = [
                        "it seems like",
                        "what i'm hearing is",
                        "the pattern i notice",
                        "key insight",
                        "important realization"
                    ]

                    for pattern in insight_patterns:
                        if pattern in content:
                            # Extract the sentence containing the insight
                            sentences = message.content.split('.')
                            for sentence in sentences:
                                if pattern in sentence.lower():
                                    insights.append(sentence.strip())
                                    break

            return insights[:3]  # Limit to top 3 insights

        except Exception as e:
            logger.error(f"Error extracting therapy insights: {e}")
            return []

    def _extract_practice_tasks(self, ai_response: str) -> List[str]:
        """Extract practice tasks or homework from AI response."""
        try:
            if not ai_response:
                return []

            tasks = []
            content_lower = ai_response.lower()

            # Look for task assignment patterns
            task_patterns = [
                "try practicing",
                "homework for you",
                "practice task",
                "exercise to try",
                "between now and next time"
            ]

            for pattern in task_patterns:
                if pattern in content_lower:
                    # Extract the sentence containing the task
                    sentences = ai_response.split('.')
                    for sentence in sentences:
                        if pattern in sentence.lower():
                            tasks.append(sentence.strip())
                            break

            return tasks

        except Exception as e:
            logger.error(f"Error extracting practice tasks: {e}")
            return []

    def _should_conclude_therapy_session(self, state: ConversationState) -> bool:
        """Determine if the therapy session should be concluded."""
        try:
            user_message = state.get("user_message", "").lower()

            # Look for session conclusion indicators
            conclusion_phrases = [
                "that's helpful",
                "thank you",
                "i feel better",
                "good for today",
                "end session",
                "that's enough for now"
            ]

            return any(phrase in user_message for phrase in conclusion_phrases)

        except Exception as e:
            logger.error(f"Error determining session conclusion: {e}")
            return False

    def _should_use_tools_or_therapy(self, state: ConversationState) -> str:
        """
        Enhanced routing logic that considers both tool usage and therapy session management.

        This replaces the original _should_use_tools method to handle therapy mode routing.
        """
        try:
            # Check if therapy mode is active
            therapy_mode_active = state.get("therapy_mode_active", False)

            # If therapy mode is active, check if session management is needed
            if therapy_mode_active:
                should_conclude = self._should_conclude_therapy_session(state)
                if should_conclude:
                    return "therapy_session"

            # Check for tool usage (original logic)
            messages = state.get("messages", [])
            if not messages:
                return "end"

            # Get the latest AI message
            ai_messages = [msg for msg in messages if hasattr(msg, 'content')]
            if not ai_messages:
                return "end"

            latest_message = ai_messages[-1]

            # Check if the AI response contains tool calls
            tool_calls = self._extract_tool_calls(latest_message.content)

            if tool_calls:
                state["tool_calls"] = tool_calls
                return "tools"

            return "end"

        except Exception as e:
            logger.error(f"Error in tool/therapy routing decision: {e}")
            return "end"

    def _build_therapy_system_prompt(self, state: ConversationState) -> str:
        """
        Build therapy-specific system prompt with retrieved therapy content and context.

        This method creates a comprehensive therapeutic system prompt that includes:
        - Therapy knowledge and techniques
        - User's emotional context
        - Personalized interventions
        - Session-specific guidance
        """
        try:
            therapy_content = state.get("therapy_content", [])
            personalized_interventions = state.get("personalized_interventions", [])
            therapy_session = state.get("therapy_session", {})

            therapy_prompt_parts = [
                "\n=== THERAPY MODE ACTIVATED ===",
                "You are now operating in Therapy Mode. Your role is to be a compassionate, empathetic, and supportive therapeutic companion.",
                "",
                "CORE THERAPEUTIC PRINCIPLES:",
                "- Listen actively and validate the user's feelings without judgment",
                "- Ask open-ended questions to help the user explore their thoughts and emotions",
                "- Offer evidence-based therapeutic techniques when appropriate",
                "- Maintain a warm, patient, and non-directive approach",
                "- Focus on empowerment and user agency rather than giving direct advice",
                "- Always acknowledge that you are an AI assistant, not a licensed therapist",
                ""
            ]

            # Add emotional context if available
            emotional_state = therapy_session.get("emotional_state")
            if emotional_state:
                therapy_prompt_parts.extend([
                    "CURRENT EMOTIONAL CONTEXT:",
                    f"- Detected emotions: {', '.join(emotional_state.get('descriptors', []))}",
                    f"- Emotional intensity: {emotional_state.get('intensity', 'unknown')}",
                    "- Respond with appropriate sensitivity to these emotional states",
                    ""
                ])

            # Add session focus if available
            session_focus = therapy_session.get("focus")
            if session_focus:
                therapy_prompt_parts.extend([
                    f"SESSION FOCUS: {session_focus}",
                    f"- Keep the conversation gently oriented toward this focus area",
                    f"- Use techniques and interventions relevant to {session_focus}",
                    ""
                ])

            # Add relevant therapy knowledge
            if therapy_content:
                therapy_prompt_parts.extend([
                    "RELEVANT THERAPY KNOWLEDGE:",
                    "Use the following therapeutic insights to inform your responses:"
                ])

                for i, content in enumerate(therapy_content[:3], 1):
                    technique = content.get("technique", "General")
                    therapy_prompt_parts.append(f"{i}. [{technique}] {content.get('content', '')[:200]}...")

                therapy_prompt_parts.append("")

            # Add personalized interventions
            if personalized_interventions:
                therapy_prompt_parts.extend([
                    "PERSONALIZED INTERVENTIONS:",
                    "These techniques have been effective for this user in the past:"
                ])

                for i, intervention in enumerate(personalized_interventions[:2], 1):
                    technique = intervention.get("technique", "General")
                    therapy_prompt_parts.append(f"{i}. [{technique}] {intervention.get('content', '')[:150]}...")

                therapy_prompt_parts.append("")

            # Add therapeutic conversation flow guidance
            therapy_prompt_parts.extend([
                "THERAPEUTIC CONVERSATION FLOW:",
                "1. LISTEN & VALIDATE: Always start by acknowledging and validating the user's feelings",
                "2. GUIDE & EXPLORE: Ask gentle, open-ended questions to help the user explore deeper",
                "3. OFFER TOOLS: When appropriate, suggest evidence-based techniques or exercises",
                "",
                "IMPORTANT REMINDERS:",
                "- You are an AI assistant providing support, not a licensed therapist",
                "- If the user expresses crisis thoughts, provide crisis resources immediately",
                "- Focus on the user's strengths and capacity for growth",
                "- Maintain appropriate boundaries while being warm and supportive",
                "- End sessions gracefully with a summary of insights when the user indicates they're ready",
                ""
            ])

            return "\n".join(therapy_prompt_parts)

        except Exception as e:
            logger.error(f"Error building therapy system prompt: {e}")
            return "\n=== THERAPY MODE ACTIVATED ===\nYou are operating in therapy mode. Be compassionate, empathetic, and supportive."


# Global orchestrator instance
darvis_orchestrator = DarvisOrchestrator()