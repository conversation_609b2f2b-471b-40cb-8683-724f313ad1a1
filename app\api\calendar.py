from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import (
    CalendarEventCreate, CalendarEventUpdate, CalendarEventResponse, 
    CalendarEventListResponse
)

router = APIRouter(prefix="/api/v1/calendar", tags=["Calendar"])

@router.post("/events", response_model=CalendarEventResponse, status_code=status.HTTP_201_CREATED)
def create_calendar_event(
    event: CalendarEventCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new calendar event for the authenticated user.
    
    This endpoint allows users to create detailed calendar events with support for
    recurring events, reminders, attendees, and rich metadata.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Request Body**:
    - **title**: Event title (1-200 characters) - **Required**
    - **start_datetime**: Event start date and time in ISO 8601 format - **Required**
    - **end_datetime**: Event end date and time in ISO 8601 format - **Required**
    - **description**: Optional event description (max 2000 characters)
    - **is_all_day**: Boolean indicating if event is all day - Default: false
    - **timezone**: Event timezone - Default: "UTC"
    - **is_recurring**: Boolean indicating if event repeats - Default: false
    - **recurrence_rule**: Recurrence rule in RRULE format (if recurring)
    - **category**: Event category for organization
    - **tags**: Array of strings for categorization
    - **color**: Hex color code for event display
    - **location**: Event location string
    - **reminder_minutes_before**: Array of minutes before event for reminders
    - **attendees**: Array of attendee objects
    
    **Returns**:
    - **201 Created**: Event successfully created with full event details
    - **400 Bad Request**: Invalid input data
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    # Validate start/end datetime
    if event.end_datetime <= event.start_datetime:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="End datetime must be after start datetime"
        )
    
    try:
        db_event = crud.create_calendar_event(
            db=db,
            user_id=user_id,
            title=event.title,
            description=event.description,
            start_datetime=event.start_datetime,
            end_datetime=event.end_datetime,
            is_all_day=event.is_all_day,
            timezone=event.timezone,
            is_recurring=event.is_recurring,
            recurrence_rule=event.recurrence_rule,
            recurrence_end_date=event.recurrence_end_date,
            category=event.category,
            tags=event.tags,
            color=event.color,
            location=event.location,
            location_data=event.location_data,
            reminder_minutes_before=event.reminder_minutes_before,
            attendees=event.attendees
        )
        return db_event
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create calendar event: {str(e)}"
        )

@router.get("/events", response_model=CalendarEventListResponse)
def get_calendar_events(
    skip: int = Query(0, ge=0, description="Number of events to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of events to return"),
    start_date: Optional[datetime] = Query(None, description="Filter events starting from this date"),
    end_date: Optional[datetime] = Query(None, description="Filter events ending before this date"),
    category: Optional[str] = Query(None, description="Filter by event category"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    is_recurring: Optional[bool] = Query(None, description="Filter by recurring status"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get calendar events for the authenticated user with filtering and pagination.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Query Parameters**:
    - **skip**: Number of events to skip (default: 0)
    - **limit**: Maximum number of events to return (default: 100, max: 1000)
    - **start_date**: Filter events starting from this date (ISO 8601 format)
    - **end_date**: Filter events ending before this date (ISO 8601 format)
    - **category**: Filter by event category
    - **tags**: Filter by one or more tags
    - **is_recurring**: Filter by recurring status (true/false)
    
    **Returns**:
    - **200 OK**: List of events with pagination info
    - **401 Unauthorized**: Invalid or expired authentication token
    
    Events are returned ordered by start datetime.
    """
    user_id = current_user["uid"]
    
    try:
        events = crud.get_user_calendar_events(
            db=db,
            user_id=user_id,
            skip=skip,
            limit=limit,
            start_date=start_date,
            end_date=end_date,
            category=category,
            tags=tags,
            is_recurring=is_recurring
        )
        
        total = crud.count_user_calendar_events(db=db, user_id=user_id)
        
        return CalendarEventListResponse(events=events, total=total)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve calendar events: {str(e)}"
        )

@router.get("/events/{event_id}", response_model=CalendarEventResponse)
def get_calendar_event(
    event_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific calendar event by ID.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Path Parameters**:
    - **event_id**: ID of the calendar event to retrieve
    
    **Returns**:
    - **200 OK**: Calendar event details
    - **404 Not Found**: Calendar event not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    db_event = crud.get_calendar_event_by_id(db=db, event_id=event_id, user_id=user_id)
    if not db_event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Calendar event not found"
        )
    
    return db_event

@router.put("/events/{event_id}", response_model=CalendarEventResponse)
def update_calendar_event(
    event_id: str,
    event_update: CalendarEventUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a calendar event.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Path Parameters**:
    - **event_id**: ID of the calendar event to update
    
    **Request Body**: Any combination of calendar event fields to update
    
    **Returns**:
    - **200 OK**: Updated calendar event details
    - **404 Not Found**: Calendar event not found
    - **400 Bad Request**: Invalid update data
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    # Convert Pydantic model to dict, excluding None values
    update_data = {k: v for k, v in event_update.model_dump().items() if v is not None}
    
    # Validate datetime logic if both are being updated
    if "start_datetime" in update_data and "end_datetime" in update_data:
        if update_data["end_datetime"] <= update_data["start_datetime"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="End datetime must be after start datetime"
            )
    
    try:
        updated_event = crud.update_calendar_event(
            db=db,
            event_id=event_id,
            user_id=user_id,
            event_data=update_data
        )
        
        if not updated_event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Calendar event not found"
            )
        
        return updated_event
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update calendar event: {str(e)}"
        )

@router.delete("/events/{event_id}")
def delete_calendar_event(
    event_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a calendar event.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Path Parameters**:
    - **event_id**: ID of the calendar event to delete
    
    **Returns**:
    - **204 No Content**: Calendar event successfully deleted
    - **404 Not Found**: Calendar event not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    success = crud.delete_calendar_event(db=db, event_id=event_id, user_id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Calendar event not found"
        )
    
    return {"message": "Calendar event deleted successfully"}

@router.get("/events/range/{start_date}/{end_date}", response_model=CalendarEventListResponse)
def get_events_in_date_range(
    start_date: datetime,
    end_date: datetime,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get calendar events within a specific date range.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Path Parameters**:
    - **start_date**: Start date for the range (ISO 8601 format)
    - **end_date**: End date for the range (ISO 8601 format)
    
    **Returns**:
    - **200 OK**: List of events within the date range
    - **400 Bad Request**: Invalid date range
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    if end_date <= start_date:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="End date must be after start date"
        )
    
    try:
        events = crud.get_events_for_date_range(
            db=db,
            user_id=user_id,
            start_date=start_date,
            end_date=end_date
        )
        
        return CalendarEventListResponse(events=events, total=len(events))
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve events for date range: {str(e)}"
        )
