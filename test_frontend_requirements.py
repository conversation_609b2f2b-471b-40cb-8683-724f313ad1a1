"""
Comprehensive test suite for frontend requirements implementation.

This test suite validates all the new functionality implemented to support
the frontend requirements documented in message_backend.md.

Tests include:
1. Session tracking endpoints
2. Enhanced profile management
3. Notification management
4. Enhanced dashboard data
5. Database operations
"""

import pytest
import json
from datetime import datetime, timedelta, timezone
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.db.database import get_db
from app.db.models import Base, User, UserSession, Notification, NotificationPreference
from app.db import crud
from app.api.auth import get_current_user

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_frontend_requirements.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

# Mock Firebase authentication
def mock_get_current_user():
    return {"uid": TEST_USER_ID, "email": TEST_USER_EMAIL}

app.dependency_overrides[get_current_user] = mock_get_current_user

# Create test client
client = TestClient(app)

# Mock Firebase token for testing
MOCK_FIREBASE_TOKEN = "mock_firebase_token_for_testing"
TEST_USER_ID = "test_user_123"
TEST_USER_EMAIL = "<EMAIL>"

@pytest.fixture(scope="module")
def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def test_db():
    """Get test database session."""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()

@pytest.fixture
def test_user(test_db):
    """Create a test user."""
    # Check if user already exists
    existing_user = crud.get_user_by_email(db=test_db, email=TEST_USER_EMAIL)
    if existing_user:
        return existing_user
    
    user = crud.create_user(db=test_db, user_id=TEST_USER_ID, email=TEST_USER_EMAIL)
    return user

@pytest.fixture
def auth_headers():
    """Mock authentication headers."""
    return {"Authorization": f"Bearer {MOCK_FIREBASE_TOKEN}"}

class TestSessionTracking:
    """Test session tracking endpoints."""
    
    def test_create_user_session(self, setup_database, test_user, auth_headers):
        """Test creating a user session."""
        session_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "session_type": "app_open"
        }
        
        response = client.post(
            "/api/v1/users/session",
            json=session_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "session_id" in data
        assert data["daily_count"] >= 1
    
    def test_get_session_data(self, setup_database, test_user, auth_headers):
        """Test getting session analytics data."""
        # First create a session
        session_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "session_type": "app_open"
        }
        client.post("/api/v1/users/session", json=session_data, headers=auth_headers)
        
        # Then get session data
        response = client.get("/api/v1/users/session-data", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "daily_sessions" in data
        assert "total_sessions" in data
        assert data["daily_sessions"] >= 1

class TestProfileManagement:
    """Test enhanced profile management endpoints."""
    
    def test_update_user_profile(self, setup_database, test_user, auth_headers):
        """Test updating user profile."""
        profile_data = {
            "display_name": "Test User",
            "darvis_name": "Tester",
            "time_zone": "America/New_York",
            "language": "en",
            "theme": "dark"
        }
        
        response = client.put(
            "/api/v1/users/profile",
            json=profile_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["display_name"] == "Test User"
    
    def test_update_profile_picture(self, setup_database, test_user, auth_headers):
        """Test updating profile picture."""
        picture_data = {
            "cloudinary_url": "https://res.cloudinary.com/test/image/upload/v123/profile.jpg",
            "cloudinary_public_id": "test/profiles/user_123_456789"
        }
        
        response = client.post(
            "/api/v1/users/profile-picture",
            json=picture_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["profile_picture_updated"] is True
    
    def test_delete_profile_picture(self, setup_database, test_user, auth_headers):
        """Test deleting profile picture."""
        response = client.delete(
            "/api/v1/users/profile-picture",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

class TestNotificationManagement:
    """Test notification management endpoints."""
    
    def test_create_notification(self, setup_database, test_user, auth_headers):
        """Test creating a notification."""
        notification_data = {
            "title": "Test Notification",
            "body": "This is a test notification",
            "type": "therapy_reminder",
            "priority": "normal",
            "scheduled_time": (datetime.now(timezone.utc) + timedelta(hours=1)).isoformat(),
            "navigation_route": "/therapy"
        }
        
        response = client.post(
            "/api/v1/notifications/",
            json=notification_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "notification_id" in data
    
    def test_get_notifications(self, setup_database, test_user, auth_headers):
        """Test getting user notifications."""
        response = client.get(
            "/api/v1/notifications/?page=1&limit=20",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "notifications" in data
        assert "total" in data
        assert "page" in data
        assert "limit" in data
    
    def test_update_notification_settings(self, setup_database, test_user, auth_headers):
        """Test updating notification settings."""
        settings_data = {
            "therapy_reminders": True,
            "task_notifications": False,
            "quiet_hours_enabled": True,
            "quiet_hours_start_hour": 22,
            "quiet_hours_end_hour": 8,
            "notification_frequency": "Normal"
        }
        
        response = client.put(
            "/api/v1/notifications/settings",
            json=settings_data,
            headers=auth_headers
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text}")
        assert response.status_code == 200
        data = response.json()
        assert data["therapy_reminders"] is True
        assert data["task_notifications"] is False

class TestEnhancedDashboard:
    """Test enhanced dashboard endpoint."""
    
    def test_dashboard_with_expandable_data(self, setup_database, test_user, auth_headers):
        """Test dashboard endpoint includes expandable card data."""
        response = client.get(
            "/api/v1/dashboard/daily-summary",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Check basic dashboard structure
        assert "user_profile" in data
        assert "greeting" in data
        assert "quick_stats" in data
        assert "today_tasks" in data
        assert "recent_notes" in data
        
        # Check new expandable card data
        assert "expandable_card_data" in data
        expandable_data = data["expandable_card_data"]
        assert "detailed_tasks" in expandable_data
        assert "upcoming_events" in expandable_data
        assert "recent_notes" in expandable_data
        assert "quick_actions" in expandable_data

class TestDatabaseOperations:
    """Test database CRUD operations."""
    
    def test_user_session_crud(self, test_db):
        """Test user session CRUD operations."""
        # Create user first
        user = crud.create_user(db=test_db, user_id="test_user_crud", email="<EMAIL>")
        
        # Create session
        session = crud.create_user_session(
            db=test_db,
            user_id=user.id,
            session_type="app_open",
            timestamp=datetime.now(timezone.utc)
        )
        
        assert session.user_id == user.id
        assert session.session_type == "app_open"
        
        # Get session data
        session_data = crud.get_user_session_data(db=test_db, user_id=user.id)
        assert session_data["total_sessions"] >= 1
    
    def test_notification_crud(self, test_db):
        """Test notification CRUD operations."""
        # Create user first
        user = crud.create_user(db=test_db, user_id="test_user_notif", email="<EMAIL>")
        
        # Create notification
        notification = crud.create_notification(
            db=test_db,
            user_id=user.id,
            title="Test Notification",
            body="Test body",
            notification_type="test",
            priority="normal",
            scheduled_time=datetime.now(timezone.utc) + timedelta(hours=1)
        )
        
        assert notification.user_id == user.id
        assert notification.title == "Test Notification"
        
        # Mark as interacted
        updated_notification = crud.mark_notification_interacted(db=test_db, notification_id=notification.id)
        assert updated_notification.is_interacted is True

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
