# Darvis AI Core

The intelligent agent core of the <PERSON><PERSON> backend, built with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>rap<PERSON>.

## Overview

This module provides the foundational AI capabilities for <PERSON><PERSON>, including:

- **PRP System**: Prompt Rule Proposals for defining AI behavior
- **LangGraph Orchestrator**: State-managed conversation flow
- **LLM Provider SDK**: Unified interface for multiple language models
- **Conversation Management**: Context-aware dialogue handling

## Architecture

### Components

1. **PRP Manager** (`prp_manager.py`)
   - Loads and parses structured prompt definitions
   - Supports system prompts and mode-specific behaviors
   - Caching for performance optimization

2. **Main Orchestrator** (`main_orchestrator.py`)
   - LangGraph-based conversation state management
   - Agent node for message processing
   - Integration with PRP system and LLM providers

3. **Prompt System** (`prompts/`)
   - `_system/`: Core system prompts and personas
   - `modes/`: Specialized AI modes (future expansion)

## Usage

### Basic Chat Interaction

```python
from main_orchestrator import darvis_orchestrator

response = darvis_orchestrator.process_message(
    user_message="Hello, how are you?",
    user_id="user_123",
    conversation_context={}
)
print(response)  # AI response string
```

### PRP Management

```python
from prp_manager import prp_manager

# Load system persona
persona = prp_manager.get_system_prp("_initial_persona.md")
print(persona["rules"])  # List of behavior rules
```

### LLM Provider Configuration

```python
from app.sdk.llm_provider_sdk import LLMProviderSDK

# Initialize with Groq
llm = LLMProviderSDK(provider="groq", model="llama3-8b-8192")

# Or with Gemini
llm = LLMProviderSDK(provider="gemini", model="gemini-pro")
```

## Configuration

### Environment Variables

- `GROQ_API_KEY`: For Groq LLM provider
- `GOOGLE_API_KEY`: For Google Gemini provider

### PRP Files

PRPs are structured markdown files with the following format:

```markdown
# PRP-XXX-001: Title

## Trigger
- When this PRP should be activated

## Rules
1. First behavior rule
2. Second behavior rule
3. Third behavior rule

## Evaluation
- Success criteria for this PRP
```

## Development

### Adding New LLM Providers

1. Add provider initialization in `LLMProviderSDK._initialize_client()`
2. Implement provider-specific initialization method
3. Update the provider selection logic

### Creating New PRPs

1. Create markdown file in appropriate directory
2. Follow the standard PRP format
3. Test with `prp_manager.load_prp()`

### Testing

Run the AI core tests:

```bash
python -m pytest tests/test_ai_core.py -v
```

## Future Enhancements

- Tool integration for external API calls
- Memory management for long-term conversations
- Multi-agent collaboration
- Custom user personas
- Advanced reasoning capabilities
