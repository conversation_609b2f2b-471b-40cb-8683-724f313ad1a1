from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
import redis
import os
from typing import Generator
import logging
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# # Database configuration

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL")
REDIS_URL = os.getenv("REDIS_URL")

# Create SQLAlchemy engine

# Create SQLAlchemy engine
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=300,    # Recycle connections every 5 minutes
    echo=False           # Set to True for SQL query logging in development
)

# Create SessionLocal class for database sessions
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Redis client
redis_client = None

def init_redis():
    """Initialize Redis connection"""
    global redis_client
    try:
        redis_client = redis.from_url(REDIS_URL, decode_responses=True)
        # Test the connection
        redis_client.ping()
        logger.info("Redis connection established successfully")
        return redis_client
    except Exception as e:
        logger.exception(f"Failed to connect to Redis: {e}")
        redis_client = None
        return None

def get_redis():
    """Get Redis client instance"""
    global redis_client
    if redis_client is None:
        init_redis()
    return redis_client

def get_db() -> Generator[Session, None, None]:
    """
    Dependency function to get database session.
    This will be used as a FastAPI dependency.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def enable_pgvector():
    """Enable pgvector extension in PostgreSQL"""
    try:
        with engine.connect() as connection:
            connection.execute(text('CREATE EXTENSION IF NOT EXISTS vector'))
            connection.commit()
            logger.info("pgvector extension enabled successfully")
    except Exception as e:
        logger.error(f"Failed to enable pgvector extension: {e}")
        # Don't raise exception as this might fail in development environments

def close_db_connections():
    """Close all database connections"""
    global redis_client
    try:
        if redis_client:
            redis_client.close()
            logger.info("Redis connection closed")
    except Exception as e:
        logger.exception(f"Error closing Redis connection: {e}")

    try:
        engine.dispose()
        logger.info("PostgreSQL connection pool disposed")
    except Exception as e:
        logger.exception(f"Error disposing PostgreSQL engine: {e}")