#!/usr/bin/env python3
"""
Complete Darvis database migration - creates ALL missing tables.
This preserves existing therapy data while adding all core Darvis tables.
"""

import os
import sys
import logging
from dotenv import load_dotenv

load_dotenv()

# Add the parent directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Direct database connection (your actual database)
from sqlalchemy import create_engine


# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL")

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL)


from app.db.models import Base

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_all_tables():
    """Create ALL tables defined in models."""
    try:
        logger.info("🚀 Creating ALL Darvis tables...")
        
        # This creates ALL tables defined in your models
        # It will skip tables that already exist (like therapy tables)
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ All Darvis tables created successfully!")
        
        # List what was created
        from sqlalchemy import inspect
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()
        
        logger.info(f"📊 Total tables now: {len(existing_tables)}")
        for table in sorted(existing_tables):
            logger.info(f"  ✅ {table}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating tables: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting complete Darvis migration...")
    
    if create_all_tables():
        logger.info("🎉 Complete migration successful!")
        logger.info("Your database now has all required Darvis tables.")
        logger.info("Therapy data is preserved and new tables are ready.")
    else:
        logger.error("💥 Migration failed!")