import psycopg2

conn = psycopg2.connect(
    host="*************",
    database="darvis-postgres", 
    user="postgres",
    password="qroinoruwob23u410841rqouUBUBUBo808",
    port="5432"
)

cur = conn.cursor()

print("📋 Checking therapy tables...")
therapy_tables = ['therapy_knowledge_chunks', 'therapy_sessions', 'therapy_progress']

for table in therapy_tables:
    cur.execute(f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table}');")
    exists = cur.fetchone()[0]
    status = '✅' if exists else '❌'
    print(f'{status} {table}')

print("\n📋 Checking all tables in database...")
cur.execute("SELECT table_name FROM information_schema.tables WHERE table_schema='public' ORDER BY table_name;")
all_tables = [row[0] for row in cur.fetchall()]
print(f"Total tables: {len(all_tables)}")
for table in all_tables:
    print(f"  • {table}")

conn.close()
print("\n🎯 Database verification complete!")