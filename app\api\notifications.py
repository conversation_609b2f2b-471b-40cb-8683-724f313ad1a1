"""
Notification Management API for Darvis.

This module provides endpoints for managing user notification preferences,
viewing notification history, and handling notification interactions.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, field_validator
from datetime import datetime, timezone, timedelta

from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/notifications", tags=["Notifications"])

# Pydantic schemas for request/response validation

# Notification creation and management schemas
class NotificationCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=255, description="Notification title")
    body: str = Field(..., min_length=1, max_length=1000, description="Notification body text")
    type: str = Field(..., description="Notification type (therapy_reminder, task_notification, etc.)")
    priority: str = Field(..., pattern="^(low|normal|high)$", description="Priority level")
    scheduled_time: datetime = Field(..., description="When to deliver the notification")
    navigation_route: Optional[str] = Field(None, max_length=200, description="Optional route to navigate to")
    navigation_data: Optional[Dict[str, Any]] = Field(None, description="Optional navigation data")

    @field_validator('type')
    @classmethod
    def validate_notification_type(cls, v):
        allowed_types = [
            'therapy_reminder', 'task_notification', 'daily_check_in',
            'weekly_report', 'system_update', 'contextual_ai', 'custom'
        ]
        if v not in allowed_types:
            raise ValueError(f'Invalid notification type. Must be one of: {", ".join(allowed_types)}')
        return v

    @field_validator('scheduled_time')
    @classmethod
    def validate_scheduled_time(cls, v):
        # Ensure we're working with timezone-aware datetimes
        current_time = datetime.now(timezone.utc)

        # If the input datetime is naive, assume it's UTC
        if v.tzinfo is None:
            v = v.replace(tzinfo=timezone.utc)

        # Convert to UTC for comparison
        if v.tzinfo != timezone.utc:
            v = v.astimezone(timezone.utc)

        if v <= current_time:
            raise ValueError('Scheduled time must be in the future')

        # Don't allow scheduling more than 1 year in advance
        max_future = current_time + timedelta(days=365)
        if v > max_future:
            raise ValueError('Cannot schedule notifications more than 1 year in advance')

        return v

    @field_validator('navigation_data')
    @classmethod
    def validate_navigation_data(cls, v):
        if v is not None:
            # Ensure navigation data is not too large
            import json
            try:
                json_str = json.dumps(v)
                if len(json_str) > 2000:  # 2KB limit
                    raise ValueError('Navigation data too large (max 2KB)')
            except (TypeError, ValueError) as e:
                raise ValueError(f'Invalid navigation data: {str(e)}')
        return v

class NotificationResponse(BaseModel):
    success: bool
    notification_id: str
    scheduled_time: str

class NotificationItem(BaseModel):
    id: str
    title: str
    body: str
    type: str
    priority: str
    scheduled_time: str
    is_read: bool
    created_at: str

    class Config:
        from_attributes = True

class NotificationListResponse(BaseModel):
    notifications: List[NotificationItem]
    total: int
    page: int
    limit: int

class NotificationInteractionResponse(BaseModel):
    success: bool
    interaction_recorded: bool

class NotificationUpdateRequest(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    body: Optional[str] = Field(None, min_length=1, max_length=1000)
    scheduled_time: Optional[datetime] = None
    navigation_route: Optional[str] = None
    navigation_data: Optional[Dict[str, Any]] = None
    priority: Optional[str] = Field(None, pattern="^(low|normal|high)$")

class NotificationDeleteResponse(BaseModel):
    success: bool
    deleted: bool

class UnreadCountResponse(BaseModel):
    unread_count: int

class MarkReadResponse(BaseModel):
    success: bool
    marked_read: bool

class DeliveryStatsResponse(BaseModel):
    total_notifications: int
    delivered_notifications: int
    failed_notifications: int
    pending_notifications: int
    delivery_rate: float
    failure_rate: float
    average_delivery_delay_seconds: Optional[float]
    period_days: int

class RetryNotificationResponse(BaseModel):
    success: bool
    retry_scheduled: bool

# Updated notification settings schemas (matching frontend requirements)
class NotificationSettingsResponse(BaseModel):
    therapy_reminders: bool
    task_notifications: bool
    daily_check_ins: bool
    weekly_reports: bool
    system_updates: bool
    contextual_ai: bool
    quiet_hours_enabled: bool
    quiet_hours_start_hour: int
    quiet_hours_start_minute: int
    quiet_hours_end_hour: int
    quiet_hours_end_minute: int
    notification_frequency: str
    sound_enabled: bool
    vibration_enabled: bool
    badge_count_enabled: bool
    updated_at: str

    class Config:
        from_attributes = True

class NotificationSettingsUpdate(BaseModel):
    therapy_reminders: Optional[bool] = None
    task_notifications: Optional[bool] = None
    daily_check_ins: Optional[bool] = None
    weekly_reports: Optional[bool] = None
    system_updates: Optional[bool] = None
    contextual_ai: Optional[bool] = None
    quiet_hours_enabled: Optional[bool] = None
    quiet_hours_start_hour: Optional[int] = Field(None, ge=0, le=23)
    quiet_hours_start_minute: Optional[int] = Field(None, ge=0, le=59)
    quiet_hours_end_hour: Optional[int] = Field(None, ge=0, le=23)
    quiet_hours_end_minute: Optional[int] = Field(None, ge=0, le=59)
    notification_frequency: Optional[str] = Field(None, pattern="^(Low|Normal|High)$")
    sound_enabled: Optional[bool] = None
    vibration_enabled: Optional[bool] = None
    badge_count_enabled: Optional[bool] = None

# Legacy schemas for backward compatibility
class NotificationPreferenceResponse(BaseModel):
    id: str
    task_reminders_enabled: bool
    productivity_insights_enabled: bool
    conversation_continuity_enabled: bool
    inbox_updates_enabled: bool
    social_context_enabled: bool
    quiet_hours_start: int
    quiet_hours_end: int
    max_notifications_per_day: int

    class Config:
        from_attributes = True

class NotificationPreferenceUpdate(BaseModel):
    task_reminders_enabled: Optional[bool] = None
    productivity_insights_enabled: Optional[bool] = None
    conversation_continuity_enabled: Optional[bool] = None
    inbox_updates_enabled: Optional[bool] = None
    social_context_enabled: Optional[bool] = None
    quiet_hours_start: Optional[int] = Field(None, ge=0, le=23)
    quiet_hours_end: Optional[int] = Field(None, ge=0, le=23)
    max_notifications_per_day: Optional[int] = Field(None, ge=0, le=50)

class FCMTokenUpdate(BaseModel):
    fcm_token: str = Field(..., min_length=50, max_length=500)


# New notification management endpoints matching frontend requirements

@router.post("/", response_model=NotificationResponse, status_code=status.HTTP_201_CREATED)
def create_notification(
    notification_data: NotificationCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new scheduled notification.

    This endpoint allows creating notifications for therapy reminders, task notifications,
    daily check-ins, and other notification types.

    **Authentication Required**: Yes (Firebase ID token)

    **Request Body**:
    - **title**: Notification title
    - **body**: Notification body text
    - **type**: Notification type (therapy_reminder, task_notification, etc.)
    - **priority**: Priority level (low, normal, high)
    - **scheduled_time**: When to deliver the notification
    - **navigation_route**: Optional route to navigate to when tapped
    - **navigation_data**: Optional data for navigation

    **Returns**:
    - **201 Created**: Notification successfully created
    - **400 Bad Request**: Invalid notification data
    - **401 Unauthorized**: Invalid or expired authentication token
    - **429 Too Many Requests**: Rate limit exceeded
    """
    user_id = current_user["uid"]

    try:
        # Check rate limiting - max 50 notifications per day per user
        today = datetime.now(timezone.utc).date()
        today_notifications = crud.get_user_notifications(db=db, user_id=user_id, page=1, limit=100)

        today_count = len([
            n for n in today_notifications["notifications"]
            if n.created_at.date() == today
        ])

        if today_count >= 50:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Daily notification limit exceeded (50 notifications per day)"
            )

        # Validate user exists and has valid preferences
        user = crud.get_user_by_id(db=db, user_id=user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        # Check if notification type is enabled for user
        preferences = crud.get_or_create_notification_preferences(db=db, user_id=user_id)
        if not _is_notification_type_enabled_api(preferences, notification_data.type):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Notification type '{notification_data.type}' is disabled for this user"
            )

        # Create the notification
        notification = crud.create_notification(
            db=db,
            user_id=user_id,
            title=notification_data.title,
            body=notification_data.body,
            notification_type=notification_data.type,
            priority=notification_data.priority,
            scheduled_time=notification_data.scheduled_time,
            navigation_route=notification_data.navigation_route,
            navigation_data=notification_data.navigation_data
        )

        logger.info(f"Created notification {notification.id} for user {user_id}")

        return NotificationResponse(
            success=True,
            notification_id=notification.id,
            scheduled_time=notification.scheduled_time.isoformat()
        )

    except HTTPException:
        raise
    except ValueError as e:
        logger.warning(f"Validation error creating notification for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create notification for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create notification due to server error"
        )

def _is_notification_type_enabled_api(preferences, notification_type: str) -> bool:
    """Check if a notification type is enabled in user preferences (API version)."""
    type_mapping = {
        "therapy_reminder": preferences.therapy_reminders,
        "task_notification": preferences.task_notifications,
        "daily_check_in": preferences.daily_check_ins,
        "weekly_report": preferences.weekly_reports,
        "system_update": preferences.system_updates,
        "contextual_ai": preferences.contextual_ai
    }
    return type_mapping.get(notification_type, True)  # Default to enabled for custom types


@router.get("/", response_model=NotificationListResponse)
def get_notifications(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Number of notifications per page"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get paginated list of user notifications.

    This endpoint returns a paginated list of notifications for the authenticated user,
    ordered by creation date (newest first).

    **Authentication Required**: Yes (Firebase ID token)

    **Query Parameters**:
    - **page**: Page number (default: 1)
    - **limit**: Number of notifications per page (default: 20, max: 100)

    **Returns**:
    - **200 OK**: Notifications successfully retrieved
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        result = crud.get_user_notifications(db=db, user_id=user_id, page=page, limit=limit)

        # Convert notifications to response format
        notification_items = []
        for notification in result["notifications"]:
            notification_items.append(NotificationItem(
                id=notification.id,
                title=notification.title,
                body=notification.body,
                type=notification.type,
                priority=notification.priority,
                scheduled_time=notification.scheduled_time.isoformat(),
                is_read=notification.is_read,
                created_at=notification.created_at.isoformat()
            ))

        return NotificationListResponse(
            notifications=notification_items,
            total=result["total"],
            page=result["page"],
            limit=result["limit"]
        )

    except Exception as e:
        logger.error(f"Failed to get notifications for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve notifications: {str(e)}"
        )


@router.post("/{notification_id}/interact", response_model=NotificationInteractionResponse)
def interact_with_notification(
    notification_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Record interaction with a notification.

    This endpoint marks a notification as interacted when the user taps on it,
    enabling analytics and engagement tracking.

    **Authentication Required**: Yes (Firebase ID token)

    **Path Parameters**:
    - **notification_id**: ID of the notification to mark as interacted

    **Returns**:
    - **200 OK**: Interaction successfully recorded
    - **404 Not Found**: Notification not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        # First verify the notification belongs to the user
        notification = crud.get_notification_by_id(db=db, notification_id=notification_id, user_id=user_id)

        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )

        # Mark as interacted
        updated_notification = crud.mark_notification_interacted(db=db, notification_id=notification_id)

        if not updated_notification:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to record interaction"
            )

        return NotificationInteractionResponse(
            success=True,
            interaction_recorded=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to record notification interaction {notification_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to record interaction: {str(e)}"
        )


@router.put("/settings", response_model=NotificationSettingsResponse)
def update_notification_settings(
    settings_update: NotificationSettingsUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update notification settings for the authenticated user.

    This endpoint allows users to customize their notification preferences including
    notification types, quiet hours, and notification behavior settings.

    **Authentication Required**: Yes (Firebase ID token)

    **Request Body**: Any combination of notification settings to update

    **Returns**:
    - **200 OK**: Settings successfully updated
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    # Convert Pydantic model to dict, excluding None values
    update_data = {k: v for k, v in settings_update.model_dump().items() if v is not None}

    try:
        # Get or create preferences first, then update them
        crud.get_or_create_notification_preferences(db, user_id)

        # Update preferences
        updated_preferences = crud.update_notification_preferences(db, user_id, update_data)

        if not updated_preferences:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update notification settings"
            )

        return NotificationSettingsResponse(
            therapy_reminders=updated_preferences.therapy_reminders,
            task_notifications=updated_preferences.task_notifications,
            daily_check_ins=updated_preferences.daily_check_ins,
            weekly_reports=updated_preferences.weekly_reports,
            system_updates=updated_preferences.system_updates,
            contextual_ai=updated_preferences.contextual_ai,
            quiet_hours_enabled=updated_preferences.quiet_hours_enabled,
            quiet_hours_start_hour=updated_preferences.quiet_hours_start_hour,
            quiet_hours_start_minute=updated_preferences.quiet_hours_start_minute,
            quiet_hours_end_hour=updated_preferences.quiet_hours_end_hour,
            quiet_hours_end_minute=updated_preferences.quiet_hours_end_minute,
            notification_frequency=updated_preferences.notification_frequency,
            sound_enabled=updated_preferences.sound_enabled,
            vibration_enabled=updated_preferences.vibration_enabled,
            badge_count_enabled=updated_preferences.badge_count_enabled,
            updated_at=updated_preferences.updated_at.isoformat()
        )

    except Exception as e:
        logger.error(f"Failed to update notification settings for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update settings: {str(e)}"
        )


@router.get("/{notification_id}", response_model=NotificationItem)
def get_notification(
    notification_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific notification by ID.

    **Authentication Required**: Yes (Firebase ID token)

    **Path Parameters**:
    - **notification_id**: ID of the notification to retrieve

    **Returns**:
    - **200 OK**: Notification successfully retrieved
    - **404 Not Found**: Notification not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        notification = crud.get_notification_by_id(db=db, notification_id=notification_id, user_id=user_id)

        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )

        return NotificationItem(
            id=notification.id,
            title=notification.title,
            body=notification.body,
            type=notification.type,
            priority=notification.priority,
            scheduled_time=notification.scheduled_time.isoformat(),
            is_read=notification.is_read,
            created_at=notification.created_at.isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get notification {notification_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve notification: {str(e)}"
        )


@router.put("/{notification_id}", response_model=NotificationItem)
def update_notification(
    notification_id: str,
    update_data: NotificationUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a notification's details.

    **Authentication Required**: Yes (Firebase ID token)

    **Path Parameters**:
    - **notification_id**: ID of the notification to update

    **Request Body**: Any combination of notification fields to update

    **Returns**:
    - **200 OK**: Notification successfully updated
    - **404 Not Found**: Notification not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    # Convert Pydantic model to dict, excluding None values
    update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}

    try:
        updated_notification = crud.update_notification(
            db=db,
            notification_id=notification_id,
            user_id=user_id,
            update_data=update_dict
        )

        if not updated_notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found or cannot be updated"
            )

        return NotificationItem(
            id=updated_notification.id,
            title=updated_notification.title,
            body=updated_notification.body,
            type=updated_notification.type,
            priority=updated_notification.priority,
            scheduled_time=updated_notification.scheduled_time.isoformat(),
            is_read=updated_notification.is_read,
            created_at=updated_notification.created_at.isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update notification {notification_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update notification: {str(e)}"
        )


@router.delete("/{notification_id}", response_model=NotificationDeleteResponse)
def delete_notification(
    notification_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a notification (only if not yet delivered).

    **Authentication Required**: Yes (Firebase ID token)

    **Path Parameters**:
    - **notification_id**: ID of the notification to delete

    **Returns**:
    - **200 OK**: Notification successfully deleted
    - **404 Not Found**: Notification not found or already delivered
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        deleted = crud.delete_notification(db=db, notification_id=notification_id, user_id=user_id)

        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found or cannot be deleted (already delivered)"
            )

        return NotificationDeleteResponse(success=True, deleted=True)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete notification {notification_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete notification: {str(e)}"
        )


@router.post("/{notification_id}/read", response_model=MarkReadResponse)
def mark_notification_read(
    notification_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Mark a notification as read.

    **Authentication Required**: Yes (Firebase ID token)

    **Path Parameters**:
    - **notification_id**: ID of the notification to mark as read

    **Returns**:
    - **200 OK**: Notification successfully marked as read
    - **404 Not Found**: Notification not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        notification = crud.mark_notification_read(db=db, notification_id=notification_id, user_id=user_id)

        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )

        return MarkReadResponse(success=True, marked_read=True)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to mark notification as read {notification_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark notification as read: {str(e)}"
        )


@router.get("/unread/count", response_model=UnreadCountResponse)
def get_unread_count(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get count of unread notifications.

    **Authentication Required**: Yes (Firebase ID token)

    **Returns**:
    - **200 OK**: Unread count successfully retrieved
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        unread_count = crud.get_unread_notification_count(db=db, user_id=user_id)
        return UnreadCountResponse(unread_count=unread_count)

    except Exception as e:
        logger.error(f"Failed to get unread count for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get unread count: {str(e)}"
        )


@router.post("/read-all", response_model=MarkReadResponse)
def mark_all_read(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Mark all notifications as read.

    **Authentication Required**: Yes (Firebase ID token)

    **Returns**:
    - **200 OK**: All notifications successfully marked as read
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        updated_count = crud.mark_all_notifications_read(db=db, user_id=user_id)
        return MarkReadResponse(success=True, marked_read=updated_count > 0)

    except Exception as e:
        logger.error(f"Failed to mark all notifications as read for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark all notifications as read: {str(e)}"
        )


@router.get("/delivery-stats", response_model=DeliveryStatsResponse)
def get_delivery_stats(
    days: int = Query(7, ge=1, le=30, description="Number of days to analyze"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get notification delivery statistics for the authenticated user.

    **Authentication Required**: Yes (Firebase ID token)

    **Query Parameters**:
    - **days**: Number of days to analyze (default: 7, max: 30)

    **Returns**:
    - **200 OK**: Delivery statistics successfully retrieved
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        stats = crud.get_notification_delivery_stats(db=db, user_id=user_id, days=days)

        return DeliveryStatsResponse(
            total_notifications=stats["total_notifications"],
            delivered_notifications=stats["delivered_notifications"],
            failed_notifications=stats["failed_notifications"],
            pending_notifications=stats["pending_notifications"],
            delivery_rate=stats["delivery_rate"],
            failure_rate=stats["failure_rate"],
            average_delivery_delay_seconds=stats["average_delivery_delay_seconds"],
            period_days=stats["period_days"]
        )

    except Exception as e:
        logger.error(f"Failed to get delivery stats for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve delivery statistics: {str(e)}"
        )


@router.post("/{notification_id}/retry", response_model=RetryNotificationResponse)
def retry_failed_notification(
    notification_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retry a failed notification.

    **Authentication Required**: Yes (Firebase ID token)

    **Path Parameters**:
    - **notification_id**: ID of the failed notification to retry

    **Returns**:
    - **200 OK**: Notification retry successfully scheduled
    - **404 Not Found**: Notification not found or not failed
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    try:
        # Verify the notification belongs to the user and is failed
        notification = crud.get_notification_by_id(db=db, notification_id=notification_id, user_id=user_id)

        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )

        if not notification.is_failed:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Notification is not in failed state"
            )

        # Reset the notification for retry
        reset_notification = crud.reset_failed_notification(db=db, notification_id=notification_id)

        if reset_notification:
            logger.info(f"Reset failed notification {notification_id} for retry")
            return RetryNotificationResponse(success=True, retry_scheduled=True)
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to reset notification for retry"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to retry notification {notification_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retry notification: {str(e)}"
        )


class NotificationHistoryResponse(BaseModel):
    id: str
    notification_type: str
    title: str
    content: str
    sent_at: str
    clicked_at: Optional[str] = None
    dismissed_at: Optional[str] = None
    relevance_score: float
    
    class Config:
        from_attributes = True

class NotificationHistoryListResponse(BaseModel):
    notifications: List[NotificationHistoryResponse]
    total: int

class NotificationInteraction(BaseModel):
    notification_id: str
    interaction_type: str = Field(..., pattern="^(clicked|dismissed)$")

class TestNotificationRequest(BaseModel):
    title: str = Field(..., min_length=1, max_length=100)
    body: str = Field(..., min_length=1, max_length=500)

@router.get("/preferences", response_model=NotificationPreferenceResponse)
def get_notification_preferences(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get the authenticated user's notification preferences.
    
    Returns the user's current notification settings including enabled notification types,
    quiet hours, and daily notification limits. Creates default preferences if none exist.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Returns**:
    - **200 OK**: Notification preferences successfully retrieved
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    preferences = crud.get_or_create_notification_preferences(db, user_id)
    return preferences

@router.put("/preferences", response_model=NotificationPreferenceResponse)
def update_notification_preferences(
    preferences_update: NotificationPreferenceUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update the authenticated user's notification preferences.
    
    Allows users to customize their notification experience by enabling/disabling
    specific notification types, setting quiet hours, and controlling frequency.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Request Body**: Any combination of preference fields to update
    
    **Returns**:
    - **200 OK**: Preferences successfully updated
    - **400 Bad Request**: Invalid preference values
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    # Convert Pydantic model to dict, excluding unset fields
    update_data = preferences_update.model_dump(exclude_unset=True)
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No fields to update"
        )
    
    # Get or create preferences first, then update them
    crud.get_or_create_notification_preferences(db, user_id)

    # Update preferences
    updated_preferences = crud.update_notification_preferences(db, user_id, update_data)
    if not updated_preferences:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update preferences"
        )
    
    logger.info(f"Updated notification preferences for user: {user_id}")
    return updated_preferences

@router.put("/fcm-token")
def update_fcm_token(
    token_update: FCMTokenUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update the user's FCM token for push notifications.
    
    This endpoint should be called whenever the client app receives a new FCM token
    to ensure push notifications can be delivered successfully.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Request Body**:
    - **fcm_token**: Firebase Cloud Messaging registration token
    
    **Returns**:
    - **200 OK**: FCM token successfully updated
    - **400 Bad Request**: Invalid token format
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    updated_user = crud.update_user_fcm_token(db, user_id, token_update.fcm_token)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    logger.info(f"Updated FCM token for user: {user_id}")
    return {"message": "FCM token updated successfully"}

@router.get("/history", response_model=NotificationHistoryListResponse)
def get_notification_history(
    skip: int = Query(0, ge=0, description="Number of notifications to skip"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of notifications to return"),
    notification_type: Optional[str] = Query(None, description="Filter by notification type"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get the authenticated user's notification history with pagination.
    
    Returns a paginated list of notifications sent to the user, including
    interaction data (clicks, dismissals) and relevance scores.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Query Parameters**:
    - **skip**: Number of notifications to skip (default: 0)
    - **limit**: Maximum notifications to return (default: 50, max: 100)
    - **notification_type**: Filter by specific notification type (optional)
    
    **Returns**:
    - **200 OK**: Notification history successfully retrieved
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    notifications = crud.get_notification_history(
        db=db,
        user_id=user_id,
        skip=skip,
        limit=limit,
        notification_type=notification_type
    )
    
    # Convert datetime objects to ISO strings for JSON serialization
    for notification in notifications:
        notification.sent_at = notification.sent_at.isoformat() if notification.sent_at else None
        notification.clicked_at = notification.clicked_at.isoformat() if notification.clicked_at else None
        notification.dismissed_at = notification.dismissed_at.isoformat() if notification.dismissed_at else None
    
    return NotificationHistoryListResponse(
        notifications=notifications,
        total=len(notifications)
    )

@router.post("/interaction")
def record_notification_interaction(
    interaction: NotificationInteraction,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Record user interaction with a notification (clicked or dismissed).
    
    This endpoint should be called when users interact with notifications
    to track engagement and improve notification relevance.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Request Body**:
    - **notification_id**: ID of the notification that was interacted with
    - **interaction_type**: Type of interaction ("clicked" or "dismissed")
    
    **Returns**:
    - **200 OK**: Interaction successfully recorded
    - **404 Not Found**: Notification not found
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]

    # First verify the notification belongs to the user
    notification = crud.get_notification_by_id(
        db=db,
        notification_id=interaction.notification_id,
        user_id=user_id
    )

    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )

    # Record the interaction
    updated_notification = crud.update_notification_interaction(
        db=db,
        notification_id=interaction.notification_id,
        interaction_type=interaction.interaction_type
    )

    if not updated_notification:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record interaction"
        )
    
    logger.info(f"Recorded {interaction.interaction_type} for notification: {interaction.notification_id}")
    return {"message": f"Interaction '{interaction.interaction_type}' recorded successfully"}

@router.get("/analytics")
def get_notification_analytics(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get notification analytics for the authenticated user.
    
    Provides insights into notification engagement including click rates,
    dismiss rates, and performance by notification type.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Query Parameters**:
    - **days**: Number of days to analyze (default: 30, max: 365)
    
    **Returns**:
    - **200 OK**: Analytics data successfully retrieved
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    analytics = crud.get_notification_analytics(db, user_id, days)
    return analytics

@router.post("/test")
def send_test_notification(
    test_request: TestNotificationRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Send a test notification to the authenticated user.
    
    Useful for testing notification delivery and debugging FCM token issues.
    Only available in development environments.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Request Body**:
    - **title**: Test notification title
    - **body**: Test notification body
    
    **Returns**:
    - **200 OK**: Test notification sent successfully
    - **400 Bad Request**: No FCM token or delivery failed
    - **401 Unauthorized**: Invalid or expired authentication token
    """
    user_id = current_user["uid"]
    
    # Get user and FCM token
    user = crud.get_user_by_id(db, user_id)
    if not user or not user.fcm_token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No FCM token found. Please update your FCM token first."
        )
    
    # Send test notification
    from app.sdk.notification_delivery_sdk import notification_delivery_sdk
    
    result = notification_delivery_sdk.send_push_notification(
        fcm_token=user.fcm_token,
        title=test_request.title,
        body=test_request.body,
        data={"type": "test", "timestamp": str(datetime.now(timezone.utc))}
    )
    
    if not result.get("success"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to send test notification: {result.get('error')}"
        )
    
    logger.info(f"Test notification sent to user: {user_id}")
    return {"message": "Test notification sent successfully", "result": result}
