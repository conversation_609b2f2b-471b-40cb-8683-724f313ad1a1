# 🚀 **Simple Update Guide for Paro Bible Bot**

*Keep this handy - follow these steps every time you update your code!*

---

## 📋 **Before You Start ANY Update**

### **🤔 Ask Yourself:**
- [ ] Did I change any database tables or columns?
- [ ] Did I add new features that need database changes?
- [ ] Did I modify files in `src/database/` folder?

**If YES to any:** You need a **Database Migration** (see Section 3)  
**If NO:** You can do a **Simple Update** (see Section 2)

---

## 🔍 **Section 1: Pre-Update Checklist**

### **✅ Always Do This First:**

**1. Save Your Code Changes:**
```bash
git add .
git commit -m "Describe what you changed"
git push
```

**2. Backup User Data (CRITICAL!):**
```bash
# This saves all user data with today's date
docker exec your-postgres-container pg_dump -U username database_name > backup_$(date +%Y_%m_%d_%H_%M).sql
```
*Replace `your-postgres-container`, `username`, and `database_name` with your actual values*

**3. Check What's Running:**
```bash
docker compose ps
```

---

## 🔄 **Section 2: Simple Code Update (No Database Changes)**

*Use this when you only changed app logic, not database structure*

### **Steps:**
```bash
# 1. Stop the app
docker compose down

# 2. Get new code
docker compose pull

# 3. Start with existing data
docker compose up -d

# 4. Check everything works
docker compose logs -f
```

### **✅ Success Signs:**
- No error messages in logs
- App responds to WhatsApp messages
- Users don't lose their data

### **🚨 If Something Breaks:**
```bash
# Stop the broken version
docker compose down

# Restore your backup
docker exec -i your-postgres-container psql -U username database_name < backup_YYYY_MM_DD_HH_MM.sql

# Go back to previous code version
git checkout previous-working-commit
docker compose up -d
```

---

## 🗄️ **Section 3: Database Schema Update (Advanced)**

*Use this when you added/changed database tables or columns*

### **🚨 DANGER ZONE - Extra Care Needed!**

### **Steps:**

**1. Create Migration File:**
```sql
-- migration_YYYY_MM_DD.sql
-- Example: Adding a new column
ALTER TABLE users ADD COLUMN new_feature TEXT DEFAULT '';

-- Example: Adding a new table
CREATE TABLE IF NOT EXISTS new_table (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    data TEXT
);
```

**2. Test Migration on Backup First:**
```bash
# Make a copy of your backup to test on
cp backup_2025_01_26.sql test_backup.sql

# Test the migration
docker exec -i postgres-container psql -U username test_db < test_backup.sql
docker exec postgres-container psql -U username test_db -f migration_2025_01_26.sql
```

**3. If Test Works, Apply to Real Database:**
```bash
# Apply migration to real database
docker exec postgres-container psql -U username database_name -f migration_2025_01_26.sql

# Then do normal update
docker compose down
docker compose pull
docker compose up -d
```

**4. Verify Everything:**
```bash
# Check logs for errors
docker compose logs -f

# Test with a real WhatsApp message
# Make sure existing users still work
```

---

## 🆘 **Section 4: Emergency Recovery**

### **If Everything Breaks:**

**1. Stay Calm!**

**2. Stop the Broken App:**
```bash
docker compose down
```

**3. Restore Your Backup:**
```bash
# Put back the old data
docker exec -i postgres-container psql -U username database_name < backup_YYYY_MM_DD.sql
```

**4. Go Back to Working Code:**
```bash
# Find your last working version
git log --oneline

# Go back to it
git checkout WORKING_COMMIT_HASH
docker compose up -d
```

**5. Figure Out What Went Wrong:**
- Check the logs: `docker compose logs`
- Test your changes on a copy first
- Ask for help if needed

---

## 📝 **Section 5: Update Log Template**

*Keep track of your updates - copy this for each update:*

```
UPDATE LOG - [DATE]
==================

What I Changed:
- [ ] Code changes only
- [ ] Database schema changes
- [ ] New features added
- [ ] Bug fixes

Backup Created: backup_YYYY_MM_DD_HH_MM.sql

Migration Files Used:
- migration_YYYY_MM_DD.sql (if any)

Update Process:
- [ ] Code committed to git
- [ ] Database backed up
- [ ] Migration tested (if needed)
- [ ] Migration applied (if needed)
- [ ] Code updated
- [ ] Logs checked
- [ ] App tested

Result:
- [ ] ✅ Success - everything working
- [ ] ❌ Failed - rolled back to: [backup/commit]

Notes:
[Any issues or observations]
```

---

## 🎯 **Section 6: Quick Reference**

### **File Locations to Remember:**
- **Backups:** `backup_YYYY_MM_DD_HH_MM.sql`
- **Migrations:** `migration_YYYY_MM_DD.sql`
- **Database Schema:** `src/database/schema.ts`
- **Docker Config:** `docker-compose.yml`

### **Key Commands:**
```bash
# Backup
docker exec postgres-container pg_dump -U user db > backup_$(date +%Y_%m_%d_%H_%M).sql

# Simple Update
docker compose down && docker compose pull && docker compose up -d

# Check Status
docker compose ps && docker compose logs -f

# Emergency Stop
docker compose down
```

### **🚨 Golden Rules:**
1. **ALWAYS backup before updating**
2. **NEVER delete user records manually**
3. **Test database changes on copies first**
4. **Keep multiple backups (don't overwrite)**
5. **When in doubt, ask for help**

---

## 📞 **When to Ask for Help:**

- Database migration fails
- App won't start after update
- Users report losing data
- Error messages you don't understand
- Backup/restore doesn't work

**Remember: It's better to ask than to break user data!**

---

*Save this document and follow it every time. Your users' spiritual journey data is precious - treat it with care! 🙏*




