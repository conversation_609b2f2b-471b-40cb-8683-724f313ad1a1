"""
Lesson Management API for Darvis.

This module provides CRUD endpoints for managing personal lessons learned,
enabling users to capture and reflect on their experiences and decisions.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional

from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import (
    LessonCreate,
    LessonUpdate,
    LessonResponse,
    LessonListResponse
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/lessons", tags=["Lessons"])

@router.post("/", response_model=LessonResponse, status_code=status.HTTP_201_CREATED)
async def create_lesson(
    lesson: LessonCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new lesson learned for the authenticated user.
    
    This endpoint allows users to capture valuable lessons from their experiences,
    including the scenario, the choice they made, and the lesson learned. This creates
    a personal knowledge base for reflection and growth.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Request Body**:
    - **scenario**: The learning scenario or situation (1-2000 characters) - **Required**
    - **choice**: The choice or decision made (1-1000 characters) - **Required**
    - **lesson**: The lesson learned from this experience (1-2000 characters) - **Required**
    
    **Returns**:
    - **201 Created**: Lesson successfully created with full lesson details
    - **400 Bad Request**: Invalid input data (content too long, missing fields, etc.)
    - **401 Unauthorized**: Invalid or expired authentication token
    
    **Features**:
    - Automatic lesson ID generation
    - User ownership assignment
    - Timestamp tracking (created_at, updated_at)
    - Personal knowledge base building
    - Reflection and growth tracking
    """
    try:
        user_id = current_user["uid"]
        
        # Create lesson with all provided information
        db_lesson = crud.create_lesson(
            db=db,
            user_id=user_id,
            scenario=lesson.scenario,
            choice=lesson.choice,
            lesson=lesson.lesson
        )
        
        logger.info(f"Created lesson {db_lesson.id} for user {user_id}")
        return db_lesson
        
    except Exception as e:
        logger.error(f"Error creating lesson for user {current_user.get('uid')}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create lesson"
        )

@router.get("/", response_model=LessonListResponse)
async def get_lessons(
    skip: int = Query(0, ge=0, description="Number of lessons to skip"),
    limit: int = Query(100, ge=1, le=100, description="Number of lessons to return"),
    search: Optional[str] = Query(None, description="Search in scenario, choice, and lesson content"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retrieve lessons for the authenticated user with search and pagination capabilities.
    
    This endpoint provides access to the user's personal knowledge base of lessons learned,
    with powerful search functionality across all lesson content and intelligent pagination.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Query Parameters**:
    - **skip**: Number of lessons to skip for pagination (default: 0)
    - **limit**: Maximum lessons to return (1-100, default: 100)
    - **search**: Search query across scenario, choice, and lesson content
    
    **Returns**:
    - **200 OK**: List of lessons with total count
    - **401 Unauthorized**: Invalid or expired authentication token
    
    **Response includes**:
    - Array of lesson objects with full details
    - Total count of matching lessons (for pagination)
    - Lessons ordered by creation date (most recent first)
    """
    try:
        user_id = current_user["uid"]
        
        # Get lessons with search and pagination
        lessons = crud.get_user_lessons(db, user_id, skip, limit, search)
        total = crud.count_user_lessons(db, user_id)
        
        return LessonListResponse(lessons=lessons, total=total)
        
    except Exception as e:
        logger.error(f"Error retrieving lessons for user {current_user.get('uid')}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve lessons"
        )

@router.get("/{lesson_id}", response_model=LessonResponse)
async def get_lesson(
    lesson_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Retrieve a specific lesson by ID.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Returns**:
    - **200 OK**: Lesson details
    - **404 Not Found**: Lesson not found or doesn't belong to user
    - **401 Unauthorized**: Invalid authentication token
    """
    user_id = current_user["uid"]
    db_lesson = crud.get_lesson_by_id(db=db, lesson_id=lesson_id, user_id=user_id)
    
    if db_lesson is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lesson not found"
        )
    
    return db_lesson

@router.put("/{lesson_id}", response_model=LessonResponse)
async def update_lesson(
    lesson_id: str,
    lesson_update: LessonUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a specific lesson's content.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Returns**:
    - **200 OK**: Updated lesson details
    - **404 Not Found**: Lesson not found or doesn't belong to user
    - **400 Bad Request**: No fields to update
    - **401 Unauthorized**: Invalid authentication token
    """
    user_id = current_user["uid"]
    
    # Convert Pydantic model to dict, excluding unset fields
    update_data = lesson_update.model_dump(exclude_unset=True)
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No fields to update"
        )
    
    db_lesson = crud.update_lesson(db=db, lesson_id=lesson_id, user_id=user_id, lesson_data=update_data)
    if db_lesson is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lesson not found"
        )
    
    logger.info(f"Updated lesson {lesson_id} for user {user_id}")
    return db_lesson

@router.delete("/{lesson_id}")
async def delete_lesson(
    lesson_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a specific lesson.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Returns**:
    - **200 OK**: Lesson successfully deleted
    - **404 Not Found**: Lesson not found or doesn't belong to user
    - **401 Unauthorized**: Invalid authentication token
    """
    user_id = current_user["uid"]
    
    success = crud.delete_lesson(db=db, lesson_id=lesson_id, user_id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lesson not found"
        )
    
    logger.info(f"Deleted lesson {lesson_id} for user {user_id}")
    return {"message": "Lesson deleted successfully"}
