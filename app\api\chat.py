from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
import sys
import os
import logging
import json

# Add the darvis-ai-core directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "darvis-ai-core"))

from app.api.auth import get_current_user

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/chat", tags=["Chat"])

class ChatMessage(BaseModel):
    """Request model for chat messages."""
    message: str = Field(..., min_length=1, max_length=5000, description="User's message to the AI")
    conversation_id: Optional[str] = Field(None, description="Optional conversation ID for memory continuity")
    model_name: Optional[str] = Field(None, description="Optional model name to override default (e.g., llama3-8b-8192)")
    stream: Optional[bool] = Field(False, description="Whether to stream the response")
    context: Optional[Dict[str, Any]] = Field(None, description="Optional conversation context")
    therapy_mode: Optional[bool] = Field(False, description="Enable therapy mode with knowledge retrieval")
    session_focus: Optional[str] = Field(None, description="Therapy session focus area (anxiety, depression, etc.)")
    emotional_state: Optional[Dict[str, Any]] = Field(None, description="Current emotional state data")

    class Config:
        json_schema_extra = {
            "example": {
                "message": "Can you help me plan my day? I have a meeting at 2 PM and need to finish a report.",
                "conversation_id": "conv_abc123",
                "model_name": "llama3-70b-8192",
                "stream": False,
                "context": {"timezone": "EST", "current_task": "planning"},
                "therapy_mode": False,
                "session_focus": "anxiety",
                "emotional_state": {"descriptors": ["anxious", "worried"], "intensity": "moderate"}
            }
        }

class ChatResponse(BaseModel):
    """Response model for chat messages."""
    response: str = Field(..., description="AI's response to the user")
    conversation_id: Optional[str] = Field(None, description="Conversation ID for memory continuity")
    user_id: str = Field(..., description="User ID for the conversation")
    model_used: Optional[str] = Field(None, description="Model name that was used")
    relevant_memories_count: Optional[int] = Field(None, description="Number of relevant memories used")
    success: bool = Field(True, description="Whether the request was successful")

@router.post("/")
def chat_with_darvis(
    chat_request: ChatMessage,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Engage in intelligent conversation with the Darvis AI assistant powered by LangGraph orchestration.

    This is the core conversational AI endpoint that processes user messages through Darvis's
    sophisticated LangGraph agent system. The AI maintains conversation memory, can access
    tools, and provides personalized responses based on the authenticated user's context.

    **Authentication Required**: Yes (Firebase ID token)

    **Request Body**:
    - **message**: User's message to the AI (1-5000 characters) - **Required**
    - **conversation_id**: Optional ID for conversation continuity and memory
    - **model_name**: Override default model (e.g., "llama3-8b-8192", "llama3-70b-8192")
    - **stream**: Enable streaming response for real-time output (default: false)
    - **context**: Additional conversation context as key-value pairs

    **AI Capabilities**:
    - Multi-turn conversation with memory
    - Tool integration (calendar, email, web search)
    - Personalized responses based on user history
    - Model selection for different use cases
    - Streaming for real-time interaction

    **Returns**:
    - **200 OK**: JSON response with AI message and metadata
    - **200 OK**: Streaming response (text/plain) when stream=true
    - **400 Bad Request**: Empty message or invalid parameters
    - **401 Unauthorized**: Invalid authentication token
    - **500 Internal Server Error**: AI service unavailable

    **Response Format** (non-streaming):
    - AI response text
    - Conversation ID for continuity
    - Model used for the response
    - Number of relevant memories accessed
    - Success status indicator
    """
    try:
        user_id = current_user["uid"]
        user_message = chat_request.message.strip()

        if not user_message:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Message cannot be empty"
            )

        logger.info(f"Processing chat message for user {user_id}, stream={chat_request.stream}, model={chat_request.model_name}")

        # Import the orchestrator here to avoid circular imports
        try:
            sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "darvis-ai-core"))
            from main_orchestrator import darvis_orchestrator
        except ImportError as e:
            logger.error(f"Failed to import orchestrator: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="AI service temporarily unavailable"
            )

        # Handle streaming vs non-streaming
        # Enhance context with therapy mode information
        enhanced_context = chat_request.context or {}
        if chat_request.therapy_mode:
            enhanced_context["therapy_mode"] = True
            if chat_request.session_focus:
                enhanced_context["session_focus"] = chat_request.session_focus
            if chat_request.emotional_state:
                enhanced_context["emotional_state"] = chat_request.emotional_state

        if chat_request.stream:
            return _stream_chat_response(
                darvis_orchestrator=darvis_orchestrator,
                user_message=user_message,
                user_id=user_id,
                conversation_id=chat_request.conversation_id,
                model_name=chat_request.model_name,
                context=enhanced_context
            )
        else:
            return _process_chat_response(
                darvis_orchestrator=darvis_orchestrator,
                user_message=user_message,
                user_id=user_id,
                conversation_id=chat_request.conversation_id,
                model_name=chat_request.model_name,
                context=enhanced_context
            )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while processing your message"
        )

def _process_chat_response(
    darvis_orchestrator,
    user_message: str,
    user_id: str,
    conversation_id: Optional[str],
    model_name: Optional[str],
    context: Optional[Dict[str, Any]]
) -> ChatResponse:
    """Process non-streaming chat response."""
    # Process the message through the LangGraph agent with memory
    result = darvis_orchestrator.process_message(
        user_message=user_message,
        user_id=user_id,
        conversation_id=conversation_id,
        conversation_context=context
    )

    # Extract response data
    ai_response = result.get("response", "I apologize, but I couldn't generate a response.")
    conversation_id = result.get("conversation_id")
    memories_count = result.get("relevant_memories_count", 0)
    success = result.get("success", False)

    if not success and result.get("error"):
        logger.error(f"Error in AI processing: {result['error']}")

    logger.info(f"Generated response for user {user_id}: {len(ai_response)} characters, "
               f"conversation: {conversation_id}, memories: {memories_count}")

    return ChatResponse(
        response=ai_response,
        conversation_id=conversation_id,
        user_id=user_id,
        model_used=model_name,
        relevant_memories_count=memories_count,
        success=success
    )

def _stream_chat_response(
    darvis_orchestrator,
    user_message: str,
    user_id: str,
    conversation_id: Optional[str],
    model_name: Optional[str],
    context: Optional[Dict[str, Any]]
) -> StreamingResponse:
    """Process streaming chat response."""

    def generate_stream():
        """Generator function for streaming response."""
        try:
            for chunk in darvis_orchestrator.stream_message(
                user_message=user_message,
                user_id=user_id,
                conversation_id=conversation_id,
                model_name=model_name,
                conversation_context=context
            ):
                # Format chunk as Server-Sent Events
                chunk_data = json.dumps(chunk)
                yield f"data: {chunk_data}\n\n"

            # Send final completion marker
            yield "data: [DONE]\n\n"

        except Exception as e:
            logger.error(f"Error in streaming response: {e}")
            error_chunk = {
                "type": "error",
                "content": "An error occurred while streaming the response.",
                "error": str(e)
            }
            yield f"data: {json.dumps(error_chunk)}\n\n"
            yield "data: [DONE]\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8"
        }
    )

@router.get("/health", tags=["Health Check"])
def chat_health_check():
    """
    Health check endpoint for the chat service.
    
    Returns the status of the chat service and AI orchestrator.
    """
    try:
        # Try to import and check the orchestrator
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "darvis-ai-core"))
        from main_orchestrator import darvis_orchestrator
        
        if darvis_orchestrator.graph is None:
            return {
                "status": "degraded",
                "message": "Chat service is running but AI orchestrator is not fully initialized",
                "ai_ready": False
            }
        
        return {
            "status": "healthy",
            "message": "Chat service is fully operational",
            "ai_ready": True
        }
        
    except ImportError:
        return {
            "status": "error",
            "message": "AI orchestrator module not available",
            "ai_ready": False
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "error",
            "message": "Health check failed",
            "ai_ready": False
        }
