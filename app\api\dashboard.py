"""
Dashboard API endpoints for <PERSON>vis home screen.

This module provides aggregated data endpoints for the mobile app's home screen,
combining user profile, tasks, notes, and session tracking in optimized single calls.
"""

import logging
from datetime import datetime, timedelta, time
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func

from app.api.auth import get_current_user
from app.db.database import get_db
from app.db import crud
from app.db.models import User, Task, Note, ActionLog

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/dashboard", tags=["Dashboard"])


# Pydantic response models for dashboard data
class UserProfileSummary(BaseModel):
    """User profile data for dashboard"""
    id: str
    email: str
    display_name: Optional[str] = None
    profile_image_url: Optional[str] = None
    created_at: datetime
    member_since_days: int


class TaskSummary(BaseModel):
    """Task summary for dashboard"""
    id: str
    content: str
    is_completed: bool
    due_date: Optional[datetime]
    priority: int
    tags: List[str]
    is_overdue: bool


class NoteSummary(BaseModel):
    """Note summary for dashboard"""
    id: str
    title: Optional[str]
    content: str
    tags: List[str]
    created_at: datetime
    updated_at: Optional[datetime]


class QuickStats(BaseModel):
    """Quick statistics for dashboard"""
    total_tasks: int
    completed_tasks_today: int
    pending_tasks: int
    overdue_tasks: int
    total_notes: int
    notes_created_today: int


class GreetingData(BaseModel):
    """Dynamic greeting data"""
    greeting_message: str
    time_of_day: str  # "morning", "afternoon", "evening", "night"
    is_first_login_today: bool
    login_count_today: int
    current_streak: int


class EventSummary(BaseModel):
    """Event summary for dashboard"""
    id: str
    title: str
    description: Optional[str]
    start_time: datetime
    end_time: Optional[datetime]
    location: Optional[str]
    is_all_day: bool = False


class DetailedTaskBreakdown(BaseModel):
    """Detailed task breakdown for expandable card"""
    high_priority: List[TaskSummary]
    medium_priority: List[TaskSummary]
    low_priority: List[TaskSummary]
    overdue: List[TaskSummary]
    completed_today: List[TaskSummary]


class UpcomingEvents(BaseModel):
    """Upcoming events for expandable card"""
    today: List[EventSummary]
    tomorrow: List[EventSummary]
    this_week: List[EventSummary]


class EnhancedNoteSummary(BaseModel):
    """Enhanced note summary with more details"""
    id: str
    title: Optional[str]
    content: str
    tags: List[str]
    created_at: datetime
    updated_at: Optional[datetime]
    is_pinned: bool = False


class ExpandableCardData(BaseModel):
    """Data for the expandable 'Today's Plate' card"""
    detailed_tasks: DetailedTaskBreakdown
    upcoming_events: UpcomingEvents
    recent_notes: List[EnhancedNoteSummary]
    quick_actions: List[str] = ["Add Task", "Create Note", "Schedule Event"]


class DailySummaryResponse(BaseModel):
    """Complete dashboard response"""
    user_profile: UserProfileSummary
    greeting: GreetingData
    quick_stats: QuickStats
    today_tasks: List[TaskSummary]
    recent_notes: List[NoteSummary]
    expandable_card_data: ExpandableCardData  # New detailed data for expandable card
    generated_at: datetime


def get_time_based_greeting(user_name: Optional[str] = None) -> tuple[str, str]:
    """Generate time-based greeting message"""
    current_hour = datetime.now().hour
    name_part = f", {user_name}" if user_name else ""
    
    if 5 <= current_hour < 12:
        return f"Good morning{name_part}! Ready to tackle the day?", "morning"
    elif 12 <= current_hour < 17:
        return f"Good afternoon{name_part}! How's your day going?", "afternoon"
    elif 17 <= current_hour < 21:
        return f"Good evening{name_part}! Winding down or still productive?", "evening"
    else:
        return f"Good night{name_part}! Late night productivity session?", "night"


def track_user_login(db: Session, user_id: str) -> tuple[bool, int]:
    """
    Track user login and return (is_first_login_today, login_count_today)
    Uses ActionLog table to track login events
    """
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = today_start + timedelta(days=1)
    
    # Check if user has logged in today
    today_logins = db.query(ActionLog).filter(
        and_(
            ActionLog.conversation_id == f"login_{user_id}",  # Use conversation_id for login tracking
            ActionLog.tool_name == "user_login",
            ActionLog.created_at >= today_start,
            ActionLog.created_at < today_end
        )
    ).count()
    
    is_first_login = today_logins == 0
    
    # Log this login event
    login_log = ActionLog(
        tool_name="user_login",
        input_params={"user_id": user_id, "timestamp": datetime.now().isoformat()},
        output_summary="User dashboard access",
        conversation_id=f"login_{user_id}"
    )
    db.add(login_log)
    db.commit()
    
    return is_first_login, today_logins + 1


def get_user_streak(db: Session, user_id: str) -> int:
    """
    Calculate user's current login streak (consecutive days)
    """
    # Get login dates for the last 30 days
    thirty_days_ago = datetime.now() - timedelta(days=30)
    
    login_dates = db.query(
        func.date(ActionLog.created_at).label('login_date')
    ).filter(
        and_(
            ActionLog.conversation_id == f"login_{user_id}",
            ActionLog.tool_name == "user_login",
            ActionLog.created_at >= thirty_days_ago
        )
    ).distinct().order_by(desc('login_date')).all()
    
    if not login_dates:
        return 0
    
    # Calculate consecutive days from today backwards
    streak = 0
    current_date = datetime.now().date()
    
    for login_date_tuple in login_dates:
        login_date = login_date_tuple[0]
        if login_date == current_date or login_date == current_date - timedelta(days=streak):
            streak += 1
            current_date = login_date
        else:
            break
    
    return streak


@router.get("/daily-summary", response_model=DailySummaryResponse)
async def get_daily_summary(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive dashboard data for the home screen.
    
    This endpoint aggregates all necessary data for the mobile app's home screen:
    - User profile information
    - Dynamic time-based greeting
    - Today's tasks (due today + overdue)
    - Recent notes (last 5)
    - Quick statistics
    - Session tracking (first login detection, streak)
    
    Returns optimized data structure for frontend consumption.
    """
    try:
        user_id = current_user["uid"]
        
        # Get user profile data
        db_user = crud.get_user_by_id(db, user_id)
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Calculate member since days
        member_since_days = (datetime.now() - db_user.created_at.replace(tzinfo=None)).days
        
        # Track login and get session data
        is_first_login, login_count = track_user_login(db, user_id)
        current_streak = get_user_streak(db, user_id)
        
        # Generate greeting
        display_name = getattr(db_user, 'display_name', None) or db_user.email.split('@')[0]
        greeting_message, time_of_day = get_time_based_greeting(display_name)
        
        # Get today's date range
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

        # Get tasks due today and overdue tasks (optimized single query)
        today_tasks = crud.get_tasks_due_today(db=db, user_id=user_id, include_overdue=True)

        # Convert to summary format with overdue detection
        all_today_tasks = []
        for task in today_tasks:
            is_overdue = task.due_date and task.due_date < today_start and not task.is_completed
            all_today_tasks.append(TaskSummary(
                id=task.id,
                content=task.content,
                is_completed=task.is_completed,
                due_date=task.due_date,
                priority=task.priority,
                tags=task.tags or [],
                is_overdue=is_overdue
            ))
        
        # Get recent notes (last 5)
        recent_notes = crud.get_user_notes(db=db, user_id=user_id, limit=5)
        notes_summary = [
            NoteSummary(
                id=note.id,
                title=note.title,
                content=note.content[:200] + "..." if len(note.content) > 200 else note.content,
                tags=note.tags or [],
                created_at=note.created_at,
                updated_at=note.updated_at
            )
            for note in recent_notes
        ]
        
        # Calculate quick stats (optimized with count queries)
        total_tasks = crud.count_user_tasks(db=db, user_id=user_id)
        completed_today = len(crud.get_tasks_completed_today(db=db, user_id=user_id))
        pending_tasks = len([t for t in all_today_tasks if not t.is_completed])
        overdue_count = len([t for t in all_today_tasks if t.is_overdue])

        total_notes = crud.count_user_notes(db=db, user_id=user_id)
        notes_today = len(crud.get_notes_created_today(db=db, user_id=user_id))

        # Build detailed task breakdown for expandable card
        detailed_tasks = DetailedTaskBreakdown(
            high_priority=[t for t in all_today_tasks if t.priority >= 2 and not t.is_completed],
            medium_priority=[t for t in all_today_tasks if t.priority == 1 and not t.is_completed],
            low_priority=[t for t in all_today_tasks if t.priority == 0 and not t.is_completed],
            overdue=[t for t in all_today_tasks if t.is_overdue],
            completed_today=[t for t in all_today_tasks if t.is_completed]
        )

        # Build upcoming events (placeholder - would need events model)
        upcoming_events = UpcomingEvents(
            today=[],  # TODO: Implement events model and queries
            tomorrow=[],
            this_week=[]
        )

        # Enhanced notes summary with pinned status
        enhanced_notes = [
            EnhancedNoteSummary(
                id=note.id,
                title=note.title,
                content=note.content[:200] + "..." if len(note.content) > 200 else note.content,
                tags=note.tags or [],
                created_at=note.created_at,
                updated_at=note.updated_at,
                is_pinned=False  # TODO: Add pinned field to Note model
            )
            for note in recent_notes
        ]

        # Build expandable card data
        expandable_card_data = ExpandableCardData(
            detailed_tasks=detailed_tasks,
            upcoming_events=upcoming_events,
            recent_notes=enhanced_notes,
            quick_actions=["Add Task", "Create Note", "Schedule Event", "Start Therapy Session"]
        )

        # Build response
        return DailySummaryResponse(
            user_profile=UserProfileSummary(
                id=db_user.id,
                email=db_user.email,
                display_name=getattr(db_user, 'display_name', None),
                profile_image_url=getattr(db_user, 'profile_image_url', None),
                created_at=db_user.created_at,
                member_since_days=member_since_days
            ),
            greeting=GreetingData(
                greeting_message=greeting_message,
                time_of_day=time_of_day,
                is_first_login_today=is_first_login,
                login_count_today=login_count,
                current_streak=current_streak
            ),
            quick_stats=QuickStats(
                total_tasks=total_tasks,
                completed_tasks_today=completed_today,
                pending_tasks=pending_tasks,
                overdue_tasks=overdue_count,
                total_notes=total_notes,
                notes_created_today=notes_today
            ),
            today_tasks=all_today_tasks[:10],  # Limit to 10 most important tasks
            recent_notes=notes_summary,
            expandable_card_data=expandable_card_data,  # Include detailed breakdown
            generated_at=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Error generating dashboard summary for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate dashboard summary"
        )
