# Darvis Backend Architecture Overview

This document provides a comprehensive guide to understanding the data and logic flow for every major user journey in the Darvis backend system. It serves as the definitive reference for developers seeking to understand the full scope and architecture of the Darvis AI assistant platform.

## System Architecture Principles

Darvis follows a modular, service-oriented architecture with clear separation of concerns:

- **API Layer** (`app/api/`): RESTful endpoints handling HTTP requests and responses
- **Business Logic** (`app/db/crud.py`): Database operations and business rules
- **Data Models** (`app/db/models.py`): SQLAlchemy ORM models defining data structure
- **AI Core** (`darvis-ai-core/`): Isolated AI reasoning and orchestration logic
- **SDK Adapters** (`app/sdk/`): Abstraction layer for external services (LLMs, embeddings, tools)
- **Background Processing** (`app/worker.py`): Celery-based asynchronous task processing

## Core Technologies

- **FastAPI**: Modern Python web framework with automatic API documentation
- **PostgreSQL + pgvector**: Primary database with vector similarity search
- **Redis**: Caching, session management, and Celery message broker
- **LangGraph**: AI agent orchestration and workflow management
- **LiveKit**: Real-time voice communication infrastructure
- **Firebase Auth**: User authentication and authorization
- **Celery**: Distributed task queue for background processing
- **Groq Vision**: Multi-modal AI for image analysis and event extraction
- **Brave Search**: Real-time web search for up-to-date information
- **Voyage AI**: State-of-the-art embeddings for semantic search
- **Therapy Mode**: AI-powered therapeutic support with evidence-based interventions

---

## 1. User Authentication Flow

**Journey**: User signs in via Flutter app → Backend verifies token → User profile management

### Flow Steps:

1. **Client Authentication** (Flutter App)
   - User signs in with email/password or Google OAuth via Firebase Auth
   - Firebase returns ID token to Flutter app
   - Flutter app includes token in `Authorization: Bearer <token>` header

2. **Token Verification** (`app/api/auth.py`)
   - `get_current_user()` dependency extracts Bearer token
   - Firebase Admin SDK verifies token authenticity and expiration
   - Returns decoded user data (UID, email, etc.) or raises 401 Unauthorized

3. **User Registration** (`app/api/users.py` - POST `/api/v1/users/register`)
   - First-time users call registration endpoint
   - Extracts user info from verified Firebase token
   - Creates user record in PostgreSQL via `crud.create_user()`
   - Returns user profile or existing profile if already registered

4. **Profile Access** (`app/api/users.py` - GET `/api/v1/users/profile`)
   - Authenticated requests can retrieve user profile
   - Queries database via `crud.get_user_by_id()`

### Key Files:
- `app/api/auth.py`: Firebase token verification
- `app/api/users.py`: User registration and profile endpoints
- `app/db/models.py`: User model definition
- `app/db/crud.py`: User database operations

---

## 2. Standard API Interaction Flow (Tasks Example)

**Journey**: Client makes API request → Validation → Database operation → Response

### Flow Steps:

1. **Request Processing** (`app/api/tasks.py`)
   - Client sends HTTP request to task endpoint (e.g., POST `/api/v1/tasks/`)
   - FastAPI validates request against Pydantic schema (`app/schemas.py`)
   - Authentication dependency verifies user token

2. **Schema Validation** (`app/schemas.py`)
   - Pydantic models validate input data (TaskCreate, TaskUpdate, etc.)
   - Field validation, type checking, and constraint enforcement
   - Automatic API documentation generation from schema definitions

3. **Business Logic** (`app/db/crud.py`)
   - API endpoint calls appropriate CRUD function
   - Business rules applied (user ownership, data constraints)
   - Database transaction management

4. **Database Operations** (`app/db/models.py`)
   - SQLAlchemy ORM models define table structure
   - Relationships between User, Task, and other entities
   - Automatic timestamp management (created_at, updated_at)

5. **Response Formation**
   - Database results serialized via Pydantic response models
   - FastAPI automatically generates JSON response
   - HTTP status codes and error handling

### Key Files:
- `app/api/tasks.py`: Task management endpoints
- `app/api/contacts.py`: Contact management endpoints
- `app/api/lessons.py`: Lesson management endpoints
- `app/schemas.py`: Request/response validation schemas
- `app/db/crud.py`: Database operations
- `app/db/models.py`: Data model definitions (User, Task, Note, Contact, Lesson, etc.)

---

## 3. Conversational AI Flow (Text Chat)

**Journey**: User message → LangGraph orchestration → AI response with memory and tools

### Flow Steps:

1. **Message Reception** (`app/api/chat.py` - POST `/api/v1/chat/`)
   - Client sends ChatMessage with user input
   - Optional parameters: conversation_id, model_name, stream, context
   - Authentication ensures personalized AI responses

2. **AI Orchestration** (`darvis-ai-core/main_orchestrator.py`)
   - `darvis_orchestrator.process_message()` handles the request
   - LangGraph agent workflow begins with user message analysis
   - **State Management**: ConversationState includes `is_search_enabled` and `current_mode` for dynamic behavior

3. **Mode Detection & PRP Loading** (`darvis-ai-core/main_orchestrator.py`)
   - **General Chat Mode**: Default conversational AI with conditional search integration
   - **Learning Mode**: Educational assistant with "Teach Me Better" loop and adaptive teaching
   - **Mode-Specific PRPs**: Loaded from `prompts/modes/` directory for specialized behaviors

4. **Memory System** (3-Layer Architecture)
   - **Layer 1**: Recent conversation history from current session
   - **Layer 2**: Semantic search across user's message history using pgvector
   - **Layer 3**: User profile and preferences from database
   - Voyage AI embeddings enable semantic memory retrieval

5. **Command Detection** (`darvis-ai-core/main_orchestrator.py`)
   - Checks for user-defined commands via `crud.get_user_command_by_trigger()`
   - Custom trigger phrases can modify AI behavior or execute specific actions

6. **Tool Integration** (Multi-Modal Capabilities)
   - **Calendar Tools**: Composio SDK for Google Calendar, Gmail integration
   - **Search Tools**: Brave Search SDK for real-time web information
   - **Vision Tools**: Groq Vision SDK for image analysis and event extraction
   - Tool selection based on user intent and conversation context

7. **LLM Processing** (`app/sdk/llm_sdk.py`)
   - Groq SDK handles LLM inference with model selection
   - Support for streaming responses via FastAPI StreamingResponse
   - Model swapping capability (llama3-8b-8192, llama3-70b-8192, etc.)

8. **Memory Storage**
   - User and AI messages stored in PostgreSQL with vector embeddings
   - Conversation continuity maintained via conversation_id
   - Semantic search index updated for future retrieval

### Key Files:
- `app/api/chat.py`: Chat endpoint and streaming logic
- `darvis-ai-core/main_orchestrator.py`: LangGraph agent orchestration with mode support
- `darvis-ai-core/prompts/modes/`: Mode-specific PRP definitions
- `app/sdk/llm_sdk.py`: LLM provider abstraction
- `app/sdk/embedding_sdk.py`: Voyage AI embedding integration
- `app/sdk/composio_tools_sdk.py`: External tool integration
- `app/sdk/groq_vision_sdk.py`: Vision processing for image analysis
- `app/sdk/brave_search_sdk.py`: Real-time web search integration
- `app/db/models.py`: Conversation and Message models

---

## 4. Real-Time Voice Flow (LiveKit Integration)

**Journey**: Voice session request → LiveKit room creation → Agent deployment → STT/TTS pipeline

### Flow Steps:

1. **Session Initiation** (`app/api/agent_management.py` - POST `/start_voice_session`)
   - Flutter app requests voice AI session
   - Specifies agent mode (assistant, therapy, focus, etc.)
   - Optional conversation_id for continuity

2. **LiveKit Room Management** (`app/api/agent_management.py`)
   - `LiveKitManager` creates new room via LiveKit Cloud API
   - Generates secure access tokens for user and agent
   - Room configured with appropriate settings for voice communication

3. **Agent Deployment** (Background Process)
   - Python subprocess launches `darvis-ai-core/livekit_agent.py`
   - Agent joins LiveKit room with generated token
   - Environment variables configure STT/TTS providers

4. **Voice Processing Pipeline** (`darvis-ai-core/livekit_agent.py`)
   - **STT (Speech-to-Text)**: Groq Whisper models convert speech to text
   - **AI Processing**: Text routed through main_orchestrator for intelligent response
   - **TTS (Text-to-Speech)**: DeepInfra Kokoro model converts response to speech
   - **Voice Activity Detection**: LiveKit Turn Detector manages conversation flow

5. **Real-Time Communication**
   - WebRTC handles low-latency audio streaming
   - Flutter app connects to LiveKit room for bidirectional audio
   - Agent provides real-time voice responses with conversation memory

6. **Session Management**
   - Background task monitoring for agent health
   - Automatic cleanup of completed sessions
   - Integration with text chat memory system

### Key Files:
- `app/api/agent_management.py`: Voice session management
- `darvis-ai-core/livekit_agent.py`: LiveKit agent implementation
- `darvis-ai-core/main_orchestrator.py`: AI logic shared with text chat

---

## 5. Frictionless Inbox Flow (Asynchronous Content Processing)

**Journey**: URL submission → Background processing → Content extraction → AI summarization

### Flow Steps:

1. **URL Capture** (`app/api/inbox.py` - POST `/api/v1/inbox/capture`)
   - User submits URL for processing via InboxItemCreate schema
   - Immediate response with "processing" status
   - Background task queued for actual content processing

2. **Celery Task Dispatch** (`app/worker.py`)
   - `process_inbox_item.delay()` queues background task
   - Redis serves as message broker for task distribution
   - Non-blocking response allows user to continue other activities

3. **Content Extraction** (Background Task)
   - Jina AI Reader API (`https://r.jina.ai/{url}`) extracts clean content
   - Handles various content types (articles, PDFs, web pages)
   - Robust error handling for inaccessible or malformed URLs

4. **AI Summarization** (Background Task)
   - Extracted content processed through LLM via `llm_sdk.py`
   - Intelligent summarization preserving key insights
   - Summary stored in database with original content

5. **Status Updates** (`app/api/inbox.py` - GET `/api/v1/inbox/`)
   - Users can check processing status via list endpoint
   - Real-time status updates: "processing" → "completed" → "failed"
   - Pagination and filtering support for large inbox collections

6. **Integration with AI Memory**
   - Processed content becomes part of user's knowledge base
   - Summaries searchable via semantic similarity
   - AI can reference inbox content in future conversations

### Key Files:
- `app/api/inbox.py`: Inbox management endpoints
- `app/worker.py`: Celery background task processing
- `app/db/models.py`: InboxItem model
- `app/sdk/llm_sdk.py`: AI summarization logic

---

## 6. Productivity Management Flows

### Contact Management System
**Journey**: Contact creation → AI-enhanced relationship tracking → Memory integration

#### Flow Steps:
1. **Contact Creation** (`app/api/contacts.py` - POST `/api/v1/contacts/`)
   - User creates contact with name, phone, meeting context, social media handles
   - **Memory Prompt**: AI-specific field for relationship context and interaction history
   - Automatic user ownership and timestamp tracking

2. **Contact Retrieval** (`app/api/contacts.py` - GET `/api/v1/contacts/`)
   - Search functionality across name, phone, meeting context, and memory prompts
   - Pagination and filtering for large contact lists
   - Alphabetical sorting for easy navigation

3. **AI Integration**
   - Contact memory prompts enhance AI's understanding of relationships
   - Personalized responses based on contact context
   - Integration with calendar tools for meeting scheduling

#### Key Files:
- `app/api/contacts.py`: Contact CRUD endpoints
- `app/db/models.py`: Contact model with social_media JSON field
- `app/schemas.py`: ContactCreate, ContactUpdate schemas with examples

### Lesson Learning System
**Journey**: Experience capture → Reflection documentation → Personal knowledge base

#### Flow Steps:
1. **Lesson Creation** (`app/api/lessons.py` - POST `/api/v1/lessons/`)
   - User documents scenario, choice made, and lesson learned
   - Structured reflection for personal growth tracking
   - Searchable knowledge base for future reference

2. **Lesson Retrieval** (`app/api/lessons.py` - GET `/api/v1/lessons/`)
   - Full-text search across scenario, choice, and lesson content
   - Chronological ordering (most recent first)
   - Pagination for large lesson collections

3. **AI Integration**
   - Lessons inform AI's understanding of user's decision-making patterns
   - Contextual advice based on past experiences
   - Personal growth insights and pattern recognition

#### Key Files:
- `app/api/lessons.py`: Lesson CRUD endpoints
- `app/db/models.py`: Lesson model with scenario/choice/lesson fields
- `app/schemas.py`: LessonCreate, LessonUpdate schemas with examples

---

## 7. Multi-Modal AI Processing Flow

### Image-to-Event Extraction
**Journey**: Image upload → Vision analysis → Structured event data → Calendar integration

#### Flow Steps:
1. **Image Analysis Trigger** (`darvis-ai-core/main_orchestrator.py`)
   - User uploads image with event-related query
   - Tool detection identifies image-to-event intent
   - PRP loading from `prompts/tools/image_to_event.md`

2. **Vision Processing** (`app/sdk/groq_vision_sdk.py`)
   - Groq Vision API (meta-llama-4-scout-17b-16e-instruct) analyzes image
   - Text transcription and event detail extraction
   - JSON validation for event_name, event_date, event_location

3. **Structured Output**
   - Success: Returns formatted event details for calendar creation
   - Error: Provides specific guidance for image quality or content issues
   - Integration with create_calendar_event tool for seamless workflow

#### Key Files:
- `app/sdk/groq_vision_sdk.py`: Vision processing SDK
- `darvis-ai-core/prompts/tools/image_to_event.md`: Tool-specific PRP
- `darvis-ai-core/main_orchestrator.py`: Tool integration and execution

### Real-Time Search Integration
**Journey**: Information request → Search execution → Context integration → Enhanced response

#### Flow Steps:
1. **Search Intent Detection** (`darvis-ai-core/main_orchestrator.py`)
   - Keywords trigger search intent when `is_search_enabled=True`
   - Mode-specific search integration rules
   - Conditional tool activation based on conversation state

2. **Web Search Execution** (`app/sdk/brave_search_sdk.py`)
   - Brave Search API integration for current information
   - Query formulation and result formatting
   - Error handling and fallback responses

3. **Context Integration**
   - Search results formatted for AI consumption
   - Combined with existing knowledge for comprehensive responses
   - Source attribution and recency indicators

#### Key Files:
- `app/sdk/brave_search_sdk.py`: Web search SDK
- `darvis-ai-core/prompts/modes/general_chat.md`: Search integration rules
- `darvis-ai-core/main_orchestrator.py`: Conditional search execution

---

## 8. Advanced AI Personality Modes

### Learning Mode
**Journey**: Educational request → Adaptive teaching → "Teach Me Better" loop → Knowledge mastery

#### Flow Steps:
1. **Mode Activation** (`darvis-ai-core/main_orchestrator.py`)
   - `current_mode="learning"` triggers educational assistant
   - PRP loading from `prompts/modes/learning_mode.md`
   - Specialized teaching persona and methodologies

2. **Adaptive Teaching** (Learning Mode PRP)
   - Initial knowledge assessment and learning goal identification
   - Multiple teaching approaches (visual, auditory, kinesthetic)
   - Scaffolding techniques for progressive complexity

3. **"Teach Me Better" Loop**
   - User explains concept in their own words
   - AI provides constructive feedback and corrections
   - Iterative refinement until mastery achieved
   - Encouragement and progress tracking

#### Key Features:
- **ELI5 Support**: Simple explanations with analogies
- **Active Learning**: User-driven explanation and feedback
- **Personalization**: Adaptation to learning styles and pace
- **Progress Tracking**: Recognition of improvement and mastery

### Enhanced General Chat Mode
**Journey**: General inquiry → Mode-specific processing → Contextual response with optional search

#### Flow Steps:
1. **Mode Processing** (`darvis-ai-core/main_orchestrator.py`)
   - Default `current_mode="general"` for standard conversations
   - PRP loading from `prompts/modes/general_chat.md`
   - Search integration based on `is_search_enabled` state

2. **Conditional Search Integration**
   - Real-time information requests trigger web search
   - Search results integrated with existing knowledge
   - Source attribution and recency indicators

3. **Contextual Response Generation**
   - Memory integration for personalized responses
   - Tool integration for actionable assistance
   - Adaptive communication style

#### Key Files:
- `darvis-ai-core/prompts/modes/learning_mode.md`: Educational assistant PRP
- `darvis-ai-core/prompts/modes/general_chat.md`: Enhanced general conversation PRP
- `darvis-ai-core/main_orchestrator.py`: Mode detection and PRP integration

---

## 9. User Customization Flows

### Commands System
- **Purpose**: User-defined trigger phrases for custom AI behaviors
- **Flow**: `app/api/commands.py` → `app/db/crud.py` → Integration in `main_orchestrator.py`
- **Features**: Conflict detection, trigger phrase lookup, personalized AI modes

### Pronunciations System
- **Purpose**: Custom TTS pronunciation for specific words
- **Flow**: `app/api/pronunciations.py` → Database storage → TTS integration
- **Features**: Phonetic spelling, SSML support, word-based lookup dictionary

---

## Data Flow Summary

1. **Authentication**: Firebase → Backend verification → User context
2. **API Requests**: Client → FastAPI → Pydantic validation → Business logic → Database
3. **AI Processing**: User input → LangGraph → Memory retrieval → Mode detection → Tool integration → LLM → Response
4. **Voice AI**: LiveKit room → Agent deployment → STT → AI processing → TTS → Audio response
5. **Background Tasks**: URL submission → Celery queue → Content extraction → AI processing → Storage
6. **Memory System**: All interactions → Vector embeddings → Semantic search → Personalized responses
7. **Productivity Management**: Contact/Lesson creation → Database storage → AI context enhancement
8. **Multi-Modal Processing**: Image upload → Vision analysis → Structured extraction → Tool integration
9. **Real-Time Search**: Information request → Web search → Context integration → Enhanced response
10. **Adaptive AI Modes**: Mode detection → PRP loading → Specialized behavior → Contextual responses
11. **Intelligent Notifications**: User behavior analysis → Relevance scoring → Smart delivery → Engagement tracking

## Advanced Capabilities Summary

### Multi-Modal AI
- **Vision Processing**: Image analysis for event extraction and structured data output
- **Text Processing**: Natural language understanding with context and memory
- **Voice Processing**: Real-time speech-to-text and text-to-speech integration

### Intelligent Tool Integration
- **Calendar Management**: Event creation and scheduling assistance
- **Web Search**: Real-time information retrieval and context integration
- **Vision Analysis**: Image-to-event extraction with JSON validation
- **Content Processing**: URL summarization and knowledge base integration
- **Smart Notifications**: Behavioral analysis and contextual push notifications

### Adaptive Personality System
- **Mode-Based Behavior**: Learning, general chat, and custom modes
- **Context-Aware Responses**: Memory integration and personalization
- **Dynamic Tool Selection**: Conditional tool usage based on conversation state
- **User Customization**: Commands, pronunciations, and preference learning

### Productivity Enhancement
- **Relationship Management**: AI-enhanced contact tracking with memory prompts
- **Knowledge Capture**: Lesson learning system for personal growth
- **Experience Documentation**: Structured reflection and insight generation
- **Personal Knowledge Base**: Searchable repository of experiences and contacts

---

## 8. Intelligent Notification Flow

**Journey**: User behavior analysis → Smart notification generation → Contextual delivery → Engagement tracking

### Flow Steps:

1. **Behavior Analysis** (Background Processing)
   - Celery tasks analyze user activity patterns (task completion, conversation frequency, voice session timing)
   - Identify optimal notification windows and user engagement preferences
   - Track notification interaction history for relevance scoring

2. **Smart Notification Generation** (`app/worker.py` - Periodic Tasks)
   - **Task Reminders**: Analyze due tasks with priority-based relevance scoring
   - **Inbox Completion**: Trigger notifications when content processing completes
   - **Productivity Insights**: Daily analysis of task/note patterns for actionable suggestions
   - **Conversation Continuity**: Detect interrupted conversations and offer resumption
   - **Social Context**: Pre-meeting reminders with contact context from memory prompts

3. **Relevance Scoring Engine** (`app/worker.py` - Intelligence Logic)
   - Multi-factor scoring: Priority weight + Urgency factor + User engagement history
   - Dynamic threshold adjustment based on user feedback and interaction patterns
   - Anti-fatigue protection with daily notification budgets and exponential backoff

4. **Contextual Delivery** (`app/sdk/notification_delivery_sdk.py`)
   - Firebase Cloud Messaging integration with proper error handling
   - State-aware delivery (respects quiet hours, active voice sessions, conversation flow)
   - Batch processing for efficiency with comprehensive response tracking

5. **User Preference Management** (`app/api/notifications.py`)
   - Granular controls for notification types, timing, and frequency
   - FCM token management for secure push notification delivery
   - Real-time preference updates with immediate effect

6. **Engagement Tracking & Analytics** (`app/db/crud.py` - Analytics Functions)
   - Track notification clicks, dismissals, and user actions
   - Calculate engagement metrics (click rates, dismiss rates) by notification type
   - Continuous learning to improve relevance scoring and timing

### Key Features:

- **Behavioral Intelligence**: Learns user active hours and engagement patterns
- **Anti-Fatigue Design**: Daily limits, quiet hours, and relevance thresholds
- **Contextual Awareness**: Integrates with task priorities, conversation state, and voice sessions
- **User Control**: Comprehensive preference management with granular settings
- **Performance Analytics**: Detailed engagement tracking for continuous improvement

### Key Files:
- `app/api/notifications.py`: Notification management endpoints and user preferences
- `app/worker.py`: Smart notification generation and periodic analysis tasks
- `app/sdk/notification_delivery_sdk.py`: Firebase Cloud Messaging integration
- `app/db/models.py`: NotificationPreference and NotificationHistory models
- `app/db/crud.py`: Notification CRUD operations and analytics functions

---

## 11. Therapy Mode Flow

**Journey**: User enters therapy mode → AI provides therapeutic support → Progress tracking and insights

### Flow Steps:

1. **Therapy Mode Activation** (`app/api/chat.py`)
   - User sends message with `therapy_mode: true` flag
   - Chat API enhances context with therapy-specific data
   - Request routed to enhanced orchestrator with therapy context

2. **Therapy Context Retrieval** (`darvis-ai-core/main_orchestrator.py`)
   - `_therapy_mode_check_node` detects therapy mode activation
   - `_therapy_context_retrieval_node` retrieves relevant therapy content
   - Emotional state detection from user message
   - Therapy session creation or continuation via `crud.create_therapy_session()`

3. **Intelligent Content Retrieval** (`app/sdk/therapy_retrieval_sdk.py`)
   - `TherapyRetrievalSDK.retrieve_therapy_content()` performs vector similarity search
   - Filters by therapy technique and emotional themes
   - Re-ranks results based on user's historical intervention effectiveness
   - Caches results in Redis for optimal latency (<300ms response time)

4. **Therapeutic Response Generation** (`darvis-ai-core/main_orchestrator.py`)
   - `_build_therapy_system_prompt()` creates therapy-specific system prompt
   - Incorporates retrieved therapy knowledge and personalized interventions
   - AI follows Listen → Guide → Offer Tools therapeutic conversation flow
   - Streaming response with immediate empathetic acknowledgment

5. **Session Management** (`darvis-ai-core/main_orchestrator.py`)
   - `_therapy_session_management_node` tracks session progress
   - Extracts therapeutic insights and practice tasks from conversation
   - Updates emotional state progression throughout session
   - Handles graceful session conclusion with summary generation

6. **Progress Tracking** (`app/api/therapy.py`)
   - POST `/api/v1/therapy/progress` for mood entries and task completion
   - GET `/api/v1/therapy/analytics` for empowerment dashboard data
   - `crud.get_therapy_progress_analytics()` calculates trends and effectiveness
   - Real-time progress visualization and intervention optimization

7. **Therapy Notifications** (`app/worker.py`)
   - `send_therapy_session_reminder()` for gentle re-engagement
   - `send_therapy_practice_followup()` for practice task support
   - `send_therapy_progress_celebration()` for milestone recognition
   - Emotional state-aware timing and frequency controls

### Key Components:
- `app/db/models.py`: `TherapyKnowledgeChunk`, `TherapySession`, `TherapyProgress` models
- `app/sdk/therapy_retrieval_sdk.py`: Intelligent therapy content retrieval with personalization
- `darvis-ai-core/main_orchestrator.py`: Therapy-specific orchestration nodes
- `app/api/therapy.py`: REST API for therapy session and progress management
- `app/db/crud.py`: Therapy CRUD operations with analytics and effectiveness tracking

---

This architecture ensures scalability, maintainability, and sophisticated AI experiences across all Darvis features, supporting multi-modal interactions, adaptive behavior, comprehensive productivity management, intelligent notification delivery, and evidence-based therapeutic support with seamless voice-chat integration.
